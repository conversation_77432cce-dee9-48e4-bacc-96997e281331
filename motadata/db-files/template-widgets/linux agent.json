[{"_type": "0", "id": 10000000001208, "visualization.name": "CPU (%)", "visualization.description": "CPU Utilization Server Category", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.cpu.user.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.cpu.interrupt.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "system.cpu.percent"}, {"type": "metric", "data.point": "system.cpu.idle.percent"}, {"type": "metric", "data.point": "system.cpu.interrupt.percent"}, {"type": "metric", "data.point": "system.cpu.system.percent"}, {"type": "metric", "data.point": "system.cpu.user.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "CPU", "style": {"font.size": "medium"}, "data.points": [{"label": "User", "value": "system.cpu.user.percent.last"}, {"label": "Interrupt", "value": "system.cpu.interrupt.percent.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "system.cpu.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "cpu"}, "color.data.point": "system.cpu.percent"}}}}, {"_type": "0", "id": 10000000001209, "visualization.name": "Memory (%)", "visualization.description": "Memory Utilization Server Category", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.memory.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.memory.installed.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.memory.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "system.memory.used.percent"}, {"type": "metric", "data.point": "system.memory.free.percent"}, {"type": "metric", "data.point": "system.memory.free.bytes"}, {"type": "metric", "data.point": "system.cache.memory.bytes"}, {"type": "metric", "data.point": "system.paged.memory.bytes"}, {"type": "metric", "data.point": "system.memory.used.bytes"}, {"type": "metric", "data.point": "system.memory.installed.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Memory", "style": {"font.size": "medium"}, "data.points": [{"label": "Total", "value": "system.memory.installed.bytes.last"}, {"label": "Free", "value": "system.memory.free.bytes.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "system.memory.used.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "memory"}, "color.data.point": "system.memory.used.percent"}}}}, {"_type": "0", "id": 10000000001210, "visualization.name": "Disk (%) ", "visualization.description": "Disk Utilization Server Category", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.disk.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.disk.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.disk.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "system.disk.used.percent"}, {"type": "metric", "data.point": "system.disk.free.percent"}, {"type": "metric", "data.point": "system.disk.free.bytes"}, {"type": "metric", "data.point": "system.disk.io.time.percent"}, {"type": "metric", "data.point": "system.disk.capacity.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Disk", "style": {"font.size": "medium"}, "data.points": [{"label": "Total", "value": "system.disk.capacity.bytes.last"}, {"label": "Free", "value": "system.disk.free.bytes.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "system.disk.used.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "disk"}, "color.data.point": "system.disk.used.percent"}}}}, {"_type": "0", "id": 10000000001211, "visualization.name": "Load Average", "visualization.description": "System Load Average", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.load.avg1.min", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.load.avg5.min", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.load.avg15.min", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "system.load.avg1.min"}, {"type": "metric", "data.point": "system.load.avg5.min"}, {"type": "metric", "data.point": "system.load.avg15.min"}]}], "visualization.properties": {"gauge": {"header": {"title": "Load Average", "style": {"font.size": "medium"}, "data.points": [{"label": "Load Avg. 1 min", "value": "system.load.avg1.min.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "5 Min", "value": "system.load.avg5.min.last"}, {"label": "15 Min", "value": "system.load.avg15.min.last"}]}, "style": {"icon": {"name": "chart-line"}, "color.data.point": "system.load.avg1.min"}}}}, {"_type": "0", "id": 10000000001212, "visualization.name": "System Network", "visualization.description": "System Network In/Out Server Category", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.network.in.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.network.out.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.network.tcp.retransmissions", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "system.network.tcp.connections"}, {"type": "metric", "data.point": "system.network.output.queue.length"}, {"type": "metric", "data.point": "system.network.bytes.per.sec"}, {"type": "metric", "data.point": "system.network.in.bytes.per.sec"}, {"type": "metric", "data.point": "system.network.out.bytes.per.sec"}, {"type": "metric", "data.point": "system.network.tcp.retransmissions"}]}], "visualization.properties": {"gauge": {"header": {"title": "Network", "style": {"font.size": "medium"}, "data.points": [{"label": "Retransmissions", "value": "system.network.tcp.retransmissions.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "IN", "value": "system.network.in.bytes.per.sec.last"}, {"label": "OUT", "value": "system.network.out.bytes.per.sec.last"}]}, "style": {"icon": {"name": "network"}, "color.data.point": "system.network.tcp.retransmissions"}}}}, {"_type": "0", "id": 10000000001213, "visualization.name": "Response Time", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ping.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ping.lost.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ping.latency.ms"}, {"type": "metric", "data.point": "ping.max.latency.ms"}, {"type": "metric", "data.point": "ping.min.latency.ms"}, {"type": "metric", "data.point": "ping.lost.packets"}]}], "visualization.properties": {"gauge": {"header": {"title": "Response Time", "style": {"font.size": "medium"}, "data.points": [{"label": "Lost Packets", "value": "ping.lost.packets.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "ping.latency.ms.last"}]}, "style": {"icon": {"name": "response-time"}, "color.data.point": "ping.latency.ms.last"}}}}, {"_type": "0", "id": 10000000001214, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Monitor Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000001215, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000001216, "visualization.name": "System Disk Utilization", "visualization.description": "System Disk Server Category", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.disk.volume~capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.disk.volume~free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.disk.volume~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.disk.volume~mount.path", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"view": "Storage Details", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "system.disk.volume", "alias": "system.disk.volume", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "system.disk.volume~capacity.bytes.last", "alias": "system.disk.volume~capacity.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "system.disk.volume~free.bytes.last", "alias": "system.disk.volume~free.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "system.disk.volume~used.bytes.last", "alias": "system.disk.volume~used.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000001217, "visualization.name": "CPU Details", "visualization.description": "System CPU Details Server Category", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.cpu.idle.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.cpu.interrupt.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.cpu.user.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001218, "visualization.name": "Memory Details", "visualization.description": "System Memory Details Server Category", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.memory.committed.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.cache.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.memory.installed.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.memory.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001219, "visualization.name": "Swap Memory", "visualization.description": "Swap Memory Linux Server", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.swap.memory.provisioned.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.swap.memory.free.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.swap.memory.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001220, "visualization.name": "Load Average", "visualization.description": "Load Average Linux Server", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.load.avg1.min", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.load.avg5.min", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.load.avg15.min", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001221, "visualization.name": "Interface Details", "visualization.description": "Interface Details Server Category", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.network.interface~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.network.interface~bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.network.interface~in.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.network.interface~out.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "system.network.interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "system.network.interface~in.bytes.per.sec.avg", "title": "In Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"icon": {"name": "long-arrow-right", "placement": "prefix"}}}, {"name": "system.network.interface~out.bytes.per.sec.avg", "title": "Out Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"icon": {"name": "long-arrow-left", "placement": "prefix"}}}, {"name": "system.network.interface~bytes.per.sec.avg", "title": "Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"icon": {"name": "traffic", "placement": "prefix"}}}, {"name": "system.network.interface~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}]}}, "visualization.empty.view": "no"}, {"_type": "0", "id": 10000000001222, "visualization.name": "Agent Process Details", "visualization.description": "Agent Process Details Server Category", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~memory.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~threads", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "system.process", "title": "Process", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "process", "style": {}}, {"name": "system.process~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status"}, {"name": "system.process~cpu.percent.last", "title": "CPU", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "gauge"}}}, {"name": "system.process~memory.used.percent.last", "title": "Memory", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "alignment": "center", "style": {}}, {"name": "system.process~threads.last", "title": "Threads", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}, "visualization.empty.view": "no"}, {"_type": "0", "id": 10000000001207, "visualization.name": "Application Status", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Application Status", "visualization.data.sources": [{"type": "availability", "object.type": "system.process", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "application", "aggregator": "last"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "instance", "title": "Instance", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "type", "title": "Application Name", "cellRender": "application_name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "status", "title": "Status", "cellRender": "status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}]}}, "visualization.result.by": ["monitor"], "visualization.empty.view": "no"}]