[{"_type": "0", "id": 10000000004237, "visualization.name": "Sessions", "visualization.description": "Oracle Sessions", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.rac.cluster.active.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.waiting.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.blocking.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "oracle.rac.cluster.active.sessions"}, {"type": "metric", "data.point": "oracle.rac.cluster.waiting.sessions"}, {"type": "metric", "data.point": "oracle.rac.cluster.blocking.sessions"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "sessions", "placement": "prefix"}}, "header": {"title": "Sessions", "style": {"font.size": "medium"}, "data.points": [{"label": "Waiting", "value": "oracle.rac.cluster.waiting.sessions.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Active", "value": "oracle.rac.cluster.active.sessions.last"}, {"label": "Blocked", "value": "oracle.rac.cluster.blocking.sessions.last"}]}}}}, {"_type": "0", "id": 10000000004238, "visualization.name": "<PERSON><PERSON>", "visualization.description": "Oracle Cache", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.rac.cluster.buffer.cache.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.library.cache.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.cursor.hit.ratio.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "oracle.rac.cluster.library.cache.used.bytes"}, {"type": "metric", "data.point": "oracle.rac.cluster.buffer.cache.used.bytes"}, {"type": "metric", "data.point": "oracle.rac.cluster.data.dictionary.cache.hit.ratio.percent"}, {"type": "metric", "data.point": "oracle.rac.cluster.sql.cache.hit.ratio.percent"}, {"type": "metric", "data.point": "oracle.rac.cluster.cursor.miss.ratio.percent"}, {"type": "metric", "data.point": "oracle.rac.cluster.cursor.hit.ratio.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "cache", "placement": "prefix"}}, "header": {"title": "<PERSON><PERSON>", "style": {"font.size": "medium"}, "data.points": [{"label": "Cursor Hit Ratio", "value": "oracle.rac.cluster.cursor.hit.ratio.percent.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "<PERSON><PERSON><PERSON>", "value": "oracle.rac.cluster.buffer.cache.used.bytes.last"}, {"label": "Library", "value": "oracle.rac.cluster.library.cache.used.bytes.last"}]}}}}, {"_type": "0", "id": 10000000004239, "visualization.name": "PGA", "visualization.description": "Oracle PGA", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.rac.cluster.pga.allocated.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.pga.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.pga.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "oracle.rac.cluster.pga.processes"}, {"type": "metric", "data.point": "oracle.rac.cluster.pga.max.processes"}, {"type": "metric", "data.point": "oracle.rac.cluster.pga.cache.hit.ratio.percent"}, {"type": "metric", "data.point": "oracle.rac.cluster.pga.used.percent"}, {"type": "metric", "data.point": "oracle.rac.cluster.pga.allocated.bytes"}, {"type": "metric", "data.point": "oracle.rac.cluster.pga.used.bytes"}, {"type": "metric", "data.point": "oracle.rac.cluster.pga.free.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "PGA", "style": {"font.size": "medium"}, "data.points": [{"label": "Allocated Bytes", "value": "oracle.rac.cluster.pga.allocated.bytes.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Used", "value": "oracle.rac.cluster.pga.used.bytes.last"}, {"label": "Free", "value": "oracle.rac.cluster.pga.free.bytes.last"}]}}}}, {"_type": "0", "id": 10000000004240, "visualization.name": "Transactions", "visualization.description": "Oracle Transactions", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.rac.cluster.enqueue.waits", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.user.commits", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.user.rollbacks", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "oracle.rac.cluster.session.commits"}, {"type": "metric", "data.point": "oracle.rac.cluster.user.calls"}, {"type": "metric", "data.point": "oracle.rac.cluster.enqueue.timeouts"}, {"type": "metric", "data.point": "oracle.rac.cluster.enqueue.deadlocks"}, {"type": "metric", "data.point": "oracle.rac.cluster.enqueue.requests"}, {"type": "metric", "data.point": "oracle.rac.cluster.enqueue.waits"}, {"type": "metric", "data.point": "oracle.rac.cluster.user.commits"}, {"type": "metric", "data.point": "oracle.rac.cluster.user.rollbacks"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "transactions", "placement": "prefix"}}, "header": {"title": "Transactions", "style": {"font.size": "medium"}, "data.points": [{"label": "Enqueue Waits", "value": "oracle.rac.cluster.enqueue.waits.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Commits", "value": "oracle.rac.cluster.user.commits.last"}, {"label": "Rollbacks", "value": "oracle.rac.cluster.user.rollbacks.last"}]}}}}, {"_type": "0", "id": 10000000004241, "visualization.name": "Database I/O", "visualization.description": "Oracle Database I/O", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.rac.cluster.database.physical.reads", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.database.physical.writes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.database.allocated.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "oracle.rac.cluster.avg.executions"}, {"type": "metric", "data.point": "oracle.rac.cluster.consistent.gets"}, {"type": "metric", "data.point": "oracle.rac.cluster.database.block.gets"}, {"type": "metric", "data.point": "oracle.rac.cluster.database.block.used.bytes"}, {"type": "metric", "data.point": "oracle.rac.cluster.database.physical.reads"}, {"type": "metric", "data.point": "oracle.rac.cluster.database.physical.writes"}, {"type": "metric", "data.point": "oracle.rac.cluster.database.allocated.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "database", "placement": "prefix"}}, "header": {"title": "Database I/O", "style": {"font.size": "medium"}, "data.points": [{"label": "Database Size", "value": "oracle.rac.cluster.database.allocated.bytes.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "oracle.rac.cluster.database.physical.reads.last"}, {"label": "Write", "value": "oracle.rac.cluster.database.physical.writes.last"}]}}}}, {"_type": "0", "id": 10000000004242, "visualization.name": "SGA", "visualization.description": "Oracle SGA", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.rac.cluster.sga.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.sga.memory.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.sga.memory.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "oracle.rac.cluster.fixed.sga.used.bytes"}, {"type": "metric", "data.point": "oracle.rac.cluster.sga.memory.free.bytes"}, {"type": "metric", "data.point": "oracle.rac.cluster.shared.io.pool.used.bytes"}, {"type": "metric", "data.point": "oracle.rac.cluster.shared.pool.used.bytes"}, {"type": "metric", "data.point": "oracle.rac.cluster.sql.area.used.bytes"}, {"type": "metric", "data.point": "oracle.rac.cluster.sga.used.percent"}, {"type": "metric", "data.point": "oracle.rac.cluster.sga.memory.used.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "SGA", "style": {"font.size": "medium"}, "data.points": [{"label": "Used", "value": "oracle.rac.cluster.sga.used.percent.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Used", "value": "oracle.rac.cluster.sga.memory.used.bytes.last"}, {"label": "Free", "value": "oracle.rac.cluster.sga.memory.free.bytes.last"}]}}}}, {"_type": "0", "id": 10000000004243, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000004244, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000004245, "visualization.name": "Pluggable Database(PDB) Details", "visualization.description": "Pluggable Database(PDB) Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.rac.cluster.pluggable.database", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.pluggable.database~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.pluggable.database~restriction.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.pluggable.database~created.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.pluggable.database~container.id", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.rac.cluster.pluggable.database", "title": "NAME", "show": "Yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.rac.cluster.pluggable.database~status.last", "title": "STATUS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.rac.cluster.pluggable.database~restriction.status.last", "title": "RESTRICTED", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.rac.cluster.pluggable.database~created.time.last", "title": "CREATION TIME", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.rac.cluster.pluggable.database~container.id", "title": "CONTAINER ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}, "visualization.empty.view": "no"}, {"_type": "0", "id": 10000000004246, "visualization.name": "Instance Details", "visualization.description": "Instance Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.rac.cluster.instance~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.instance~node", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.instance~sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.instance~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.instance~uptime", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.rac.cluster.instance", "title": "", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.rac.cluster.instance~name.last", "title": "INSTANCE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.rac.cluster.instance~node.last", "title": "NODE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.rac.cluster.instance~sessions.last", "title": "NUMBER OF SESSIONS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.rac.cluster.instance~status.last", "title": "STATUS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "oracle.rac.cluster.instance~uptime.last", "title": "Connected time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}, "visualization.empty.view": "no"}, {"_type": "0", "id": 10000000004247, "visualization.name": "Automatic Storage Management (ASM) Details", "visualization.description": "Oracle RAC ASM Disk Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "oracle.rac.cluster.asm.disk.group~number", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.asm.disk.group~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.asm.disk.group~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.asm.disk.group~provisioned.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.asm.disk.group~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "oracle.rac.cluster.asm.disk.group~free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "oracle.rac.cluster.asm.disk.group~number.last", "title": "Number", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "oracle.rac.cluster.asm.disk.group", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "oracle.rac.cluster.asm.disk.group~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "oracle.rac.cluster.asm.disk.group~state.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "oracle.rac.cluster.asm.disk.group~provisioned.bytes.last", "title": "Total Memory", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "oracle.rac.cluster.asm.disk.group~used.bytes.last", "title": "Used Memory", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "oracle.rac.cluster.asm.disk.group~free.bytes.last", "title": "Free Memory", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}]}}, "visualization.empty.view": "no"}]