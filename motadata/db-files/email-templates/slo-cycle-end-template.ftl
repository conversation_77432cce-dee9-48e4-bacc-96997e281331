<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>SLO Completion Report</title>
</head>

<body bgcolor="#F3F6F8" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0">
<table width="100%" bgcolor="#F3F6F8" border="0" cellpadding="0" cellspacing="0" align="center" style="border-collapse: collapse; font-family: Arial, Helvetica, sans-serif; background:#F3F6F8;">
    <tr style="margin:0; padding:0;"><td style="margin:0; padding:0;" height="40"></td></tr>
    <#include "rebranding-header.ftl">
    <tr style="margin:0; padding:0;"><td style="margin:0; padding:0;" height="24"></td></tr>
    <tr style="margin:0; padding:0;">
        <td style="margin:0; padding:0;" valign="top" align="center">
            <table width="600" border="0" cellspacing="0" cellpadding="0" bgcolor="#ffffff" style="padding:0 24px; border-top: 4px solid #009DDC; background:#fff;">
                <tbody>
                <tr style="margin:0; padding:0;"><td style="margin:0; padding:0;" height="24"></td></tr>
                <tr style="margin:0; padding:0;"><td style="margin:0; padding:0; text-align: center;"><img width="36" height="36" style="width:36px; height:36px;" src="cid:information.png" alt="Completion"/></td></tr>
                <tr style="margin:0; padding:0;"><td style="margin:0; padding:0;" height="10"></td></tr>
                <tr style="margin:0; padding:0;">
                    <td style="margin:0; padding:0; text-align: center;">
                        <h1 style="font-size: 16px; color:#364658; font-family: Arial, Helvetica, sans-serif; margin:0; padding:0 0 5px;">
                            <#if .data_model["slo.frequency"]??>${.data_model["slo.frequency"]}</#if> Report - <#if .data_model["slo.profile.name"]??>${.data_model["slo.profile.name"]}</#if>
                        </h1>
                        <p style="color:#9BA3AB; font-size:12px; text-align:center; margin:0; padding:3px 0 0 0; font-weight:bold;">
                            <#if .data_model["timestamp"]??>${.data_model["timestamp"]}</#if>
                        </p>
                    </td>
                </tr>
                <tr style="margin:0; padding:0;"><td style="margin:0; padding:0;" height="32"></td></tr>
                <tr style="margin:0; padding:0;"><td style="margin:0; padding:0;"><hr style="border: 1px solid #EEF2F6; margin:0; padding:0;"></td></tr>
                <tr style="margin:0; padding:0;"><td style="margin:0; padding:0;" height="16"></td></tr>
                <tr style="margin:0; padding:0;">
                    <td style="margin:0; padding:0;">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="border-collapse: collapse; margin:0; padding:0;">
                            <tbody>
                            <tr style="margin:0; padding:0;" valign="top">
                                <td width="33.3333%" style="margin:0; padding:0 5px;">
                                    <p style="color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;">Business Service Name</p>
                                    <p style="color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;">
                                        <#if .data_model["slo.profile.business.service.name"]??>${.data_model["slo.profile.business.service.name"]}</#if>
                                    </p>
                                </td>
                                <td width="33.3333%" style="margin:0; padding:0 5px;">
                                    <p style="color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;">Status</p>
                                    <p style="color:<#if .data_model["slo~status"]??><#if .data_model["slo~status"] == "Ok">#89C540<#elseif .data_model["slo~status"] == "Warning">#f5bc18<#elseif .data_model["slo~status"] == "Breached">#F25C4E<#else>#009DDC</#if><#else>#009DDC</#if>; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;">
                                        <#if .data_model["slo~status"]??>${.data_model["slo~status"]}</#if>
                                    </p>
                                </td>
                                <td width="33.3333%" style="margin:0; padding:0 5px;">
                                    <p style="color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;">SLO Frequency</p>
                                    <p style="color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;">
                                        <#if .data_model["slo.frequency"]??>${.data_model["slo.frequency"]}</#if>
                                    </p>
                                </td>
                            </tr>

                            <tr style="margin:0; padding:0;"><td colspan="3" style="margin:0; padding:0;" height="16"></td></tr>
                            <tr style="margin:0; padding:0;" valign="top">
                                <td width="33.3333%" style="margin:0; padding:0 5px;">
                                    <p style="color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;">Target</p>
                                    <p style="color:#009DDC; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;">
                                        <#if .data_model["slo.target"]??>${.data_model["slo.target"]}%</#if>
                                    </p>
                                </td>
                                <td width="33.3333%" style="margin:0; padding:0 5px;">
                                    <p style="color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;">Achieved</p>
                                    <p style="color:#89C540; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;">
                                        <#if .data_model["slo~achieved.percent"]??>${.data_model["slo~achieved.percent"]}%</#if>
                                    </p>
                                </td>
                                <td width="33.3333%" style="margin:0; padding:0 5px;">
                                    <p style="color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;">Violation</p>
                                    <p style="color:#F25C4E; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;">
                                        <#if .data_model["slo~violated.percent"]??>${.data_model["slo~violated.percent"]}%</#if>
                                    </p>
                                </td>
                            </tr>
                            <tr style="margin:0; padding:0;"><td colspan="3" style="margin:0; padding:0;" height="16"></td></tr>

                            <tr style="margin:0; padding:0;" valign="top">
                                <td width="33.3333%" style="margin:0; padding:0 5px;">
                                    <p style="color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;">Type</p>
                                    <p style="color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;">
                                        <#if .data_model["slo.profile.type"]??>${.data_model["slo.profile.type"]}</#if>
                                    </p>
                                </td>
                                <td width="33.3333%" style="margin:0; padding:0 5px;">
                                    <p style="color:#7B8FA5; font-size:10px; line-height:14px; margin:0; padding:0 0 5px;">Start Date/Time</p>
                                    <p style="color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;">
                                        <#if .data_model["slo.profile.start.time"]??>${.data_model["slo.profile.start.time"]}</#if>
                                    </p>
                                </td>
                                <td width="33.3333%" style="margin:0; padding:0 5px;">
                                    <!-- Empty cell for alignment -->
                                </td>
                            </tr>

                            <tr style="margin:0; padding:0;"><td colspan="3" style="margin:0; padding:0;" height="16"></td></tr>
                            <tr style="margin:0; padding:0;">
                                <td colspan="3" style="margin:0; padding:0;">
                                    <p style="color:#364658; font-size:10px; font-weight:bold; line-height:14px; margin:0; padding:0;">
                                        <#if .data_model["message"]??>
                                            ${.data_model["message"]}
                                      </#if>
                                    </p>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr style="margin:0; padding:0;"><td style="margin:0; padding:0;" height="16"></td></tr>
                <tr style="margin:0; padding:0;"><td style="margin:0; padding:0;"><hr style="border: 1px solid #EEF2F6; margin:0; padding:0;"></td></tr>
                <tr style="margin:0; padding:0;">
                    <td style="margin:0; padding:0;">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="border-collapse: collapse; margin:0; padding:0;">
                            <tbody>
                            <tr style="margin:0; padding:0;"><td colspan="3" style="margin:0; padding:0;" height="24"></td></tr>
                            <tr style="margin:0; padding:0;">
                                <td colspan="3" style="margin:0; padding:0;">
                                    <p style="margin:0; padding:0; font-size:11px; color:#727E8A; text-align:center;">
                                        For more information,
                                        <#if .data_model["slo.url"]??>
                                            <a href="${.data_model["slo.url"]}" target="_blank" style="color:#009DDC; text-decoration: underline;">Click here.</a>
                                        </#if>
                                    </p>
                                </td>
                            </tr>
                            <tr style="margin:0; padding:0;"><td colspan="3" style="margin:0; padding:0;" height="16"></td></tr>
                            <tr style="margin:0; padding:0;">
                                <td colspan="3" style="margin:0; padding:0;">
                                    <p style="color:#364658; font-size:10px; line-height:14px; text-align:center; margin:0; padding:0 0 5px;">
                                        <strong>Disclaimer:</strong> This is an auto-generated message. Do not respond to this email.<br/>
                                        If you have any questions, please contact your System Administrator.
                                    </p>
                                </td>
                            </tr>
                            <tr style="margin:0; padding:0;"><td colspan="3" style="margin:0; padding:0;" height="24"></td></tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                </tbody>
            </table>
        </td>
    </tr>
    <tr style="margin:0; padding:0;"><td style="margin:0; padding:0;" height="24"></td></tr>

    <#if .data_model["rebranding"]?? && !.data_model["rebranding"]>
        <tr align="center" style="margin:0; padding:0; text-align: center;">
            <td style="margin:0; padding:0;">
                <p style="font-size:10px; margin:0; padding:0; color:#7B8FA5;">
                    For more details, visit
                    <a style="color:#009DDC; text-decoration: none;" href="https://www.motadata.com/" target="_blank">www.motadata.com</a>
                </p>
            </td>
        </tr>
    </#if>
    <tr style="margin:0; padding:0;"><td style="margin:0; padding:0;" height="16"></td></tr>

    <#include "rebranding-footer.ftl">

    <tr style="margin:0; padding:0;"><td style="margin:0; padding:0;" height="32"></td></tr>
</table>
</body>
</html>