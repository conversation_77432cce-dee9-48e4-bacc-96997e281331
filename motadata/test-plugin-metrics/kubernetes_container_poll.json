{"172.16.14.94": {"result": {"kubernetes.container": [{"kubernetes.container": "kube-system-weave-net-sx2pd-weave-npc", "kubernetes.container.cpu.percent": 0.0032371250000000004, "kubernetes.container.cpu.request.percent": 1.25, "kubernetes.container.creation.time": "Wed, Jun 11 2025, 06:48", "kubernetes.container.image": "rajchaudhuri/weave-npc:2.9.0", "kubernetes.container.memory.bytes": ********, "kubernetes.container.name": "weave-npc", "kubernetes.container.node.name": "master", "kubernetes.container.pod.name": "weave-net-sx2pd", "kubernetes.container.restarts": 5, "kubernetes.container.status": "running", "kubernetes.container.volume.mounts": "/run/xtables.lock,/var/run/secrets/kubernetes.io/serviceaccount"}, {"kubernetes.container": "kube-system-weave-net-wmprn-weave-npc", "kubernetes.container.cpu.percent": 0.0009796000000000002, "kubernetes.container.cpu.request.percent": 1.25, "kubernetes.container.creation.time": "Wed, Jun 11 2025, 06:47", "kubernetes.container.image": "rajchaudhuri/weave-npc:2.9.0", "kubernetes.container.memory.bytes": ********, "kubernetes.container.name": "weave-npc", "kubernetes.container.node.name": "worker2", "kubernetes.container.pod.name": "weave-net-wmprn", "kubernetes.container.restarts": 5, "kubernetes.container.status": "running", "kubernetes.container.volume.mounts": "/run/xtables.lock,/var/run/secrets/kubernetes.io/serviceaccount"}]}}}