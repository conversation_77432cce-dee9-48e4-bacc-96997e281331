{"172.16.14.94": {"result": {"kubernetes.component": [{"kubernetes.component": "controller-manager", "kubernetes.component.message": "ok", "kubernetes.component.status": "Healthy"}, {"kubernetes.component": "scheduler", "kubernetes.component.message": "ok", "kubernetes.component.status": "Healthy"}, {"kubernetes.component": "etcd-0", "kubernetes.component.message": "ok", "kubernetes.component.status": "Healthy"}], "kubernetes.components": 3, "kubernetes.job": [{"kubernetes.job": "default-my-job", "kubernetes.job.completion.time": "Mon, Feb 24 2025, 13:16:58", "kubernetes.job.duration": 12, "kubernetes.job.name": "my-job", "kubernetes.job.namespace": "default", "kubernetes.job.started.time": "Mon, Feb 24 2025, 13:16:46", "kubernetes.job.status": "Complete"}], "kubernetes.jobs": 1, "kubernetes.namespace": [{"kubernetes.namespace": "default", "kubernetes.namespace.creation.time": "Sun, Feb 09 2025, 05:14", "kubernetes.namespace.failed.pods": 1, "kubernetes.namespace.pending.pods": 1, "kubernetes.namespace.running.pods": 3, "kubernetes.namespace.status": "Active", "kubernetes.namespace.succeeded.pods": 1, "kubernetes.namespace.total.pods": 6}, {"kubernetes.namespace": "kube-node-lease", "kubernetes.namespace.creation.time": "Sun, Feb 09 2025, 05:14", "kubernetes.namespace.status": "Active"}, {"kubernetes.namespace": "kube-public", "kubernetes.namespace.creation.time": "Sun, Feb 09 2025, 05:14", "kubernetes.namespace.status": "Active"}, {"kubernetes.namespace": "kube-system", "kubernetes.namespace.creation.time": "Sun, Feb 09 2025, 05:14", "kubernetes.namespace.running.pods": 13, "kubernetes.namespace.status": "Active", "kubernetes.namespace.total.pods": 13}], "kubernetes.namespaces": 4}}}