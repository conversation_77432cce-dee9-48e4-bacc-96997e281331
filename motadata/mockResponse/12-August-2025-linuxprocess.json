{"************": {"discovery": null, "collect": [{"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 17/main: logical replication launcher", "system.process.command": "postgres: 17/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1165, "system.process.memory.used.bytes": 3149824, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 6 minutes 38 seconds", "system.process.started.time.seconds": 3729998, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225947648}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 71, "system.process.id": 1133, "system.process.memory.used.bytes": 7294976, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 6 minutes 42 seconds", "system.process.started.time.seconds": 3730002, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224317440}, {"status": "Up", "system.process": "postgres|postgres: 17/main: checkpointer", "system.process.command": "postgres: 17/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1152, "system.process.memory.used.bytes": 19988480, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 6 minutes 39 seconds", "system.process.started.time.seconds": 3729999, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224927744}, {"status": "Up", "system.process": "postgres|postgres: 17/main: background writer", "system.process.command": "postgres: 17/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 68, "system.process.id": 1153, "system.process.memory.used.bytes": 6017024, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 6 minutes 39 seconds", "system.process.started.time.seconds": 3729999, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: walwriter", "system.process.command": "postgres: 17/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1163, "system.process.memory.used.bytes": 6504448, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 6 minutes 38 seconds", "system.process.started.time.seconds": 3729998, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: autovacuum launcher", "system.process.command": "postgres: 17/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1164, "system.process.memory.used.bytes": 3829760, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 6 minutes 38 seconds", "system.process.started.time.seconds": 3729998, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225939456}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 17/main: autovacuum launcher", "system.process.command": "postgres: 17/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1164, "system.process.memory.used.bytes": 3825664, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 11 minutes 39 seconds", "system.process.started.time.seconds": 3730299, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225939456}, {"status": "Up", "system.process": "postgres|postgres: 17/main: logical replication launcher", "system.process.command": "postgres: 17/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1165, "system.process.memory.used.bytes": 3149824, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 11 minutes 39 seconds", "system.process.started.time.seconds": 3730299, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225947648}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 71, "system.process.id": 1133, "system.process.memory.used.bytes": 7294976, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 11 minutes 43 seconds", "system.process.started.time.seconds": 3730303, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224317440}, {"status": "Up", "system.process": "postgres|postgres: 17/main: checkpointer", "system.process.command": "postgres: 17/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1152, "system.process.memory.used.bytes": 19955712, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 11 minutes 40 seconds", "system.process.started.time.seconds": 3730300, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224927744}, {"status": "Up", "system.process": "postgres|postgres: 17/main: background writer", "system.process.command": "postgres: 17/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 68, "system.process.id": 1153, "system.process.memory.used.bytes": 5996544, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 11 minutes 40 seconds", "system.process.started.time.seconds": 3730300, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: walwriter", "system.process.command": "postgres: 17/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1163, "system.process.memory.used.bytes": 6377472, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 11 minutes 39 seconds", "system.process.started.time.seconds": 3730299, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 17/main: checkpointer", "system.process.command": "postgres: 17/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1152, "system.process.memory.used.bytes": 19865600, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 16 minutes 39 seconds", "system.process.started.time.seconds": 3730599, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224927744}, {"status": "Up", "system.process": "postgres|postgres: 17/main: background writer", "system.process.command": "postgres: 17/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 68, "system.process.id": 1153, "system.process.memory.used.bytes": 5988352, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 16 minutes 39 seconds", "system.process.started.time.seconds": 3730599, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: walwriter", "system.process.command": "postgres: 17/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1163, "system.process.memory.used.bytes": 6295552, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 16 minutes 38 seconds", "system.process.started.time.seconds": 3730598, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: autovacuum launcher", "system.process.command": "postgres: 17/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1164, "system.process.memory.used.bytes": 3825664, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 16 minutes 38 seconds", "system.process.started.time.seconds": 3730598, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225939456}, {"status": "Up", "system.process": "postgres|postgres: 17/main: logical replication launcher", "system.process.command": "postgres: 17/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1165, "system.process.memory.used.bytes": 3149824, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 16 minutes 38 seconds", "system.process.started.time.seconds": 3730598, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225947648}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 71, "system.process.id": 1133, "system.process.memory.used.bytes": 7294976, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 16 minutes 42 seconds", "system.process.started.time.seconds": 3730602, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224317440}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 17/main: logical replication launcher", "system.process.command": "postgres: 17/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1165, "system.process.memory.used.bytes": 3149824, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 21 minutes 41 seconds", "system.process.started.time.seconds": 3730901, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225947648}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 71, "system.process.id": 1133, "system.process.memory.used.bytes": 7294976, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 21 minutes 45 seconds", "system.process.started.time.seconds": 3730905, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224317440}, {"status": "Up", "system.process": "postgres|postgres: 17/main: checkpointer", "system.process.command": "postgres: 17/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1152, "system.process.memory.used.bytes": 19857408, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 21 minutes 42 seconds", "system.process.started.time.seconds": 3730902, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224927744}, {"status": "Up", "system.process": "postgres|postgres: 17/main: background writer", "system.process.command": "postgres: 17/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 68, "system.process.id": 1153, "system.process.memory.used.bytes": 5988352, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 21 minutes 42 seconds", "system.process.started.time.seconds": 3730902, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: walwriter", "system.process.command": "postgres: 17/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1163, "system.process.memory.used.bytes": 6295552, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 21 minutes 41 seconds", "system.process.started.time.seconds": 3730901, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: autovacuum launcher", "system.process.command": "postgres: 17/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1164, "system.process.memory.used.bytes": 3825664, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 21 minutes 41 seconds", "system.process.started.time.seconds": 3730901, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225939456}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 71, "system.process.id": 1133, "system.process.memory.used.bytes": 7294976, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 26 minutes 42 seconds", "system.process.started.time.seconds": 3731202, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224317440}, {"status": "Up", "system.process": "postgres|postgres: 17/main: checkpointer", "system.process.command": "postgres: 17/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1152, "system.process.memory.used.bytes": 21069824, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 26 minutes 39 seconds", "system.process.started.time.seconds": 3731199, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224927744}, {"status": "Up", "system.process": "postgres|postgres: 17/main: background writer", "system.process.command": "postgres: 17/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 68, "system.process.id": 1153, "system.process.memory.used.bytes": 5988352, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 26 minutes 39 seconds", "system.process.started.time.seconds": 3731199, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: walwriter", "system.process.command": "postgres: 17/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1163, "system.process.memory.used.bytes": 6283264, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 26 minutes 38 seconds", "system.process.started.time.seconds": 3731198, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: autovacuum launcher", "system.process.command": "postgres: 17/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1164, "system.process.memory.used.bytes": 3825664, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 26 minutes 38 seconds", "system.process.started.time.seconds": 3731198, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225939456}, {"status": "Up", "system.process": "postgres|postgres: 17/main: logical replication launcher", "system.process.command": "postgres: 17/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1165, "system.process.memory.used.bytes": 3149824, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 26 minutes 38 seconds", "system.process.started.time.seconds": 3731198, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225947648}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 17/main: logical replication launcher", "system.process.command": "postgres: 17/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1165, "system.process.memory.used.bytes": 3149824, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 31 minutes 47 seconds", "system.process.started.time.seconds": 3731507, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225947648}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 71, "system.process.id": 1133, "system.process.memory.used.bytes": 7294976, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 31 minutes 51 seconds", "system.process.started.time.seconds": 3731511, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224317440}, {"status": "Up", "system.process": "postgres|postgres: 17/main: checkpointer", "system.process.command": "postgres: 17/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1152, "system.process.memory.used.bytes": 20721664, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 31 minutes 48 seconds", "system.process.started.time.seconds": 3731508, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224927744}, {"status": "Up", "system.process": "postgres|postgres: 17/main: background writer", "system.process.command": "postgres: 17/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 68, "system.process.id": 1153, "system.process.memory.used.bytes": 6111232, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 31 minutes 48 seconds", "system.process.started.time.seconds": 3731508, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: walwriter", "system.process.command": "postgres: 17/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1163, "system.process.memory.used.bytes": 6057984, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 31 minutes 47 seconds", "system.process.started.time.seconds": 3731507, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: autovacuum launcher", "system.process.command": "postgres: 17/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1164, "system.process.memory.used.bytes": 3825664, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 31 minutes 47 seconds", "system.process.started.time.seconds": 3731507, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225939456}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 17/main: background writer", "system.process.command": "postgres: 17/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 68, "system.process.id": 1153, "system.process.memory.used.bytes": 6111232, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 36 minutes 39 seconds", "system.process.started.time.seconds": 3731799, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: walwriter", "system.process.command": "postgres: 17/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1163, "system.process.memory.used.bytes": 6057984, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 36 minutes 38 seconds", "system.process.started.time.seconds": 3731798, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: autovacuum launcher", "system.process.command": "postgres: 17/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1164, "system.process.memory.used.bytes": 3825664, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 36 minutes 38 seconds", "system.process.started.time.seconds": 3731798, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225939456}, {"status": "Up", "system.process": "postgres|postgres: 17/main: logical replication launcher", "system.process.command": "postgres: 17/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1165, "system.process.memory.used.bytes": 3149824, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 36 minutes 38 seconds", "system.process.started.time.seconds": 3731798, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225947648}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 71, "system.process.id": 1133, "system.process.memory.used.bytes": 7294976, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 36 minutes 42 seconds", "system.process.started.time.seconds": 3731802, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224317440}, {"status": "Up", "system.process": "postgres|postgres: 17/main: checkpointer", "system.process.command": "postgres: 17/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1152, "system.process.memory.used.bytes": 20721664, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 36 minutes 39 seconds", "system.process.started.time.seconds": 3731799, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224927744}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 71, "system.process.id": 1133, "system.process.memory.used.bytes": 7294976, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 41 minutes 52 seconds", "system.process.started.time.seconds": 3732112, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224317440}, {"status": "Up", "system.process": "postgres|postgres: 17/main: checkpointer", "system.process.command": "postgres: 17/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1152, "system.process.memory.used.bytes": 20721664, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 41 minutes 49 seconds", "system.process.started.time.seconds": 3732109, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224927744}, {"status": "Up", "system.process": "postgres|postgres: 17/main: background writer", "system.process.command": "postgres: 17/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 68, "system.process.id": 1153, "system.process.memory.used.bytes": 6111232, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 41 minutes 49 seconds", "system.process.started.time.seconds": 3732109, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: walwriter", "system.process.command": "postgres: 17/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1163, "system.process.memory.used.bytes": 6057984, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 41 minutes 48 seconds", "system.process.started.time.seconds": 3732108, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: autovacuum launcher", "system.process.command": "postgres: 17/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1164, "system.process.memory.used.bytes": 3825664, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 41 minutes 48 seconds", "system.process.started.time.seconds": 3732108, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225939456}, {"status": "Up", "system.process": "postgres|postgres: 17/main: logical replication launcher", "system.process.command": "postgres: 17/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1165, "system.process.memory.used.bytes": 3149824, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 41 minutes 48 seconds", "system.process.started.time.seconds": 3732108, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225947648}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 71, "system.process.id": 1133, "system.process.memory.used.bytes": 7294976, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 49 minutes 50 seconds", "system.process.started.time.seconds": 3732590, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224317440}, {"status": "Up", "system.process": "postgres|postgres: 17/main: checkpointer", "system.process.command": "postgres: 17/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1152, "system.process.memory.used.bytes": 20619264, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 49 minutes 47 seconds", "system.process.started.time.seconds": 3732587, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224927744}, {"status": "Up", "system.process": "postgres|postgres: 17/main: background writer", "system.process.command": "postgres: 17/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 68, "system.process.id": 1153, "system.process.memory.used.bytes": 6111232, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 49 minutes 47 seconds", "system.process.started.time.seconds": 3732587, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: walwriter", "system.process.command": "postgres: 17/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 69, "system.process.id": 1163, "system.process.memory.used.bytes": 6057984, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 49 minutes 46 seconds", "system.process.started.time.seconds": 3732586, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 224456704}, {"status": "Up", "system.process": "postgres|postgres: 17/main: autovacuum launcher", "system.process.command": "postgres: 17/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1164, "system.process.memory.used.bytes": 3825664, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 49 minutes 46 seconds", "system.process.started.time.seconds": 3732586, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225939456}, {"status": "Up", "system.process": "postgres|postgres: 17/main: logical replication launcher", "system.process.command": "postgres: 17/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1165, "system.process.memory.used.bytes": 3149824, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 43 days 4 hours 49 minutes 46 seconds", "system.process.started.time.seconds": 3732586, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 225947648}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38208) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38218) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38228) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38242) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.command": "postgres: 17/main: motadata motadata 127.0.0.1(38254) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/17/bin/postgres -D /var/lib/postgresql/17/main -c config_file=/etc/postgresql/17/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}], "rediscovery": null}, "************": {"discovery": null, "collect": [{"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 10/main: checkpointer process", "system.process.command": "postgres: 10/main: checkpointer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1570, "system.process.memory.used.bytes": 3604480, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 149 days 3 hours 47 minutes 18 seconds", "system.process.started.time.seconds": 12887238, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: writer process", "system.process.command": "postgres: 10/main: writer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1571, "system.process.memory.used.bytes": 5824512, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 3 hours 47 minutes 18 seconds", "system.process.started.time.seconds": 12887238, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 2796, "system.process.memory.used.bytes": 4677632, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 3 hours 49 minutes 47 seconds", "system.process.started.time.seconds": 13787, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058444800}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 2798, "system.process.memory.used.bytes": 4677632, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 3 hours 49 minutes 47 seconds", "system.process.started.time.seconds": 13787, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2125520896}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 49, "system.process.id": 1411, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4947968, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 149 days 3 hours 47 minutes 27 seconds", "system.process.started.time.seconds": 12887247, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 80080896}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1485, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 21106688, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 149 days 3 hours 47 minutes 23 seconds", "system.process.started.time.seconds": 12887243, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: wal writer process", "system.process.command": "postgres: 10/main: wal writer process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1572, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 8974336, "system.process.memory.used.percent": 0.2, "system.process.name": "postgres", "system.process.started.time": " 149 days 3 hours 47 minutes 18 seconds", "system.process.started.time.seconds": 12887238, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: autovacuum launcher process", "system.process.command": "postgres: 10/main: autovacuum launcher process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1573, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 6205440, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 3 hours 47 minutes 18 seconds", "system.process.started.time.seconds": 12887238, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326885376}, {"status": "Up", "system.process": "postgres|postgres: 10/main: stats collector process", "system.process.command": "postgres: 10/main: stats collector process", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1576, "system.process.memory.used.bytes": 3063808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 149 days 3 hours 47 minutes 18 seconds", "system.process.started.time.seconds": 12887238, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 177905664}, {"status": "Up", "system.process": "postgres|postgres: 10/main: bgworker: logical replication launcher", "system.process.command": "postgres: 10/main: bgworker: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1577, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4263936, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 3 hours 47 minutes 18 seconds", "system.process.started.time.seconds": 12887238, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326762496}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 49, "system.process.id": 1411, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4947968, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 149 days 3 hours 52 minutes 28 seconds", "system.process.started.time.seconds": 12887548, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 80080896}, {"status": "Up", "system.process": "postgres|postgres: 10/main: checkpointer process", "system.process.command": "postgres: 10/main: checkpointer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1570, "system.process.memory.used.bytes": 3604480, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 149 days 3 hours 52 minutes 19 seconds", "system.process.started.time.seconds": 12887539, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: writer process", "system.process.command": "postgres: 10/main: writer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1571, "system.process.memory.used.bytes": 5824512, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 3 hours 52 minutes 19 seconds", "system.process.started.time.seconds": 12887539, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: autovacuum launcher process", "system.process.command": "postgres: 10/main: autovacuum launcher process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1573, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 6205440, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 3 hours 52 minutes 19 seconds", "system.process.started.time.seconds": 12887539, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326885376}, {"status": "Up", "system.process": "postgres|postgres: 10/main: stats collector process", "system.process.command": "postgres: 10/main: stats collector process", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1576, "system.process.memory.used.bytes": 3063808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 149 days 3 hours 52 minutes 19 seconds", "system.process.started.time.seconds": 12887539, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 177905664}, {"status": "Up", "system.process": "postgres|postgres: 10/main: bgworker: logical replication launcher", "system.process.command": "postgres: 10/main: bgworker: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1577, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4263936, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 3 hours 52 minutes 19 seconds", "system.process.started.time.seconds": 12887539, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326762496}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 2796, "system.process.memory.used.bytes": 4677632, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 3 hours 54 minutes 48 seconds", "system.process.started.time.seconds": 14088, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058444800}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1485, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 21106688, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 149 days 3 hours 52 minutes 24 seconds", "system.process.started.time.seconds": 12887544, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: wal writer process", "system.process.command": "postgres: 10/main: wal writer process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1572, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 8974336, "system.process.memory.used.percent": 0.2, "system.process.name": "postgres", "system.process.started.time": " 149 days 3 hours 52 minutes 19 seconds", "system.process.started.time.seconds": 12887539, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 2798, "system.process.memory.used.bytes": 4677632, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 3 hours 54 minutes 48 seconds", "system.process.started.time.seconds": 14088, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2125520896}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 10/main: checkpointer process", "system.process.command": "postgres: 10/main: checkpointer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1570, "system.process.memory.used.bytes": 3604480, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 149 days 3 hours 57 minutes 18 seconds", "system.process.started.time.seconds": 12887838, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: wal writer process", "system.process.command": "postgres: 10/main: wal writer process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1572, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 8974336, "system.process.memory.used.percent": 0.2, "system.process.name": "postgres", "system.process.started.time": " 149 days 3 hours 57 minutes 18 seconds", "system.process.started.time.seconds": 12887838, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 2796, "system.process.memory.used.bytes": 4677632, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 3 hours 59 minutes 47 seconds", "system.process.started.time.seconds": 14387, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058444800}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 2798, "system.process.memory.used.bytes": 4677632, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 3 hours 59 minutes 47 seconds", "system.process.started.time.seconds": 14387, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2125520896}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 49, "system.process.id": 1411, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4947968, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 149 days 3 hours 57 minutes 27 seconds", "system.process.started.time.seconds": 12887847, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 80080896}, {"status": "Up", "system.process": "postgres|postgres: 10/main: writer process", "system.process.command": "postgres: 10/main: writer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1571, "system.process.memory.used.bytes": 5824512, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 3 hours 57 minutes 18 seconds", "system.process.started.time.seconds": 12887838, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: autovacuum launcher process", "system.process.command": "postgres: 10/main: autovacuum launcher process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1573, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 6205440, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 3 hours 57 minutes 18 seconds", "system.process.started.time.seconds": 12887838, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326885376}, {"status": "Up", "system.process": "postgres|postgres: 10/main: stats collector process", "system.process.command": "postgres: 10/main: stats collector process", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1576, "system.process.memory.used.bytes": 3063808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 149 days 3 hours 57 minutes 18 seconds", "system.process.started.time.seconds": 12887838, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 177905664}, {"status": "Up", "system.process": "postgres|postgres: 10/main: bgworker: logical replication launcher", "system.process.command": "postgres: 10/main: bgworker: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1577, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4263936, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 3 hours 57 minutes 18 seconds", "system.process.started.time.seconds": 12887838, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326762496}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1485, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 21106688, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 149 days 3 hours 57 minutes 23 seconds", "system.process.started.time.seconds": 12887843, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1485, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 21106688, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 2 minutes 26 seconds", "system.process.started.time.seconds": 12888146, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: wal writer process", "system.process.command": "postgres: 10/main: wal writer process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1572, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 8974336, "system.process.memory.used.percent": 0.2, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 2 minutes 21 seconds", "system.process.started.time.seconds": 12888141, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: stats collector process", "system.process.command": "postgres: 10/main: stats collector process", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1576, "system.process.memory.used.bytes": 3063808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 2 minutes 21 seconds", "system.process.started.time.seconds": 12888141, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 177905664}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 49, "system.process.id": 1411, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4947968, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 149 days 4 hours 2 minutes 30 seconds", "system.process.started.time.seconds": 12888150, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 80080896}, {"status": "Up", "system.process": "postgres|postgres: 10/main: checkpointer process", "system.process.command": "postgres: 10/main: checkpointer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1570, "system.process.memory.used.bytes": 3604480, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 2 minutes 21 seconds", "system.process.started.time.seconds": 12888141, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: writer process", "system.process.command": "postgres: 10/main: writer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1571, "system.process.memory.used.bytes": 5824512, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 2 minutes 21 seconds", "system.process.started.time.seconds": 12888141, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: autovacuum launcher process", "system.process.command": "postgres: 10/main: autovacuum launcher process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1573, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 6205440, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 2 minutes 21 seconds", "system.process.started.time.seconds": 12888141, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326885376}, {"status": "Up", "system.process": "postgres|postgres: 10/main: bgworker: logical replication launcher", "system.process.command": "postgres: 10/main: bgworker: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1577, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4263936, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 2 minutes 21 seconds", "system.process.started.time.seconds": 12888141, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326762496}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 2796, "system.process.memory.used.bytes": 4677632, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 4 hours 4 minutes 50 seconds", "system.process.started.time.seconds": 14690, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058444800}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 2798, "system.process.memory.used.bytes": 4677632, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 4 hours 4 minutes 50 seconds", "system.process.started.time.seconds": 14690, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2125520896}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 49, "system.process.id": 1411, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4947968, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 149 days 4 hours 7 minutes 27 seconds", "system.process.started.time.seconds": 12888447, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 80080896}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1485, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 21106688, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 7 minutes 23 seconds", "system.process.started.time.seconds": 12888443, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: checkpointer process", "system.process.command": "postgres: 10/main: checkpointer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1570, "system.process.memory.used.bytes": 3604480, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 7 minutes 18 seconds", "system.process.started.time.seconds": 12888438, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: wal writer process", "system.process.command": "postgres: 10/main: wal writer process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1572, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 8974336, "system.process.memory.used.percent": 0.2, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 7 minutes 18 seconds", "system.process.started.time.seconds": 12888438, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: autovacuum launcher process", "system.process.command": "postgres: 10/main: autovacuum launcher process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1573, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 6205440, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 7 minutes 18 seconds", "system.process.started.time.seconds": 12888438, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326885376}, {"status": "Up", "system.process": "postgres|postgres: 10/main: stats collector process", "system.process.command": "postgres: 10/main: stats collector process", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1576, "system.process.memory.used.bytes": 3063808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 7 minutes 18 seconds", "system.process.started.time.seconds": 12888438, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 177905664}, {"status": "Up", "system.process": "postgres|postgres: 10/main: writer process", "system.process.command": "postgres: 10/main: writer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1571, "system.process.memory.used.bytes": 5824512, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 7 minutes 18 seconds", "system.process.started.time.seconds": 12888438, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: bgworker: logical replication launcher", "system.process.command": "postgres: 10/main: bgworker: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1577, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4263936, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 7 minutes 18 seconds", "system.process.started.time.seconds": 12888438, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326762496}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 2796, "system.process.memory.used.bytes": 4677632, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 4 hours 9 minutes 47 seconds", "system.process.started.time.seconds": 14987, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058444800}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 2798, "system.process.memory.used.bytes": 4677632, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 4 hours 9 minutes 47 seconds", "system.process.started.time.seconds": 14987, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2125520896}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1485, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 21106688, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 12 minutes 31 seconds", "system.process.started.time.seconds": 12888751, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: autovacuum launcher process", "system.process.command": "postgres: 10/main: autovacuum launcher process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1573, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 6205440, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 12 minutes 26 seconds", "system.process.started.time.seconds": 12888746, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326885376}, {"status": "Up", "system.process": "postgres|postgres: 10/main: stats collector process", "system.process.command": "postgres: 10/main: stats collector process", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1576, "system.process.memory.used.bytes": 3063808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 12 minutes 26 seconds", "system.process.started.time.seconds": 12888746, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 177905664}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 2796, "system.process.memory.used.bytes": 4677632, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 4 hours 14 minutes 55 seconds", "system.process.started.time.seconds": 15295, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058444800}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 2798, "system.process.memory.used.bytes": 4677632, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 4 hours 14 minutes 55 seconds", "system.process.started.time.seconds": 15295, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2125520896}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 49, "system.process.id": 1411, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4947968, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 149 days 4 hours 12 minutes 35 seconds", "system.process.started.time.seconds": 12888755, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 80080896}, {"status": "Up", "system.process": "postgres|postgres: 10/main: checkpointer process", "system.process.command": "postgres: 10/main: checkpointer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1570, "system.process.memory.used.bytes": 3604480, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 12 minutes 26 seconds", "system.process.started.time.seconds": 12888746, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: writer process", "system.process.command": "postgres: 10/main: writer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1571, "system.process.memory.used.bytes": 5824512, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 12 minutes 26 seconds", "system.process.started.time.seconds": 12888746, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: wal writer process", "system.process.command": "postgres: 10/main: wal writer process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1572, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 8974336, "system.process.memory.used.percent": 0.2, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 12 minutes 26 seconds", "system.process.started.time.seconds": 12888746, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: bgworker: logical replication launcher", "system.process.command": "postgres: 10/main: bgworker: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1577, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4263936, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 12 minutes 26 seconds", "system.process.started.time.seconds": 12888746, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326762496}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 10/main: autovacuum launcher process", "system.process.command": "postgres: 10/main: autovacuum launcher process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1573, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 6205440, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 17 minutes 18 seconds", "system.process.started.time.seconds": 12889038, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326885376}, {"status": "Up", "system.process": "postgres|postgres: 10/main: stats collector process", "system.process.command": "postgres: 10/main: stats collector process", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1576, "system.process.memory.used.bytes": 3063808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 17 minutes 18 seconds", "system.process.started.time.seconds": 12889038, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 177905664}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 2796, "system.process.memory.used.bytes": 4677632, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 4 hours 19 minutes 47 seconds", "system.process.started.time.seconds": 15587, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058444800}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 2798, "system.process.memory.used.bytes": 4677632, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 4 hours 19 minutes 47 seconds", "system.process.started.time.seconds": 15587, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2125520896}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1485, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 21106688, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 17 minutes 23 seconds", "system.process.started.time.seconds": 12889043, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: checkpointer process", "system.process.command": "postgres: 10/main: checkpointer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1570, "system.process.memory.used.bytes": 3604480, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 17 minutes 18 seconds", "system.process.started.time.seconds": 12889038, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: wal writer process", "system.process.command": "postgres: 10/main: wal writer process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1572, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 8974336, "system.process.memory.used.percent": 0.2, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 17 minutes 18 seconds", "system.process.started.time.seconds": 12889038, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: bgworker: logical replication launcher", "system.process.command": "postgres: 10/main: bgworker: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1577, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4263936, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 17 minutes 18 seconds", "system.process.started.time.seconds": 12889038, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326762496}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 49, "system.process.id": 1411, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4947968, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 149 days 4 hours 17 minutes 27 seconds", "system.process.started.time.seconds": 12889047, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 80080896}, {"status": "Up", "system.process": "postgres|postgres: 10/main: writer process", "system.process.command": "postgres: 10/main: writer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1571, "system.process.memory.used.bytes": 5824512, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 17 minutes 18 seconds", "system.process.started.time.seconds": 12889038, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 49, "system.process.id": 1411, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4947968, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 149 days 4 hours 22 minutes 37 seconds", "system.process.started.time.seconds": 12889357, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 80080896}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1485, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 21106688, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 22 minutes 33 seconds", "system.process.started.time.seconds": 12889353, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: checkpointer process", "system.process.command": "postgres: 10/main: checkpointer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1570, "system.process.memory.used.bytes": 3604480, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 22 minutes 28 seconds", "system.process.started.time.seconds": 12889348, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: writer process", "system.process.command": "postgres: 10/main: writer process", "system.process.cpu.percent": 0, "system.process.handles": 73, "system.process.id": 1571, "system.process.memory.used.bytes": 5824512, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 22 minutes 28 seconds", "system.process.started.time.seconds": 12889348, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: wal writer process", "system.process.command": "postgres: 10/main: wal writer process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1572, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 8974336, "system.process.memory.used.percent": 0.2, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 22 minutes 28 seconds", "system.process.started.time.seconds": 12889348, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326471680}, {"status": "Up", "system.process": "postgres|postgres: 10/main: autovacuum launcher process", "system.process.command": "postgres: 10/main: autovacuum launcher process", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1573, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 6205440, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 22 minutes 28 seconds", "system.process.started.time.seconds": 12889348, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326885376}, {"status": "Up", "system.process": "postgres|postgres: 10/main: stats collector process", "system.process.command": "postgres: 10/main: stats collector process", "system.process.cpu.percent": 0, "system.process.handles": 70, "system.process.id": 1576, "system.process.memory.used.bytes": 3063808, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 22 minutes 28 seconds", "system.process.started.time.seconds": 12889348, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 177905664}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 2796, "system.process.memory.used.bytes": 4677632, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 4 hours 24 minutes 57 seconds", "system.process.started.time.seconds": 15897, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2058444800}, {"status": "Up", "system.process": "postgres|postgres: 10/main: bgworker: logical replication launcher", "system.process.command": "postgres: 10/main: bgworker: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 74, "system.process.id": 1577, "system.process.io.bytes.per.sec": -1025, "system.process.memory.used.bytes": 4263936, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 149 days 4 hours 22 minutes 28 seconds", "system.process.started.time.seconds": 12889348, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 326762496}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 2798, "system.process.memory.used.bytes": 4677632, "system.process.memory.used.percent": 0.1, "system.process.name": "apache2", "system.process.started.time": " 0 day 4 hours 24 minutes 57 seconds", "system.process.started.time.seconds": 15897, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 2125520896}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/10/bin/postgres -D /var/lib/postgresql/10/main -c config_file=/etc/postgresql/10/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}], "rediscovery": null}, "************": {"discovery": null, "collect": [{"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 789, "system.process.memory.used.bytes": 2519040, "system.process.memory.used.percent": 0.2, "system.process.name": "apache2", "system.process.started.time": " 106 days 23 hours 25 minutes 41 seconds", "system.process.started.time.seconds": 9242741, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 6836224}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 803, "system.process.memory.used.bytes": 7335936, "system.process.memory.used.percent": 0.7, "system.process.name": "apache2", "system.process.started.time": " 106 days 23 hours 25 minutes 40 seconds", "system.process.started.time.seconds": 9242740, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241591808}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 804, "system.process.memory.used.bytes": 7782400, "system.process.memory.used.percent": 0.7, "system.process.name": "apache2", "system.process.started.time": " 106 days 23 hours 25 minutes 40 seconds", "system.process.started.time.seconds": 9242740, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241600000}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 789, "system.process.memory.used.bytes": 2519040, "system.process.memory.used.percent": 0.2, "system.process.name": "apache2", "system.process.started.time": " 106 days 23 hours 30 minutes 41 seconds", "system.process.started.time.seconds": 9243041, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 6836224}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 803, "system.process.memory.used.bytes": 7335936, "system.process.memory.used.percent": 0.7, "system.process.name": "apache2", "system.process.started.time": " 106 days 23 hours 30 minutes 40 seconds", "system.process.started.time.seconds": 9243040, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241591808}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 804, "system.process.memory.used.bytes": 7782400, "system.process.memory.used.percent": 0.7, "system.process.name": "apache2", "system.process.started.time": " 106 days 23 hours 30 minutes 40 seconds", "system.process.started.time.seconds": 9243040, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241600000}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 789, "system.process.memory.used.bytes": 2519040, "system.process.memory.used.percent": 0.2, "system.process.name": "apache2", "system.process.started.time": " 106 days 23 hours 35 minutes 41 seconds", "system.process.started.time.seconds": 9243341, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 6836224}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 803, "system.process.memory.used.bytes": 7335936, "system.process.memory.used.percent": 0.7, "system.process.name": "apache2", "system.process.started.time": " 106 days 23 hours 35 minutes 40 seconds", "system.process.started.time.seconds": 9243340, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241591808}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 804, "system.process.memory.used.bytes": 7782400, "system.process.memory.used.percent": 0.7, "system.process.name": "apache2", "system.process.started.time": " 106 days 23 hours 35 minutes 40 seconds", "system.process.started.time.seconds": 9243340, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241600000}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 789, "system.process.memory.used.bytes": 2519040, "system.process.memory.used.percent": 0.2, "system.process.name": "apache2", "system.process.started.time": " 106 days 23 hours 40 minutes 43 seconds", "system.process.started.time.seconds": 9243643, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 6836224}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 803, "system.process.memory.used.bytes": 7335936, "system.process.memory.used.percent": 0.7, "system.process.name": "apache2", "system.process.started.time": " 106 days 23 hours 40 minutes 42 seconds", "system.process.started.time.seconds": 9243642, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241591808}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 804, "system.process.memory.used.bytes": 7782400, "system.process.memory.used.percent": 0.7, "system.process.name": "apache2", "system.process.started.time": " 106 days 23 hours 40 minutes 42 seconds", "system.process.started.time.seconds": 9243642, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241600000}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 789, "system.process.memory.used.bytes": 2519040, "system.process.memory.used.percent": 0.2, "system.process.name": "apache2", "system.process.started.time": " 106 days 23 hours 45 minutes 40 seconds", "system.process.started.time.seconds": 9243940, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 6836224}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 803, "system.process.memory.used.bytes": 7335936, "system.process.memory.used.percent": 0.7, "system.process.name": "apache2", "system.process.started.time": " 106 days 23 hours 45 minutes 39 seconds", "system.process.started.time.seconds": 9243939, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241591808}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 804, "system.process.memory.used.bytes": 7782400, "system.process.memory.used.percent": 0.7, "system.process.name": "apache2", "system.process.started.time": " 106 days 23 hours 45 minutes 39 seconds", "system.process.started.time.seconds": 9243939, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241600000}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 789, "system.process.memory.used.bytes": 2519040, "system.process.memory.used.percent": 0.2, "system.process.name": "apache2", "system.process.started.time": " 106 days 23 hours 50 minutes 48 seconds", "system.process.started.time.seconds": 9244248, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 6836224}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 803, "system.process.memory.used.bytes": 7335936, "system.process.memory.used.percent": 0.7, "system.process.name": "apache2", "system.process.started.time": " 106 days 23 hours 50 minutes 47 seconds", "system.process.started.time.seconds": 9244247, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241591808}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 804, "system.process.memory.used.bytes": 7782400, "system.process.memory.used.percent": 0.7, "system.process.name": "apache2", "system.process.started.time": " 106 days 23 hours 50 minutes 47 seconds", "system.process.started.time.seconds": 9244247, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241600000}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 789, "system.process.memory.used.bytes": 2519040, "system.process.memory.used.percent": 0.2, "system.process.name": "apache2", "system.process.started.time": " 106 days 23 hours 55 minutes 40 seconds", "system.process.started.time.seconds": 9244540, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 6836224}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 803, "system.process.memory.used.bytes": 7335936, "system.process.memory.used.percent": 0.7, "system.process.name": "apache2", "system.process.started.time": " 106 days 23 hours 55 minutes 39 seconds", "system.process.started.time.seconds": 9244539, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241591808}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 804, "system.process.memory.used.bytes": 7782400, "system.process.memory.used.percent": 0.7, "system.process.name": "apache2", "system.process.started.time": " 106 days 23 hours 55 minutes 39 seconds", "system.process.started.time.seconds": 9244539, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241600000}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 789, "system.process.memory.used.bytes": 2519040, "system.process.memory.used.percent": 0.2, "system.process.name": "apache2", "system.process.started.time": " 107 days 0 hour 0 minute 50 seconds", "system.process.started.time.seconds": 9244850, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 6836224}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 803, "system.process.memory.used.bytes": 7335936, "system.process.memory.used.percent": 0.7, "system.process.name": "apache2", "system.process.started.time": " 107 days 0 hour 0 minute 49 seconds", "system.process.started.time.seconds": 9244849, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241591808}, {"status": "Up", "system.process": "apache2|/usr/sbin/apache2 -k start", "system.process.command": "/usr/sbin/apache2 -k start", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 804, "system.process.memory.used.bytes": 7782400, "system.process.memory.used.percent": 0.7, "system.process.name": "apache2", "system.process.started.time": " 107 days 0 hour 0 minute 49 seconds", "system.process.started.time.seconds": 9244849, "system.process.threads": 27, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 1241600000}]}], "rediscovery": null}, "172.16.8.167": {"discovery": null, "collect": [{"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.command": "/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.name": "evolutio"}, {"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.command": "/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.name": "evolutio"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.command": "/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.name": "evolutio"}, {"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.command": "/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.name": "evolutio"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.command": "/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.name": "evolutio"}, {"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.command": "/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.name": "evolutio"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.command": "/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.name": "evolutio"}, {"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.command": "/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.name": "evolutio"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.command": "/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.name": "evolutio"}, {"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.command": "/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.name": "evolutio"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.command": "/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.name": "evolutio"}, {"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.command": "/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.name": "evolutio"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.command": "/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.name": "evolutio"}, {"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.command": "/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.name": "evolutio"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.command": "/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.name": "evolutio"}, {"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.command": "/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.name": "evolutio"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.command": "/usr/libexec/evolution-calendar-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.Calendarx2816x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/Calendar/2816/2", "system.process.name": "evolutio"}, {"status": "Down", "system.process": "evolutio|/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.command": "/usr/libexec/evolution-addressbook-factory-subprocess --factory all --bus-name org.gnome.evolution.dataserver.Subprocess.Backend.AddressBookx2902x2 --own-path /org/gnome/evolution/dataserver/Subprocess/Backend/AddressBook/2902/2", "system.process.name": "evolutio"}]}], "rediscovery": null}, "************": {"discovery": null, "collect": [{"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 64, "system.process.id": 1674270, "system.process.memory.used.bytes": 19529728, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 7 minutes 45 seconds", "system.process.started.time.seconds": 2347665, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674271, "system.process.memory.used.bytes": 123207680, "system.process.memory.used.percent": 0.9, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 7 minutes 45 seconds", "system.process.started.time.seconds": 2347665, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229064704}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 1674272, "system.process.memory.used.bytes": 8982528, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 7 minutes 45 seconds", "system.process.started.time.seconds": 2347665, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228753408}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674274, "system.process.memory.used.bytes": 9244672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 7 minutes 45 seconds", "system.process.started.time.seconds": 2347665, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674275, "system.process.memory.used.bytes": 9900032, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 7 minutes 45 seconds", "system.process.started.time.seconds": 2347665, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674276, "system.process.memory.used.bytes": 7278592, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 7 minutes 45 seconds", "system.process.started.time.seconds": 2347665, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230215680}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674274, "system.process.memory.used.bytes": 9244672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 12 minutes 45 seconds", "system.process.started.time.seconds": 2347965, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674275, "system.process.memory.used.bytes": 9900032, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 12 minutes 45 seconds", "system.process.started.time.seconds": 2347965, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674276, "system.process.memory.used.bytes": 7278592, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 12 minutes 45 seconds", "system.process.started.time.seconds": 2347965, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230215680}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 64, "system.process.id": 1674270, "system.process.memory.used.bytes": 19529728, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 12 minutes 45 seconds", "system.process.started.time.seconds": 2347965, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674271, "system.process.memory.used.bytes": 123207680, "system.process.memory.used.percent": 0.9, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 12 minutes 45 seconds", "system.process.started.time.seconds": 2347965, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229064704}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 1674272, "system.process.memory.used.bytes": 8982528, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 12 minutes 45 seconds", "system.process.started.time.seconds": 2347965, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228753408}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 64, "system.process.id": 1674270, "system.process.memory.used.bytes": 19529728, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 17 minutes 45 seconds", "system.process.started.time.seconds": 2348265, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674271, "system.process.memory.used.bytes": 123207680, "system.process.memory.used.percent": 0.9, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 17 minutes 45 seconds", "system.process.started.time.seconds": 2348265, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229064704}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 1674272, "system.process.memory.used.bytes": 8982528, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 17 minutes 45 seconds", "system.process.started.time.seconds": 2348265, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228753408}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674274, "system.process.memory.used.bytes": 9244672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 17 minutes 45 seconds", "system.process.started.time.seconds": 2348265, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674275, "system.process.memory.used.bytes": 9900032, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 17 minutes 45 seconds", "system.process.started.time.seconds": 2348265, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674276, "system.process.memory.used.bytes": 7278592, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 17 minutes 45 seconds", "system.process.started.time.seconds": 2348265, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230215680}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674276, "system.process.memory.used.bytes": 7278592, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 22 minutes 47 seconds", "system.process.started.time.seconds": 2348567, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230215680}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 64, "system.process.id": 1674270, "system.process.memory.used.bytes": 19529728, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 22 minutes 47 seconds", "system.process.started.time.seconds": 2348567, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674271, "system.process.memory.used.bytes": 123207680, "system.process.memory.used.percent": 0.9, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 22 minutes 47 seconds", "system.process.started.time.seconds": 2348567, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229064704}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 1674272, "system.process.memory.used.bytes": 8982528, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 22 minutes 47 seconds", "system.process.started.time.seconds": 2348567, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228753408}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674274, "system.process.memory.used.bytes": 9244672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 22 minutes 47 seconds", "system.process.started.time.seconds": 2348567, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674275, "system.process.memory.used.bytes": 9900032, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 22 minutes 47 seconds", "system.process.started.time.seconds": 2348567, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 1674272, "system.process.memory.used.bytes": 8982528, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 27 minutes 45 seconds", "system.process.started.time.seconds": 2348865, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228753408}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674274, "system.process.memory.used.bytes": 9244672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 27 minutes 45 seconds", "system.process.started.time.seconds": 2348865, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674275, "system.process.memory.used.bytes": 9900032, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 27 minutes 45 seconds", "system.process.started.time.seconds": 2348865, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674276, "system.process.memory.used.bytes": 7278592, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 27 minutes 45 seconds", "system.process.started.time.seconds": 2348865, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230215680}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 64, "system.process.id": 1674270, "system.process.memory.used.bytes": 19529728, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 27 minutes 45 seconds", "system.process.started.time.seconds": 2348865, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674271, "system.process.memory.used.bytes": 123207680, "system.process.memory.used.percent": 0.9, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 27 minutes 45 seconds", "system.process.started.time.seconds": 2348865, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229064704}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674276, "system.process.memory.used.bytes": 7278592, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 32 minutes 53 seconds", "system.process.started.time.seconds": 2349173, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230215680}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 64, "system.process.id": 1674270, "system.process.memory.used.bytes": 19529728, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 32 minutes 53 seconds", "system.process.started.time.seconds": 2349173, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674271, "system.process.memory.used.bytes": 123207680, "system.process.memory.used.percent": 0.9, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 32 minutes 53 seconds", "system.process.started.time.seconds": 2349173, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229064704}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 1674272, "system.process.memory.used.bytes": 8982528, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 32 minutes 53 seconds", "system.process.started.time.seconds": 2349173, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228753408}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674274, "system.process.memory.used.bytes": 9244672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 32 minutes 53 seconds", "system.process.started.time.seconds": 2349173, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674275, "system.process.memory.used.bytes": 9900032, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 32 minutes 53 seconds", "system.process.started.time.seconds": 2349173, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674276, "system.process.memory.used.bytes": 7278592, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 37 minutes 45 seconds", "system.process.started.time.seconds": 2349465, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230215680}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 64, "system.process.id": 1674270, "system.process.memory.used.bytes": 19529728, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 37 minutes 45 seconds", "system.process.started.time.seconds": 2349465, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674271, "system.process.memory.used.bytes": 123207680, "system.process.memory.used.percent": 0.9, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 37 minutes 45 seconds", "system.process.started.time.seconds": 2349465, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229064704}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 1674272, "system.process.memory.used.bytes": 8982528, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 37 minutes 45 seconds", "system.process.started.time.seconds": 2349465, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228753408}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674274, "system.process.memory.used.bytes": 9244672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 37 minutes 45 seconds", "system.process.started.time.seconds": 2349465, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674275, "system.process.memory.used.bytes": 9900032, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 37 minutes 45 seconds", "system.process.started.time.seconds": 2349465, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674271, "system.process.memory.used.bytes": 123207680, "system.process.memory.used.percent": 0.9, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 42 minutes 55 seconds", "system.process.started.time.seconds": 2349775, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229064704}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 1674272, "system.process.memory.used.bytes": 8982528, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 42 minutes 55 seconds", "system.process.started.time.seconds": 2349775, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228753408}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 1674274, "system.process.memory.used.bytes": 9244672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 42 minutes 55 seconds", "system.process.started.time.seconds": 2349775, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674275, "system.process.memory.used.bytes": 9900032, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 42 minutes 55 seconds", "system.process.started.time.seconds": 2349775, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 1674276, "system.process.memory.used.bytes": 7278592, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 42 minutes 55 seconds", "system.process.started.time.seconds": 2349775, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230215680}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 64, "system.process.id": 1674270, "system.process.memory.used.bytes": 19529728, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 4 hours 42 minutes 55 seconds", "system.process.started.time.seconds": 2349775, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228597760}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38154) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38166) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38170) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(38178) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}], "rediscovery": null}, "************": {"discovery": null, "collect": [{"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.command": "/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.cpu.percent": 1.15, "system.process.id": 908, "system.process.memory.used.bytes": 128843776, "system.process.memory.used.percent": 6.6, "system.process.name": "beam.smp", "system.process.started.time": " 149 days 3 hours 45 minutes 24 seconds", "system.process.started.time.seconds": 12887124, "system.process.threads": 73, "system.process.user": "rabbitmq", "system.process.virtual.memory.bytes": 2700292096}, {"status": "Up", "system.process": "named|/usr/sbin/named -u named -c /etc/named.conf", "system.process.command": "/usr/sbin/named -u named -c /etc/named.conf", "system.process.cpu.percent": 0, "system.process.id": 1227, "system.process.memory.used.bytes": 58601472, "system.process.memory.used.percent": 3, "system.process.name": "named", "system.process.started.time": " 149 days 3 hours 45 minutes 23 seconds", "system.process.started.time.seconds": 12887123, "system.process.threads": 7, "system.process.user": "named", "system.process.virtual.memory.bytes": 399429632}], "system.process.network.connection": [{"system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "25672"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.command": "/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.cpu.percent": 1.15, "system.process.id": 908, "system.process.memory.used.bytes": 126529536, "system.process.memory.used.percent": 6.5, "system.process.name": "beam.smp", "system.process.started.time": " 149 days 3 hours 50 minutes 24 seconds", "system.process.started.time.seconds": 12887424, "system.process.threads": 73, "system.process.user": "rabbitmq", "system.process.virtual.memory.bytes": 2708676608}, {"status": "Up", "system.process": "named|/usr/sbin/named -u named -c /etc/named.conf", "system.process.command": "/usr/sbin/named -u named -c /etc/named.conf", "system.process.cpu.percent": 0, "system.process.id": 1227, "system.process.memory.used.bytes": 58601472, "system.process.memory.used.percent": 3, "system.process.name": "named", "system.process.started.time": " 149 days 3 hours 50 minutes 23 seconds", "system.process.started.time.seconds": 12887423, "system.process.threads": 7, "system.process.user": "named", "system.process.virtual.memory.bytes": 399429632}], "system.process.network.connection": [{"system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "25672"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.command": "/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.cpu.percent": 1.15, "system.process.id": 908, "system.process.memory.used.bytes": 128966656, "system.process.memory.used.percent": 6.6, "system.process.name": "beam.smp", "system.process.started.time": " 149 days 3 hours 55 minutes 24 seconds", "system.process.started.time.seconds": 12887724, "system.process.threads": 73, "system.process.user": "rabbitmq", "system.process.virtual.memory.bytes": 2699505664}, {"status": "Up", "system.process": "named|/usr/sbin/named -u named -c /etc/named.conf", "system.process.command": "/usr/sbin/named -u named -c /etc/named.conf", "system.process.cpu.percent": 0, "system.process.id": 1227, "system.process.memory.used.bytes": 58601472, "system.process.memory.used.percent": 3, "system.process.name": "named", "system.process.started.time": " 149 days 3 hours 55 minutes 23 seconds", "system.process.started.time.seconds": 12887723, "system.process.threads": 7, "system.process.user": "named", "system.process.virtual.memory.bytes": 399429632}], "system.process.network.connection": [{"system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "25672"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.command": "/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.cpu.percent": 1.15, "system.process.id": 908, "system.process.memory.used.bytes": 126181376, "system.process.memory.used.percent": 6.5, "system.process.name": "beam.smp", "system.process.started.time": " 149 days 4 hours 0 minute 26 seconds", "system.process.started.time.seconds": 12888026, "system.process.threads": 73, "system.process.user": "rabbitmq", "system.process.virtual.memory.bytes": 2701422592}, {"status": "Up", "system.process": "named|/usr/sbin/named -u named -c /etc/named.conf", "system.process.command": "/usr/sbin/named -u named -c /etc/named.conf", "system.process.cpu.percent": 0, "system.process.id": 1227, "system.process.memory.used.bytes": 58601472, "system.process.memory.used.percent": 3, "system.process.name": "named", "system.process.started.time": " 149 days 4 hours 0 minute 25 seconds", "system.process.started.time.seconds": 12888025, "system.process.threads": 7, "system.process.user": "named", "system.process.virtual.memory.bytes": 399429632}], "system.process.network.connection": [{"system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "25672"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.command": "/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.cpu.percent": 1.15, "system.process.id": 908, "system.process.memory.used.bytes": 122650624, "system.process.memory.used.percent": 6.3, "system.process.name": "beam.smp", "system.process.started.time": " 149 days 4 hours 5 minutes 24 seconds", "system.process.started.time.seconds": 12888324, "system.process.threads": 73, "system.process.user": "rabbitmq", "system.process.virtual.memory.bytes": 2708025344}, {"status": "Up", "system.process": "named|/usr/sbin/named -u named -c /etc/named.conf", "system.process.command": "/usr/sbin/named -u named -c /etc/named.conf", "system.process.cpu.percent": 0, "system.process.id": 1227, "system.process.memory.used.bytes": 58601472, "system.process.memory.used.percent": 3, "system.process.name": "named", "system.process.started.time": " 149 days 4 hours 5 minutes 23 seconds", "system.process.started.time.seconds": 12888323, "system.process.threads": 7, "system.process.user": "named", "system.process.virtual.memory.bytes": 399429632}], "system.process.network.connection": [{"system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "25672"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.command": "/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.cpu.percent": 1.15, "system.process.id": 908, "system.process.memory.used.bytes": 128024576, "system.process.memory.used.percent": 6.6, "system.process.name": "beam.smp", "system.process.started.time": " 149 days 4 hours 10 minutes 32 seconds", "system.process.started.time.seconds": 12888632, "system.process.threads": 73, "system.process.user": "rabbitmq", "system.process.virtual.memory.bytes": 2698706944}, {"status": "Up", "system.process": "named|/usr/sbin/named -u named -c /etc/named.conf", "system.process.command": "/usr/sbin/named -u named -c /etc/named.conf", "system.process.cpu.percent": 0, "system.process.id": 1227, "system.process.memory.used.bytes": 58601472, "system.process.memory.used.percent": 3, "system.process.name": "named", "system.process.started.time": " 149 days 4 hours 10 minutes 31 seconds", "system.process.started.time.seconds": 12888631, "system.process.threads": 7, "system.process.user": "named", "system.process.virtual.memory.bytes": 399429632}], "system.process.network.connection": [{"system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "25672"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.command": "/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.cpu.percent": 1.15, "system.process.id": 908, "system.process.memory.used.bytes": 131018752, "system.process.memory.used.percent": 6.7, "system.process.name": "beam.smp", "system.process.started.time": " 149 days 4 hours 15 minutes 24 seconds", "system.process.started.time.seconds": 12888924, "system.process.threads": 73, "system.process.user": "rabbitmq", "system.process.virtual.memory.bytes": 2703790080}, {"status": "Up", "system.process": "named|/usr/sbin/named -u named -c /etc/named.conf", "system.process.command": "/usr/sbin/named -u named -c /etc/named.conf", "system.process.cpu.percent": 0, "system.process.id": 1227, "system.process.memory.used.bytes": 58601472, "system.process.memory.used.percent": 3, "system.process.name": "named", "system.process.started.time": " 149 days 4 hours 15 minutes 23 seconds", "system.process.started.time.seconds": 12888923, "system.process.threads": 7, "system.process.user": "named", "system.process.virtual.memory.bytes": 399429632}], "system.process.network.connection": [{"system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "25672"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.command": "/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.cpu.percent": 1.15, "system.process.id": 908, "system.process.memory.used.bytes": 125739008, "system.process.memory.used.percent": 6.5, "system.process.name": "beam.smp", "system.process.started.time": " 149 days 4 hours 20 minutes 33 seconds", "system.process.started.time.seconds": 12889233, "system.process.threads": 73, "system.process.user": "rabbitmq", "system.process.virtual.memory.bytes": 2692079616}, {"status": "Up", "system.process": "named|/usr/sbin/named -u named -c /etc/named.conf", "system.process.command": "/usr/sbin/named -u named -c /etc/named.conf", "system.process.cpu.percent": 0, "system.process.id": 1227, "system.process.memory.used.bytes": 58601472, "system.process.memory.used.percent": 3, "system.process.name": "named", "system.process.started.time": " 149 days 4 hours 20 minutes 32 seconds", "system.process.started.time.seconds": 12889232, "system.process.threads": 7, "system.process.user": "named", "system.process.virtual.memory.bytes": 399429632}], "system.process.network.connection": [{"system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "25672"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.command": "/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.cpu.percent": 1.15, "system.process.id": 908, "system.process.memory.used.bytes": 127307776, "system.process.memory.used.percent": 6.6, "system.process.name": "beam.smp", "system.process.started.time": " 149 days 4 hours 28 minutes 31 seconds", "system.process.started.time.seconds": 12889711, "system.process.threads": 73, "system.process.user": "rabbitmq", "system.process.virtual.memory.bytes": 2699759616}, {"status": "Up", "system.process": "named|/usr/sbin/named -u named -c /etc/named.conf", "system.process.command": "/usr/sbin/named -u named -c /etc/named.conf", "system.process.cpu.percent": 0, "system.process.id": 1227, "system.process.memory.used.bytes": 58601472, "system.process.memory.used.percent": 3, "system.process.name": "named", "system.process.started.time": " 149 days 4 hours 28 minutes 30 seconds", "system.process.started.time.seconds": 12889710, "system.process.threads": 7, "system.process.user": "named", "system.process.virtual.memory.bytes": 399429632}], "system.process.network.connection": [{"system.process": "beam.smp|/usr/lib64/erlang/erts-5.10.4/bin/beam.smp -W w -A 64 -P 1048576 -t 5000000 -stbt db -zdbbl 32000 -K true -- -root /usr/lib64/erlang -progname erl -- -home /var/lib/rabbitmq -- -pa /usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/ebin -noshell -noinput -s rabbit boot -sname rabbit@rhel-7 -boot start_sasl -kernel inet_default_connect_options [{nodelay,true}] -sasl errlog_type error -sasl sasl_error_logger false -rabbit error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit sasl_error_logger {file,\"/var/log/rabbitmq/<EMAIL>\"} -rabbit enabled_plugins_file \"/etc/rabbitmq/enabled_plugins\" -rabbit plugins_dir \"/usr/lib/rabbitmq/plugins:/usr/lib/rabbitmq/lib/rabbitmq_server-3.6.9/plugins\" -rabbit plugins_expand_dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7-plugins-expand\" -os_mon start_cpu_sup false -os_mon start_disksup false -os_mon start_memsup false -mnesia dir \"/var/lib/rabbitmq/mnesia/rabbit@rhel-7\" -kernel inet_dist_listen_min 25672 -kernel inet_dist_listen_max 25672", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "25672"}]}], "rediscovery": null}, "************": {"discovery": null, "collect": [{"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915592, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 4 minutes 33 seconds", "system.process.started.time.seconds": 2365473, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915593, "system.process.memory.used.bytes": 5779456, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 4 minutes 33 seconds", "system.process.started.time.seconds": 2365473, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230207488}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 66, "system.process.id": 915584, "system.process.memory.used.bytes": 18350080, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 4 minutes 33 seconds", "system.process.started.time.seconds": 2365473, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logger", "system.process.command": "postgres: 16/main: logger", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915585, "system.process.memory.used.bytes": 4313088, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 4 minutes 33 seconds", "system.process.started.time.seconds": 2365473, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 79015936}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915586, "system.process.memory.used.bytes": 69349376, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 4 minutes 33 seconds", "system.process.started.time.seconds": 2365473, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229076992}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915587, "system.process.memory.used.bytes": 10760192, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 4 minutes 33 seconds", "system.process.started.time.seconds": 2365473, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228745216}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915591, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 4 minutes 33 seconds", "system.process.started.time.seconds": 2365473, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Down", "system.process": "sudo|sudo -u postgres psql", "system.process.command": "sudo -u postgres psql", "system.process.name": "sudo"}, {"status": "Down", "system.process": "psql|/usr/lib/postgresql/16/bin/psql", "system.process.command": "/usr/lib/postgresql/16/bin/psql", "system.process.name": "psql"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915591, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 9 minutes 33 seconds", "system.process.started.time.seconds": 2365773, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915592, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 9 minutes 33 seconds", "system.process.started.time.seconds": 2365773, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915593, "system.process.memory.used.bytes": 5779456, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 9 minutes 33 seconds", "system.process.started.time.seconds": 2365773, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230207488}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 66, "system.process.id": 915584, "system.process.memory.used.bytes": 18350080, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 9 minutes 33 seconds", "system.process.started.time.seconds": 2365773, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logger", "system.process.command": "postgres: 16/main: logger", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915585, "system.process.memory.used.bytes": 4313088, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 9 minutes 33 seconds", "system.process.started.time.seconds": 2365773, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 79015936}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915586, "system.process.memory.used.bytes": 69349376, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 9 minutes 33 seconds", "system.process.started.time.seconds": 2365773, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229076992}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915587, "system.process.memory.used.bytes": 10760192, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 9 minutes 33 seconds", "system.process.started.time.seconds": 2365773, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228745216}, {"status": "Down", "system.process": "sudo|sudo -u postgres psql", "system.process.command": "sudo -u postgres psql", "system.process.name": "sudo"}, {"status": "Down", "system.process": "psql|/usr/lib/postgresql/16/bin/psql", "system.process.command": "/usr/lib/postgresql/16/bin/psql", "system.process.name": "psql"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915587, "system.process.memory.used.bytes": 10760192, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 14 minutes 33 seconds", "system.process.started.time.seconds": 2366073, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228745216}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915591, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 14 minutes 33 seconds", "system.process.started.time.seconds": 2366073, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915592, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 14 minutes 33 seconds", "system.process.started.time.seconds": 2366073, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915593, "system.process.memory.used.bytes": 5779456, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 14 minutes 33 seconds", "system.process.started.time.seconds": 2366073, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230207488}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 66, "system.process.id": 915584, "system.process.memory.used.bytes": 18350080, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 14 minutes 33 seconds", "system.process.started.time.seconds": 2366073, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logger", "system.process.command": "postgres: 16/main: logger", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915585, "system.process.memory.used.bytes": 4313088, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 14 minutes 33 seconds", "system.process.started.time.seconds": 2366073, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 79015936}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915586, "system.process.memory.used.bytes": 69349376, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 14 minutes 33 seconds", "system.process.started.time.seconds": 2366073, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229076992}, {"status": "Down", "system.process": "sudo|sudo -u postgres psql", "system.process.command": "sudo -u postgres psql", "system.process.name": "sudo"}, {"status": "Down", "system.process": "psql|/usr/lib/postgresql/16/bin/psql", "system.process.command": "/usr/lib/postgresql/16/bin/psql", "system.process.name": "psql"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: logger", "system.process.command": "postgres: 16/main: logger", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915585, "system.process.memory.used.bytes": 4313088, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 19 minutes 35 seconds", "system.process.started.time.seconds": 2366375, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 79015936}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915586, "system.process.memory.used.bytes": 69349376, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 19 minutes 35 seconds", "system.process.started.time.seconds": 2366375, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229076992}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915587, "system.process.memory.used.bytes": 10760192, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 19 minutes 35 seconds", "system.process.started.time.seconds": 2366375, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228745216}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915591, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 19 minutes 35 seconds", "system.process.started.time.seconds": 2366375, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915592, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 19 minutes 35 seconds", "system.process.started.time.seconds": 2366375, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915593, "system.process.memory.used.bytes": 5779456, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 19 minutes 35 seconds", "system.process.started.time.seconds": 2366375, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230207488}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 66, "system.process.id": 915584, "system.process.memory.used.bytes": 18350080, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 19 minutes 35 seconds", "system.process.started.time.seconds": 2366375, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Down", "system.process": "sudo|sudo -u postgres psql", "system.process.command": "sudo -u postgres psql", "system.process.name": "sudo"}, {"status": "Down", "system.process": "psql|/usr/lib/postgresql/16/bin/psql", "system.process.command": "/usr/lib/postgresql/16/bin/psql", "system.process.name": "psql"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 66, "system.process.id": 915584, "system.process.memory.used.bytes": 18350080, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 24 minutes 33 seconds", "system.process.started.time.seconds": 2366673, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logger", "system.process.command": "postgres: 16/main: logger", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915585, "system.process.memory.used.bytes": 4313088, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 24 minutes 33 seconds", "system.process.started.time.seconds": 2366673, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 79015936}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915586, "system.process.memory.used.bytes": 69349376, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 24 minutes 33 seconds", "system.process.started.time.seconds": 2366673, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229076992}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915587, "system.process.memory.used.bytes": 10760192, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 24 minutes 33 seconds", "system.process.started.time.seconds": 2366673, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228745216}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915591, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 24 minutes 33 seconds", "system.process.started.time.seconds": 2366673, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915592, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 24 minutes 33 seconds", "system.process.started.time.seconds": 2366673, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915593, "system.process.memory.used.bytes": 5779456, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 24 minutes 33 seconds", "system.process.started.time.seconds": 2366673, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230207488}, {"status": "Down", "system.process": "sudo|sudo -u postgres psql", "system.process.command": "sudo -u postgres psql", "system.process.name": "sudo"}, {"status": "Down", "system.process": "psql|/usr/lib/postgresql/16/bin/psql", "system.process.command": "/usr/lib/postgresql/16/bin/psql", "system.process.name": "psql"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915587, "system.process.memory.used.bytes": 10760192, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 29 minutes 41 seconds", "system.process.started.time.seconds": 2366981, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228745216}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915591, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 29 minutes 41 seconds", "system.process.started.time.seconds": 2366981, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915592, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 29 minutes 41 seconds", "system.process.started.time.seconds": 2366981, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915593, "system.process.memory.used.bytes": 5779456, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 29 minutes 41 seconds", "system.process.started.time.seconds": 2366981, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230207488}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 66, "system.process.id": 915584, "system.process.memory.used.bytes": 18350080, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 29 minutes 41 seconds", "system.process.started.time.seconds": 2366981, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logger", "system.process.command": "postgres: 16/main: logger", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915585, "system.process.memory.used.bytes": 4313088, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 29 minutes 41 seconds", "system.process.started.time.seconds": 2366981, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 79015936}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915586, "system.process.memory.used.bytes": 69349376, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 29 minutes 41 seconds", "system.process.started.time.seconds": 2366981, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229076992}, {"status": "Down", "system.process": "sudo|sudo -u postgres psql", "system.process.command": "sudo -u postgres psql", "system.process.name": "sudo"}, {"status": "Down", "system.process": "psql|/usr/lib/postgresql/16/bin/psql", "system.process.command": "/usr/lib/postgresql/16/bin/psql", "system.process.name": "psql"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915586, "system.process.memory.used.bytes": 69349376, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 34 minutes 33 seconds", "system.process.started.time.seconds": 2367273, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229076992}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915587, "system.process.memory.used.bytes": 10760192, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 34 minutes 33 seconds", "system.process.started.time.seconds": 2367273, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228745216}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915591, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 34 minutes 33 seconds", "system.process.started.time.seconds": 2367273, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915592, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 34 minutes 33 seconds", "system.process.started.time.seconds": 2367273, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915593, "system.process.memory.used.bytes": 5779456, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 34 minutes 33 seconds", "system.process.started.time.seconds": 2367273, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230207488}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 66, "system.process.id": 915584, "system.process.memory.used.bytes": 18350080, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 34 minutes 33 seconds", "system.process.started.time.seconds": 2367273, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logger", "system.process.command": "postgres: 16/main: logger", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915585, "system.process.memory.used.bytes": 4313088, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 34 minutes 33 seconds", "system.process.started.time.seconds": 2367273, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 79015936}, {"status": "Down", "system.process": "sudo|sudo -u postgres psql", "system.process.command": "sudo -u postgres psql", "system.process.name": "sudo"}, {"status": "Down", "system.process": "psql|/usr/lib/postgresql/16/bin/psql", "system.process.command": "/usr/lib/postgresql/16/bin/psql", "system.process.name": "psql"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 66, "system.process.id": 915584, "system.process.memory.used.bytes": 18350080, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 39 minutes 43 seconds", "system.process.started.time.seconds": 2367583, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logger", "system.process.command": "postgres: 16/main: logger", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915585, "system.process.memory.used.bytes": 4313088, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 39 minutes 43 seconds", "system.process.started.time.seconds": 2367583, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 79015936}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915586, "system.process.memory.used.bytes": 69349376, "system.process.memory.used.percent": 0.5, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 39 minutes 43 seconds", "system.process.started.time.seconds": 2367583, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229076992}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 61, "system.process.id": 915587, "system.process.memory.used.bytes": 10760192, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 39 minutes 43 seconds", "system.process.started.time.seconds": 2367583, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228745216}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 62, "system.process.id": 915591, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 39 minutes 43 seconds", "system.process.started.time.seconds": 2367583, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228593664}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915592, "system.process.memory.used.bytes": 8925184, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 39 minutes 43 seconds", "system.process.started.time.seconds": 2367583, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230232064}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 63, "system.process.id": 915593, "system.process.memory.used.bytes": 5779456, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 39 minutes 43 seconds", "system.process.started.time.seconds": 2367583, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 230207488}, {"status": "Down", "system.process": "sudo|sudo -u postgres psql", "system.process.command": "sudo -u postgres psql", "system.process.name": "sudo"}, {"status": "Down", "system.process": "psql|/usr/lib/postgresql/16/bin/psql", "system.process.command": "/usr/lib/postgresql/16/bin/psql", "system.process.name": "psql"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(53396) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(52362) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "************", "system.process.destination.port": "*", "system.process.source.ip": "************", "system.process.source.port": "5432"}]}], "rediscovery": null}, "***********": {"discovery": null, "collect": [{"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1694, "system.process.memory.used.bytes": 3403776, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 149 days 3 hours 46 minutes 51 seconds", "system.process.started.time.seconds": 12887211, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1695, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 149 days 3 hours 46 minutes 51 seconds", "system.process.started.time.seconds": 12887211, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.id": 1701, "system.process.memory.used.bytes": 3387392, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 149 days 3 hours 46 minutes 51 seconds", "system.process.started.time.seconds": 12887211, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1702, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 149 days 3 hours 46 minutes 51 seconds", "system.process.started.time.seconds": 12887211, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8213, "system.process.memory.used.bytes": 2658304, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 21 hours 29 minutes 14 seconds", "system.process.started.time.seconds": 5520554, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36802560}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8688, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 21 hours 25 minutes 41 seconds", "system.process.started.time.seconds": 5520341, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8796, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 21 hours 25 minutes 27 seconds", "system.process.started.time.seconds": 5520327, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8688, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 21 hours 30 minutes 42 seconds", "system.process.started.time.seconds": 5520642, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8796, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 21 hours 30 minutes 28 seconds", "system.process.started.time.seconds": 5520628, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1694, "system.process.memory.used.bytes": 3403776, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 149 days 3 hours 51 minutes 52 seconds", "system.process.started.time.seconds": 12887512, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1695, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 149 days 3 hours 51 minutes 52 seconds", "system.process.started.time.seconds": 12887512, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1701, "system.process.memory.used.bytes": 3387392, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 149 days 3 hours 51 minutes 52 seconds", "system.process.started.time.seconds": 12887512, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1702, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 149 days 3 hours 51 minutes 52 seconds", "system.process.started.time.seconds": 12887512, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8213, "system.process.memory.used.bytes": 2658304, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 21 hours 34 minutes 15 seconds", "system.process.started.time.seconds": 5520855, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36802560}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8796, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 21 hours 35 minutes 27 seconds", "system.process.started.time.seconds": 5520927, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1694, "system.process.memory.used.bytes": 3403776, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 149 days 3 hours 56 minutes 51 seconds", "system.process.started.time.seconds": 12887811, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1695, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 149 days 3 hours 56 minutes 51 seconds", "system.process.started.time.seconds": 12887811, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1701, "system.process.memory.used.bytes": 3387392, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 149 days 3 hours 56 minutes 51 seconds", "system.process.started.time.seconds": 12887811, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1702, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 149 days 3 hours 56 minutes 51 seconds", "system.process.started.time.seconds": 12887811, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8213, "system.process.memory.used.bytes": 2658304, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 21 hours 39 minutes 14 seconds", "system.process.started.time.seconds": 5521154, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36802560}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8688, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 21 hours 35 minutes 41 seconds", "system.process.started.time.seconds": 5520941, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8796, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 21 hours 40 minutes 30 seconds", "system.process.started.time.seconds": 5521230, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.id": 1694, "system.process.memory.used.bytes": 3403776, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 1 minutes 54 seconds", "system.process.started.time.seconds": 12888114, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1695, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 1 minutes 54 seconds", "system.process.started.time.seconds": 12888114, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1701, "system.process.memory.used.bytes": 3387392, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 1 minutes 54 seconds", "system.process.started.time.seconds": 12888114, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1702, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 1 minutes 54 seconds", "system.process.started.time.seconds": 12888114, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8213, "system.process.memory.used.bytes": 2658304, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 21 hours 44 minutes 17 seconds", "system.process.started.time.seconds": 5521457, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36802560}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8688, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 21 hours 40 minutes 44 seconds", "system.process.started.time.seconds": 5521244, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1701, "system.process.memory.used.bytes": 3387392, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 6 minutes 51 seconds", "system.process.started.time.seconds": 12888411, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1702, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 6 minutes 51 seconds", "system.process.started.time.seconds": 12888411, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8213, "system.process.memory.used.bytes": 2658304, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 21 hours 49 minutes 14 seconds", "system.process.started.time.seconds": 5521754, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36802560}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8688, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 21 hours 45 minutes 41 seconds", "system.process.started.time.seconds": 5521541, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8796, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 21 hours 45 minutes 27 seconds", "system.process.started.time.seconds": 5521527, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1694, "system.process.memory.used.bytes": 3403776, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 6 minutes 51 seconds", "system.process.started.time.seconds": 12888411, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1695, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 6 minutes 51 seconds", "system.process.started.time.seconds": 12888411, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8688, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 21 hours 50 minutes 50 seconds", "system.process.started.time.seconds": 5521850, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8796, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 21 hours 50 minutes 36 seconds", "system.process.started.time.seconds": 5521836, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1694, "system.process.memory.used.bytes": 3403776, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 12 minutes 0 second", "system.process.started.time.seconds": 12888720, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1695, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 12 minutes 0 second", "system.process.started.time.seconds": 12888720, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1701, "system.process.memory.used.bytes": 3387392, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 12 minutes 0 second", "system.process.started.time.seconds": 12888720, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1702, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 12 minutes 0 second", "system.process.started.time.seconds": 12888720, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8213, "system.process.memory.used.bytes": 2658304, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 21 hours 54 minutes 23 seconds", "system.process.started.time.seconds": 5522063, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36802560}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8213, "system.process.memory.used.bytes": 2658304, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 21 hours 59 minutes 14 seconds", "system.process.started.time.seconds": 5522354, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36802560}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8688, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 21 hours 55 minutes 41 seconds", "system.process.started.time.seconds": 5522141, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8796, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 21 hours 55 minutes 27 seconds", "system.process.started.time.seconds": 5522127, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1694, "system.process.memory.used.bytes": 3403776, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 16 minutes 51 seconds", "system.process.started.time.seconds": 12889011, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1695, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 16 minutes 51 seconds", "system.process.started.time.seconds": 12889011, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1701, "system.process.memory.used.bytes": 3387392, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 16 minutes 51 seconds", "system.process.started.time.seconds": 12889011, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1702, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 16 minutes 51 seconds", "system.process.started.time.seconds": 12889011, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8213, "system.process.memory.used.bytes": 2658304, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 22 hours 4 minutes 24 seconds", "system.process.started.time.seconds": 5522664, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36802560}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8688, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 22 hours 0 minute 51 seconds", "system.process.started.time.seconds": 5522451, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8796, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 22 hours 0 minute 37 seconds", "system.process.started.time.seconds": 5522437, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1694, "system.process.memory.used.bytes": 3403776, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 22 minutes 1 seconds", "system.process.started.time.seconds": 12889321, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1695, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 22 minutes 1 seconds", "system.process.started.time.seconds": 12889321, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1701, "system.process.memory.used.bytes": 3387392, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 22 minutes 1 seconds", "system.process.started.time.seconds": 12889321, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1702, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 22 minutes 1 seconds", "system.process.started.time.seconds": 12889321, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8688, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 22 hours 8 minutes 48 seconds", "system.process.started.time.seconds": 5522928, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8796, "system.process.memory.used.bytes": 2682880, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 22 hours 8 minutes 34 seconds", "system.process.started.time.seconds": 5522914, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 37703680}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1694, "system.process.memory.used.bytes": 3403776, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 29 minutes 58 seconds", "system.process.started.time.seconds": 12889798, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.command": "/opt/sdb/MaxDB/pgm/vserver -sdbstarter 1001 1001 A -S 7200 -Y", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1695, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 29 minutes 58 seconds", "system.process.started.time.seconds": 12889798, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1701, "system.process.memory.used.bytes": 3387392, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 29 minutes 58 seconds", "system.process.started.time.seconds": 12889798, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36646912}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 1702, "system.process.memory.used.bytes": 167936, "system.process.memory.used.percent": 0, "system.process.name": "vserver", "system.process.started.time": " 149 days 4 hours 29 minutes 58 seconds", "system.process.started.time.seconds": 12889798, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 32313344}, {"status": "Up", "system.process": "vserver|sdbgloballistener start -m", "system.process.command": "sdbgloballistener start -m", "system.process.cpu.percent": 0, "system.process.handles": 0, "system.process.id": 8213, "system.process.memory.used.bytes": 2658304, "system.process.memory.used.percent": 0.1, "system.process.name": "vserver", "system.process.started.time": " 63 days 22 hours 12 minutes 21 seconds", "system.process.started.time.seconds": 5523141, "system.process.threads": 1, "system.process.user": "sdb", "system.process.virtual.memory.bytes": 36802560}]}], "rediscovery": null}, "***********": {"discovery": null, "collect": [{"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "nginx|nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.command": "nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.cpu.percent": 0, "system.process.handles": 34, "system.process.id": 1412598, "system.process.memory.used.bytes": 1626112, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 22 minutes 38 seconds", "system.process.started.time.seconds": 2366558, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 11423744}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412605, "system.process.memory.used.bytes": 3198976, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 22 minutes 38 seconds", "system.process.started.time.seconds": 2366558, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412599, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 22 minutes 38 seconds", "system.process.started.time.seconds": 2366558, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412600, "system.process.memory.used.bytes": 3330048, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 22 minutes 38 seconds", "system.process.started.time.seconds": 2366558, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412601, "system.process.memory.used.bytes": 4509696, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 22 minutes 38 seconds", "system.process.started.time.seconds": 2366558, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "standalo|/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.command": "/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.cpu.percent": 0, "system.process.handles": 9, "system.process.id": 1412828, "system.process.memory.used.bytes": 1835008, "system.process.memory.used.percent": 0, "system.process.name": "standalo", "system.process.started.time": " 27 days 9 hours 22 minutes 15 seconds", "system.process.started.time.seconds": 2366535, "system.process.threads": 1, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 2867200}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414374, "system.process.memory.used.bytes": 3612672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 20 minutes 2 seconds", "system.process.started.time.seconds": 2366402, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414375, "system.process.memory.used.bytes": 4005888, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 20 minutes 2 seconds", "system.process.started.time.seconds": 2366402, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229826560}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414371, "system.process.memory.used.bytes": 10166272, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 20 minutes 2 seconds", "system.process.started.time.seconds": 2366402, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229085184}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 54, "system.process.id": 1414372, "system.process.memory.used.bytes": 3481600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 20 minutes 2 seconds", "system.process.started.time.seconds": 2366402, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414376, "system.process.memory.used.bytes": 3481600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 20 minutes 2 seconds", "system.process.started.time.seconds": 2366402, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229806080}, {"status": "Up", "system.process": "java|java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.command": "java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.cpu.percent": 0.0125, "system.process.handles": 569, "system.process.id": 1412943, "system.process.memory.used.bytes": 110759936, "system.process.memory.used.percent": 1.3, "system.process.name": "java", "system.process.started.time": " 27 days 9 hours 22 minutes 15 seconds", "system.process.started.time.seconds": 2366535, "system.process.threads": 73, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 1544192000}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 58, "system.process.id": 1414370, "system.process.memory.used.bytes": 8650752, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 20 minutes 2 seconds", "system.process.started.time.seconds": 2366402, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228179968}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412602, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 22 minutes 38 seconds", "system.process.started.time.seconds": 2366558, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412603, "system.process.memory.used.bytes": 3067904, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 22 minutes 38 seconds", "system.process.started.time.seconds": 2366558, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412604, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 22 minutes 38 seconds", "system.process.started.time.seconds": 2366558, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412606, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 22 minutes 38 seconds", "system.process.started.time.seconds": 2366558, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "***********", "system.process.destination.port": "*", "system.process.source.ip": "***********", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412599, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 27 minutes 38 seconds", "system.process.started.time.seconds": 2366858, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412601, "system.process.memory.used.bytes": 4509696, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 27 minutes 38 seconds", "system.process.started.time.seconds": 2366858, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412602, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 27 minutes 38 seconds", "system.process.started.time.seconds": 2366858, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412605, "system.process.memory.used.bytes": 3198976, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 27 minutes 38 seconds", "system.process.started.time.seconds": 2366858, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "standalo|/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.command": "/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.cpu.percent": 0, "system.process.handles": 9, "system.process.id": 1412828, "system.process.memory.used.bytes": 1835008, "system.process.memory.used.percent": 0, "system.process.name": "standalo", "system.process.started.time": " 27 days 9 hours 27 minutes 16 seconds", "system.process.started.time.seconds": 2366836, "system.process.threads": 1, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 2867200}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 58, "system.process.id": 1414370, "system.process.memory.used.bytes": 8650752, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 25 minutes 2 seconds", "system.process.started.time.seconds": 2366702, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228179968}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414371, "system.process.memory.used.bytes": 10166272, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 25 minutes 2 seconds", "system.process.started.time.seconds": 2366702, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229085184}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414376, "system.process.memory.used.bytes": 3481600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 25 minutes 2 seconds", "system.process.started.time.seconds": 2366702, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229806080}, {"status": "Up", "system.process": "nginx|nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.command": "nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.cpu.percent": 0, "system.process.handles": 34, "system.process.id": 1412598, "system.process.memory.used.bytes": 1626112, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 27 minutes 38 seconds", "system.process.started.time.seconds": 2366858, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 11423744}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412600, "system.process.memory.used.bytes": 3330048, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 27 minutes 38 seconds", "system.process.started.time.seconds": 2366858, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412606, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 27 minutes 38 seconds", "system.process.started.time.seconds": 2366858, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "java|java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.command": "java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.cpu.percent": 0.0125, "system.process.handles": 569, "system.process.id": 1412943, "system.process.memory.used.bytes": 113512448, "system.process.memory.used.percent": 1.3, "system.process.name": "java", "system.process.started.time": " 27 days 9 hours 27 minutes 15 seconds", "system.process.started.time.seconds": 2366835, "system.process.threads": 73, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 1544192000}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 54, "system.process.id": 1414372, "system.process.memory.used.bytes": 3481600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 25 minutes 2 seconds", "system.process.started.time.seconds": 2366702, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414374, "system.process.memory.used.bytes": 3743744, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 25 minutes 2 seconds", "system.process.started.time.seconds": 2366702, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414375, "system.process.memory.used.bytes": 4005888, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 25 minutes 2 seconds", "system.process.started.time.seconds": 2366702, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229826560}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412603, "system.process.memory.used.bytes": 3067904, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 27 minutes 38 seconds", "system.process.started.time.seconds": 2366858, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412604, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 27 minutes 38 seconds", "system.process.started.time.seconds": 2366858, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "***********", "system.process.destination.port": "*", "system.process.source.ip": "***********", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412600, "system.process.memory.used.bytes": 3330048, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 32 minutes 38 seconds", "system.process.started.time.seconds": 2367158, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414371, "system.process.memory.used.bytes": 10166272, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 30 minutes 2 seconds", "system.process.started.time.seconds": 2367002, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229085184}, {"status": "Up", "system.process": "nginx|nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.command": "nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.cpu.percent": 0, "system.process.handles": 34, "system.process.id": 1412598, "system.process.memory.used.bytes": 1626112, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 32 minutes 38 seconds", "system.process.started.time.seconds": 2367158, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 11423744}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412602, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 32 minutes 38 seconds", "system.process.started.time.seconds": 2367158, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412603, "system.process.memory.used.bytes": 3067904, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 32 minutes 38 seconds", "system.process.started.time.seconds": 2367158, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412606, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 32 minutes 38 seconds", "system.process.started.time.seconds": 2367158, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "standalo|/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.command": "/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.cpu.percent": 0, "system.process.handles": 9, "system.process.id": 1412828, "system.process.memory.used.bytes": 1835008, "system.process.memory.used.percent": 0, "system.process.name": "standalo", "system.process.started.time": " 27 days 9 hours 32 minutes 15 seconds", "system.process.started.time.seconds": 2367135, "system.process.threads": 1, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 2867200}, {"status": "Up", "system.process": "java|java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.command": "java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.cpu.percent": 0.0125, "system.process.handles": 569, "system.process.id": 1412943, "system.process.memory.used.bytes": 117837824, "system.process.memory.used.percent": 1.4, "system.process.name": "java", "system.process.started.time": " 27 days 9 hours 32 minutes 15 seconds", "system.process.started.time.seconds": 2367135, "system.process.threads": 73, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 1544192000}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 58, "system.process.id": 1414370, "system.process.memory.used.bytes": 8650752, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 30 minutes 2 seconds", "system.process.started.time.seconds": 2367002, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228179968}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414375, "system.process.memory.used.bytes": 4005888, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 30 minutes 2 seconds", "system.process.started.time.seconds": 2367002, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229826560}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414376, "system.process.memory.used.bytes": 3481600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 30 minutes 2 seconds", "system.process.started.time.seconds": 2367002, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229806080}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412599, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 32 minutes 38 seconds", "system.process.started.time.seconds": 2367158, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412601, "system.process.memory.used.bytes": 4509696, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 32 minutes 38 seconds", "system.process.started.time.seconds": 2367158, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412604, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 32 minutes 38 seconds", "system.process.started.time.seconds": 2367158, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412605, "system.process.memory.used.bytes": 3198976, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 32 minutes 38 seconds", "system.process.started.time.seconds": 2367158, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 54, "system.process.id": 1414372, "system.process.memory.used.bytes": 3481600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 30 minutes 2 seconds", "system.process.started.time.seconds": 2367002, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414374, "system.process.memory.used.bytes": 3743744, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 30 minutes 2 seconds", "system.process.started.time.seconds": 2367002, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "***********", "system.process.destination.port": "*", "system.process.source.ip": "***********", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "nginx|nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.command": "nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.cpu.percent": 0, "system.process.handles": 34, "system.process.id": 1412598, "system.process.memory.used.bytes": 1626112, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 37 minutes 40 seconds", "system.process.started.time.seconds": 2367460, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 11423744}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412599, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 37 minutes 40 seconds", "system.process.started.time.seconds": 2367460, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "standalo|/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.command": "/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.cpu.percent": 0, "system.process.handles": 9, "system.process.id": 1412828, "system.process.memory.used.bytes": 1835008, "system.process.memory.used.percent": 0, "system.process.name": "standalo", "system.process.started.time": " 27 days 9 hours 37 minutes 18 seconds", "system.process.started.time.seconds": 2367438, "system.process.threads": 1, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 2867200}, {"status": "Up", "system.process": "java|java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.command": "java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.cpu.percent": 0.0125, "system.process.handles": 569, "system.process.id": 1412943, "system.process.memory.used.bytes": 112857088, "system.process.memory.used.percent": 1.3, "system.process.name": "java", "system.process.started.time": " 27 days 9 hours 37 minutes 17 seconds", "system.process.started.time.seconds": 2367437, "system.process.threads": 73, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 1544192000}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414371, "system.process.memory.used.bytes": 10166272, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 35 minutes 4 seconds", "system.process.started.time.seconds": 2367304, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229085184}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412602, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 37 minutes 40 seconds", "system.process.started.time.seconds": 2367460, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412603, "system.process.memory.used.bytes": 3067904, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 37 minutes 40 seconds", "system.process.started.time.seconds": 2367460, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412604, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 37 minutes 40 seconds", "system.process.started.time.seconds": 2367460, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412605, "system.process.memory.used.bytes": 3198976, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 37 minutes 40 seconds", "system.process.started.time.seconds": 2367460, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412600, "system.process.memory.used.bytes": 3330048, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 37 minutes 40 seconds", "system.process.started.time.seconds": 2367460, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412601, "system.process.memory.used.bytes": 4509696, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 37 minutes 40 seconds", "system.process.started.time.seconds": 2367460, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 58, "system.process.id": 1414370, "system.process.memory.used.bytes": 8650752, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 35 minutes 4 seconds", "system.process.started.time.seconds": 2367304, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228179968}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 54, "system.process.id": 1414372, "system.process.memory.used.bytes": 3481600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 35 minutes 4 seconds", "system.process.started.time.seconds": 2367304, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414374, "system.process.memory.used.bytes": 3743744, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 35 minutes 4 seconds", "system.process.started.time.seconds": 2367304, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414375, "system.process.memory.used.bytes": 4005888, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 35 minutes 4 seconds", "system.process.started.time.seconds": 2367304, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229826560}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412606, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 37 minutes 40 seconds", "system.process.started.time.seconds": 2367460, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414376, "system.process.memory.used.bytes": 3481600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 35 minutes 4 seconds", "system.process.started.time.seconds": 2367304, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229806080}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "***********", "system.process.destination.port": "*", "system.process.source.ip": "***********", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414371, "system.process.memory.used.bytes": 9510912, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 40 minutes 2 seconds", "system.process.started.time.seconds": 2367602, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229085184}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414375, "system.process.memory.used.bytes": 4005888, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 40 minutes 2 seconds", "system.process.started.time.seconds": 2367602, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229826560}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412602, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 42 minutes 38 seconds", "system.process.started.time.seconds": 2367758, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412604, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 42 minutes 38 seconds", "system.process.started.time.seconds": 2367758, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412605, "system.process.memory.used.bytes": 3198976, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 42 minutes 38 seconds", "system.process.started.time.seconds": 2367758, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 54, "system.process.id": 1414372, "system.process.memory.used.bytes": 3481600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 40 minutes 2 seconds", "system.process.started.time.seconds": 2367602, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414374, "system.process.memory.used.bytes": 3743744, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 40 minutes 2 seconds", "system.process.started.time.seconds": 2367602, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412599, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 42 minutes 38 seconds", "system.process.started.time.seconds": 2367758, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412600, "system.process.memory.used.bytes": 3330048, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 42 minutes 38 seconds", "system.process.started.time.seconds": 2367758, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412603, "system.process.memory.used.bytes": 3067904, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 42 minutes 38 seconds", "system.process.started.time.seconds": 2367758, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414376, "system.process.memory.used.bytes": 3481600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 40 minutes 2 seconds", "system.process.started.time.seconds": 2367602, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229806080}, {"status": "Up", "system.process": "nginx|nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.command": "nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.cpu.percent": 0, "system.process.handles": 34, "system.process.id": 1412598, "system.process.memory.used.bytes": 1626112, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 42 minutes 38 seconds", "system.process.started.time.seconds": 2367758, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 11423744}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412606, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 42 minutes 38 seconds", "system.process.started.time.seconds": 2367758, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "standalo|/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.command": "/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.cpu.percent": 0, "system.process.handles": 9, "system.process.id": 1412828, "system.process.memory.used.bytes": 1835008, "system.process.memory.used.percent": 0, "system.process.name": "standalo", "system.process.started.time": " 27 days 9 hours 42 minutes 15 seconds", "system.process.started.time.seconds": 2367735, "system.process.threads": 1, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 2867200}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412601, "system.process.memory.used.bytes": 4509696, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 42 minutes 38 seconds", "system.process.started.time.seconds": 2367758, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "java|java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.command": "java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.cpu.percent": 0.0125, "system.process.handles": 569, "system.process.id": 1412943, "system.process.memory.used.bytes": 97390592, "system.process.memory.used.percent": 1.1, "system.process.name": "java", "system.process.started.time": " 27 days 9 hours 42 minutes 15 seconds", "system.process.started.time.seconds": 2367735, "system.process.threads": 73, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 1544192000}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 58, "system.process.id": 1414370, "system.process.memory.used.bytes": 8650752, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 40 minutes 2 seconds", "system.process.started.time.seconds": 2367602, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228179968}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "***********", "system.process.destination.port": "*", "system.process.source.ip": "***********", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "nginx|nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.command": "nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.cpu.percent": 0, "system.process.handles": 34, "system.process.id": 1412598, "system.process.memory.used.bytes": 1626112, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 47 minutes 47 seconds", "system.process.started.time.seconds": 2368067, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 11423744}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412599, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 47 minutes 47 seconds", "system.process.started.time.seconds": 2368067, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412603, "system.process.memory.used.bytes": 3067904, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 47 minutes 47 seconds", "system.process.started.time.seconds": 2368067, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414376, "system.process.memory.used.bytes": 3481600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 45 minutes 11 seconds", "system.process.started.time.seconds": 2367911, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229806080}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 54, "system.process.id": 1414372, "system.process.memory.used.bytes": 3481600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 45 minutes 11 seconds", "system.process.started.time.seconds": 2367911, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412602, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 47 minutes 47 seconds", "system.process.started.time.seconds": 2368067, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412604, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 47 minutes 47 seconds", "system.process.started.time.seconds": 2368067, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412605, "system.process.memory.used.bytes": 3198976, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 47 minutes 47 seconds", "system.process.started.time.seconds": 2368067, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "standalo|/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.command": "/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.cpu.percent": 0, "system.process.handles": 9, "system.process.id": 1412828, "system.process.memory.used.bytes": 1835008, "system.process.memory.used.percent": 0, "system.process.name": "standalo", "system.process.started.time": " 27 days 9 hours 47 minutes 24 seconds", "system.process.started.time.seconds": 2368044, "system.process.threads": 1, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 2867200}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414374, "system.process.memory.used.bytes": 3612672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 45 minutes 11 seconds", "system.process.started.time.seconds": 2367911, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412600, "system.process.memory.used.bytes": 3330048, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 47 minutes 47 seconds", "system.process.started.time.seconds": 2368067, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412601, "system.process.memory.used.bytes": 4378624, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 47 minutes 47 seconds", "system.process.started.time.seconds": 2368067, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414375, "system.process.memory.used.bytes": 4005888, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 45 minutes 11 seconds", "system.process.started.time.seconds": 2367911, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229826560}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412606, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 47 minutes 47 seconds", "system.process.started.time.seconds": 2368067, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "java|java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.command": "java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.cpu.percent": 0.0125, "system.process.handles": 569, "system.process.id": 1412943, "system.process.memory.used.bytes": 90705920, "system.process.memory.used.percent": 1, "system.process.name": "java", "system.process.started.time": " 27 days 9 hours 47 minutes 24 seconds", "system.process.started.time.seconds": 2368044, "system.process.threads": 73, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 1544192000}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 58, "system.process.id": 1414370, "system.process.memory.used.bytes": 8650752, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 45 minutes 11 seconds", "system.process.started.time.seconds": 2367911, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228179968}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414371, "system.process.memory.used.bytes": 9248768, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 45 minutes 11 seconds", "system.process.started.time.seconds": 2367911, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229085184}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "***********", "system.process.destination.port": "*", "system.process.source.ip": "***********", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412603, "system.process.memory.used.bytes": 3067904, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 52 minutes 38 seconds", "system.process.started.time.seconds": 2368358, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412606, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 52 minutes 38 seconds", "system.process.started.time.seconds": 2368358, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "standalo|/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.command": "/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.cpu.percent": 0, "system.process.handles": 9, "system.process.id": 1412828, "system.process.memory.used.bytes": 1835008, "system.process.memory.used.percent": 0, "system.process.name": "standalo", "system.process.started.time": " 27 days 9 hours 52 minutes 15 seconds", "system.process.started.time.seconds": 2368335, "system.process.threads": 1, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 2867200}, {"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414371, "system.process.memory.used.bytes": 9248768, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 50 minutes 2 seconds", "system.process.started.time.seconds": 2368202, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229085184}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412605, "system.process.memory.used.bytes": 3198976, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 52 minutes 38 seconds", "system.process.started.time.seconds": 2368358, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "java|java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.command": "java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.cpu.percent": 0.0125, "system.process.handles": 569, "system.process.id": 1412943, "system.process.memory.used.bytes": 90968064, "system.process.memory.used.percent": 1, "system.process.name": "java", "system.process.started.time": " 27 days 9 hours 52 minutes 15 seconds", "system.process.started.time.seconds": 2368335, "system.process.threads": 73, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 1544192000}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412599, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 52 minutes 38 seconds", "system.process.started.time.seconds": 2368358, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412602, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 52 minutes 38 seconds", "system.process.started.time.seconds": 2368358, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 54, "system.process.id": 1414372, "system.process.memory.used.bytes": 3481600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 50 minutes 2 seconds", "system.process.started.time.seconds": 2368202, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414374, "system.process.memory.used.bytes": 3612672, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 50 minutes 2 seconds", "system.process.started.time.seconds": 2368202, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414375, "system.process.memory.used.bytes": 4005888, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 50 minutes 2 seconds", "system.process.started.time.seconds": 2368202, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229826560}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414376, "system.process.memory.used.bytes": 3481600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 50 minutes 2 seconds", "system.process.started.time.seconds": 2368202, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229806080}, {"status": "Up", "system.process": "nginx|nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.command": "nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.cpu.percent": 0, "system.process.handles": 34, "system.process.id": 1412598, "system.process.memory.used.bytes": 1626112, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 52 minutes 38 seconds", "system.process.started.time.seconds": 2368358, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 11423744}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412600, "system.process.memory.used.bytes": 3330048, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 52 minutes 38 seconds", "system.process.started.time.seconds": 2368358, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412601, "system.process.memory.used.bytes": 4378624, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 52 minutes 38 seconds", "system.process.started.time.seconds": 2368358, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412604, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 52 minutes 38 seconds", "system.process.started.time.seconds": 2368358, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 58, "system.process.id": 1414370, "system.process.memory.used.bytes": 8650752, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 50 minutes 2 seconds", "system.process.started.time.seconds": 2368202, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228179968}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "***********", "system.process.destination.port": "*", "system.process.source.ip": "***********", "system.process.source.port": "5432"}]}, {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "postgres|postgres: 16/main: checkpointer", "system.process.command": "postgres: 16/main: checkpointer", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414371, "system.process.memory.used.bytes": 8986624, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 55 minutes 11 seconds", "system.process.started.time.seconds": 2368511, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229085184}, {"status": "Up", "system.process": "postgres|postgres: 16/main: background writer", "system.process.command": "postgres: 16/main: background writer", "system.process.cpu.percent": 0, "system.process.handles": 54, "system.process.id": 1414372, "system.process.memory.used.bytes": 3481600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 55 minutes 11 seconds", "system.process.started.time.seconds": 2368511, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412602, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 57 minutes 47 seconds", "system.process.started.time.seconds": 2368667, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412604, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 57 minutes 47 seconds", "system.process.started.time.seconds": 2368667, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412605, "system.process.memory.used.bytes": 3198976, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 57 minutes 47 seconds", "system.process.started.time.seconds": 2368667, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412606, "system.process.memory.used.bytes": 3723264, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 57 minutes 47 seconds", "system.process.started.time.seconds": 2368667, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "standalo|/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.command": "/bin/sh /opt/wildfly/bin/standalone.sh -b=0.0.0.0", "system.process.cpu.percent": 0, "system.process.handles": 9, "system.process.id": 1412828, "system.process.memory.used.bytes": 1835008, "system.process.memory.used.percent": 0, "system.process.name": "standalo", "system.process.started.time": " 27 days 9 hours 57 minutes 25 seconds", "system.process.started.time.seconds": 2368645, "system.process.threads": 1, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 2867200}, {"status": "Up", "system.process": "postgres|postgres: 16/main: walwriter", "system.process.command": "postgres: 16/main: walwriter", "system.process.cpu.percent": 0, "system.process.handles": 55, "system.process.id": 1414374, "system.process.memory.used.bytes": 3481600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 55 minutes 11 seconds", "system.process.started.time.seconds": 2368511, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228315136}, {"status": "Up", "system.process": "postgres|postgres: 16/main: logical replication launcher", "system.process.command": "postgres: 16/main: logical replication launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414376, "system.process.memory.used.bytes": 3481600, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 55 minutes 11 seconds", "system.process.started.time.seconds": 2368511, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229806080}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412599, "system.process.memory.used.bytes": 3461120, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 57 minutes 47 seconds", "system.process.started.time.seconds": 2368667, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412600, "system.process.memory.used.bytes": 3330048, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 57 minutes 47 seconds", "system.process.started.time.seconds": 2368667, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412601, "system.process.memory.used.bytes": 3854336, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 57 minutes 47 seconds", "system.process.started.time.seconds": 2368667, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "postgres|postgres: 16/main: autovacuum launcher", "system.process.command": "postgres: 16/main: autovacuum launcher", "system.process.cpu.percent": 0, "system.process.handles": 56, "system.process.id": 1414375, "system.process.memory.used.bytes": 4005888, "system.process.memory.used.percent": 0, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 55 minutes 11 seconds", "system.process.started.time.seconds": 2368511, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 229826560}, {"status": "Up", "system.process": "nginx|nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.command": "nginx: master process /usr/sbin/nginx -g daemon on; master_process on;", "system.process.cpu.percent": 0, "system.process.handles": 34, "system.process.id": 1412598, "system.process.memory.used.bytes": 1626112, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 57 minutes 47 seconds", "system.process.started.time.seconds": 2368667, "system.process.threads": 1, "system.process.user": "root", "system.process.virtual.memory.bytes": 11423744}, {"status": "Up", "system.process": "nginx|nginx: worker process", "system.process.command": "nginx: worker process", "system.process.cpu.percent": 0, "system.process.handles": 31, "system.process.id": 1412603, "system.process.memory.used.bytes": 3067904, "system.process.memory.used.percent": 0, "system.process.name": "nginx", "system.process.started.time": " 27 days 9 hours 57 minutes 47 seconds", "system.process.started.time.seconds": 2368667, "system.process.threads": 1, "system.process.user": "www-data", "system.process.virtual.memory.bytes": 13189120}, {"status": "Up", "system.process": "java|java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.command": "java -D[Standalone] -Xms64m -Xmx512m -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true -Djboss.modules.system.pkgs=org.jboss.byteman -Djava.awt.headless=true --add-exports=java.desktop/sun.awt=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldap=ALL-UNNAMED --add-exports=java.naming/com.sun.jndi.url.ldaps=ALL-UNNAMED --add-exports=jdk.naming.dns/com.sun.jndi.dns=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED -Djava.security.manager=allow -Dorg.jboss.boot.log.file=/opt/wildfly/standalone/log/server.log -Dlogging.configuration=file:/opt/wildfly/standalone/configuration/logging.properties -jar /opt/wildfly/jboss-modules.jar -mp /opt/wildfly/modules org.jboss.as.standalone -Djboss.home.dir=/opt/wildfly -Djboss.server.base.dir=/opt/wildfly/standalone -b=0.0.0.0", "system.process.cpu.percent": 0.0125, "system.process.handles": 569, "system.process.id": 1412943, "system.process.memory.used.bytes": 75632640, "system.process.memory.used.percent": 0.9, "system.process.name": "java", "system.process.started.time": " 27 days 9 hours 57 minutes 24 seconds", "system.process.started.time.seconds": 2368644, "system.process.threads": 73, "system.process.user": "j<PERSON>s", "system.process.virtual.memory.bytes": 1544192000}, {"status": "Up", "system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.command": "/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.cpu.percent": 0, "system.process.handles": 58, "system.process.id": 1414370, "system.process.memory.used.bytes": 8650752, "system.process.memory.used.percent": 0.1, "system.process.name": "postgres", "system.process.started.time": " 27 days 9 hours 55 minutes 11 seconds", "system.process.started.time.seconds": 2368511, "system.process.threads": 1, "system.process.user": "postgres", "system.process.virtual.memory.bytes": 228179968}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51172) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51180) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51196) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51198) idle", "system.process.name": "postgres"}, {"status": "Down", "system.process": "postgres|postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.command": "postgres: 16/main: motadata motadata 127.0.0.1(51200) idle", "system.process.name": "postgres"}], "system.process.network.connection": [{"system.process": "postgres|/usr/lib/postgresql/16/bin/postgres -D /var/lib/postgresql/16/main -c config_file=/etc/postgresql/16/main/postgresql.conf", "system.process.destination.ip": "***********", "system.process.destination.port": "*", "system.process.source.ip": "***********", "system.process.source.port": "5432"}]}], "rediscovery": null}}