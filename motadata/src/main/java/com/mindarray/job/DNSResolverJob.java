/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  23-Jul-2025	    Nikun<PERSON> Patel        MOTADATA-6236: initial commit.
 */


package com.mindarray.job;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.json.JsonObject;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

/**
 * Quartz job responsible for triggering the DNS server resolver logic on a scheduled interval.
 * <p>
 * The interval is determined dynamically from the configuration using
 * {@link MotadataConfigUtil#getDNSResolverTimerHours()} and is used to generate the cron expression.
 */
public class DNSResolverJob implements Job
{
    /**
     * Cron expression to schedule the job execution every N hours,
     * where N is the configured timer from {@code dns.server.resolver.timer.hours}.
     */
    public static final String DNS_RESOLVER_JOB_CRON_EXPRESSION = String.format("0 0 */%d ? * *", MotadataConfigUtil.getDNSResolverTimerHours());

    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(DNSResolverJob.class, GlobalConstants.MOTADATA_JOB, "DNS Resolver Job");

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException
    {
        try
        {
            if (CommonUtil.debugEnabled())
            {
                LOGGER.debug("Starting scheduled DNS Resolver job");
            }

            // Send event to resolve ips/hosts
            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_DNS_RESOLVER, new JsonObject());
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
