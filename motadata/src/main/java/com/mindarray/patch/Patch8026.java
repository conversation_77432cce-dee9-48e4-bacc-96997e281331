/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			    Notes
 *  014-Jul-2025	Priya<PERSON>h <PERSON><PERSON> to establish parent-child relationships between templates and their tabs
 *  06-Aug-2025	    Sankalp	            Patch to attach default policy to database group.
 */

package com.mindarray.patch;

import com.mindarray.Bootstrap;

import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.db.DBConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.store.MetricPolicyConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.PasswordPolicyConfigStore;
import com.mindarray.store.TemplateConfigStore;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.PasswordPolicy.PASSWORD_POLICY_MAXIMUM_LENGTH;
import static com.mindarray.api.Template.*;
import static com.mindarray.db.DBConstants.*;


public class Patch8026 implements Patch
{
    private static final Logger LOGGER = new Logger(Patch8026.class, MOTADATA_PATCH, "Patch 8.0.26");

    private static final String VERSION = "8.0.26";

    /**
     * Returns the version number of this patch.
     *
     * @return The version string "8.0.26"
     */
    @Override
    public String getVersion()
    {
        return VERSION;
    }

    @Override
    public Future<Void> doPatch()
    {
        var promise = Promise.<Void>promise();

        var futures = new ArrayList<Future<Void>>();

        LOGGER.info("executing patch 8.0.26");

        futures.add(executeTemplatePatch());

        futures.add(executePasswordPolicyPatch());

        futures.add(executeMetricPolicyPatch());

        Future.join(futures).onComplete(result ->
        {
            if (result.succeeded())
            {
                LOGGER.info("successfully executed patch 8.0.26  ");

                promise.complete();

            }
            else
            {
                LOGGER.error(result.cause());

                promise.fail(result.cause());

            }
        });

        return promise.future();
    }

    private Future<Void> executeTemplatePatch()
    {
        var promise = Promise.<Void>promise();

        try
        {
            updateDockerParentTemplateId()
                    .compose(e -> setParentChildRelationshipOfTemplate())
                    .compose(e -> setTemplateIdForObjects())
                    .compose(e -> removeTabs())
                    .compose(e-> removePingTemplate()).onComplete(result ->
                    {
                        if (result.succeeded())
                        {
                            LOGGER.info("successfully executed patch 8.0.26 final all template for object assigned and tabs removed ");

                            promise.complete();

                        }
                        else
                        {
                            LOGGER.warn(String.format("failed to execute patch 8026 reason: %s", result.cause()));

                            promise.fail(result.cause());
                        }
                    });
        }
        catch (Exception e)
        {
            LOGGER.error(e);

            promise.fail(e);
        }
        return promise.future();
    }

    private Future<Void> removePingTemplate()
    {

        var promise = Promise.<Void>promise();

        try
        {
            var template = TemplateConfigStore.getStore().getItem(10000000000178L);

            if(template!=null)
            {
                Bootstrap.configDBService.delete(TBL_TEMPLATE,
                        new JsonObject().put(FIELD_NAME, ID).put(VALUE, template.getLong(ID)),
                        DEFAULT_USER, MOTADATA_SYSTEM, result ->
                        {
                            if (result.succeeded())
                            {
                                TemplateConfigStore.getStore().deleteItem(template.getLong(ID));
                                promise.complete();
                            }
                            else
                            {
                                LOGGER.warn(String.format("failed to delete template %d in database: %s",
                                        template.getLong(ID), result.cause().getMessage()));
                                promise.fail(result.cause());
                            }
                        });

            }
            else
            {
                LOGGER.info("template not found in database for Ping");
                promise.complete(null);
            }



        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }


    private Future<Void> setParentChildRelationshipOfTemplate() {

        LOGGER.info("executing patch 8.0.26 - establishing template parent-child relationships");

        var promise = Promise.<Void>promise();

        try
        {
            var futures = new ArrayList<Future<Void>>();

            // Get all templates from the store
            var allTemplates = TemplateConfigStore.getStore().getItemsByValue("_type","0");

            if (allTemplates != null && !allTemplates.isEmpty())
            {
                LOGGER.info(String.format("processing %d templates for parent-child relationship establishment", allTemplates.size()));

                // Iterate through all templates
                for (var index = 0; index < allTemplates.size(); index++)
                {
                    var parentTemplate = allTemplates.getJsonObject(index);

                    var parentTemplateId = parentTemplate.getLong(ID);

                    // Check if this template has tabs
                    if (parentTemplate.containsKey(TEMPLATE_TABS))
                    {
                        var templateTabs = parentTemplate.getJsonArray(TEMPLATE_TABS);

                        if (templateTabs != null && !templateTabs.isEmpty())
                        {
                            LOGGER.info(String.format("processing template %d with %d tabs", parentTemplateId, templateTabs.size()));

                            // Process each tab in the template.tabs array
                            for (var tabIndex = 0; tabIndex < templateTabs.size(); tabIndex++)
                            {
                                var tabObject = templateTabs.getJsonObject(tabIndex);

                                if (tabObject.containsKey(TEMPLATE_TAB_ID))
                                {
                                    var tabId = tabObject.getLong(TEMPLATE_TAB_ID);

                                    // Find the corresponding template object using the tab ID
                                    var template = TemplateConfigStore.getStore().getItem(tabId);

                                    if(tabId.equals(parentTemplateId))
                                    {

                                        template.put(TEMPLATE_PARENT_ID, DEFAULT_TEMPLATE_ID);

                                        var updateFuture = updateTemplate(template);
                                        futures.add(updateFuture);
                                    }
                                    else if (template != null && !template.isEmpty())
                                    {
                                        // Set the parent ID for this tab template
                                        template.put(TEMPLATE_PARENT_ID, parentTemplateId);

                                        template.put(TEMPLATE_TEXT,tabObject.getString(TEMPLATE_TEXT));
                                        // Create a future for updating this tab template
                                        var updateFuture = updateTemplate(template);
                                        futures.add(updateFuture);

                                        LOGGER.debug(String.format("scheduled update for tab template %d with parent %d", tabId, parentTemplateId));
                                    }
                                    else
                                    {
                                        LOGGER.warn(String.format("tab template with ID %d not found in TemplateConfigStore", tabId));
                                    }
                                }
                            }
                        }
                    }


                    // Check if this template has tabs
                    if (parentTemplate.containsKey("custom.tabs"))
                    {
                        var templateTabs = parentTemplate.getJsonArray("custom.tabs");

                        if (templateTabs != null && !templateTabs.isEmpty())
                        {
                            LOGGER.info(String.format("processing template %d with %d custom tabs", parentTemplateId, templateTabs.size()));

                            // Process each tab in the template.tabs array
                            for (var tabIndex = 0; tabIndex < templateTabs.size(); tabIndex++)
                            {
                                var tabObject = templateTabs.getJsonObject(tabIndex);

                                if (tabObject.containsKey(TEMPLATE_TAB_ID))
                                {
                                    var tabId = tabObject.getLong(TEMPLATE_TAB_ID);

                                    // Finding the corresponding template object using the tab ID
                                    var tabTemplate = TemplateConfigStore.getStore().getItem(tabId);

                                    if(tabId.equals(parentTemplateId))
                                    {

                                        tabTemplate.put(TEMPLATE_PARENT_ID, DEFAULT_TEMPLATE_ID);

                                        var updateFuture = updateTemplate(tabTemplate);

                                        futures.add(updateFuture);
                                    }
                                    else if (tabTemplate != null && !tabTemplate.isEmpty())
                                    {
                                        // Set the parent ID for this tab template
                                        tabTemplate.put(TEMPLATE_PARENT_ID, parentTemplateId);

                                        tabTemplate.put(TEMPLATE_TEXT,tabObject.getString(TEMPLATE_NAME));

                                        // Create a future for updating this tab template
                                        var updateFuture = updateTemplate(tabTemplate);

                                        futures.add(updateFuture);

                                        LOGGER.debug(String.format("scheduled update for tab template %d with parent %d", tabId, parentTemplateId));
                                    }
                                    else
                                    {
                                        LOGGER.warn(String.format("tab template with ID %d not found in TemplateConfigStore", tabId));
                                    }
                                }
                            }
                        }
                    }
                }

                // Wait for all updates to complete
                Future.join(futures).onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        LOGGER.info(String.format("successfully executed patch 8.0.26 - updated %d tab templates", futures.size()));

                        promise.complete();
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        promise.fail(result.cause());
                    }
                });
            }
            else
            {
                LOGGER.info("no templates found in TemplateConfigStore, patch 8.0.26 completed with no changes");

                promise.complete();
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }


    /**
     * Updates a tab template in the database and refreshes the store.
     *
     * @param template The tab template object to update
     * @return A Future that completes when the update is successful
     */
    private Future<Void> updateTemplate(JsonObject template)
    {
        var promise = Promise.<Void>promise();

        Bootstrap.configDBService.update(DBConstants.TBL_TEMPLATE,
                new JsonObject().put(FIELD_NAME, ID).put(VALUE, template.getLong(ID)),
                template,
                DEFAULT_USER, MOTADATA_SYSTEM, result ->
                {
                    if (result.succeeded())
                    {
                        // Update the template in the store
                        TemplateConfigStore.getStore().updateItem(template.getLong(ID)).onComplete(asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                LOGGER.debug(String.format("successfully updated tab template %d with parent ID %d",
                                        template.getLong(ID), template.getLong(TEMPLATE_PARENT_ID)));

                                promise.complete();
                            }
                            else
                            {
                                LOGGER.warn(String.format("failed to refresh tab template %d in store: %s",
                                        template.getLong(ID), asyncResult.cause().getMessage()));

                                promise.fail(asyncResult.cause());
                            }
                        });
                    }
                    else
                    {
                        LOGGER.warn(String.format("failed to update tab template %d in database: %s",
                                template.getLong(ID), result.cause().getMessage()));

                        promise.fail(result.cause());
                    }
                });

        return promise.future();
    }


    private Future<Void> setTemplateIdForObjects()
    {
        LOGGER.info("executing patch 8.0.26 - establishing templateId in Objects");

        var promise = Promise.<Void>promise();

        try
        {
            var futures = new ArrayList<Future<Void>>();

            var objects = ObjectConfigStore.getStore().getItems();

            if (objects != null && !objects.isEmpty())
            {
                LOGGER.info(String.format("processing %d objects for template ID establishment", objects.size()));

                for (var i = 0; i < objects.size(); i++)
                {
                    var object = objects.getJsonObject(i);

                    object.put(VisualizationConstants.TEMPLATE_ID, VisualizationConstants.resolveTemplate(object.getLong(ID)));

                    // Create a future for updating this object
                    var updateFuture = updateObject(object);

                    futures.add(updateFuture);

                    LOGGER.debug(String.format(
                            "scheduled update for object %d with template ID %d [IP=%s, Name=%s, Category=%s Type = %s Object.id= %d]",object.getLong(ID),object.getLong(VisualizationConstants.TEMPLATE_ID),
                            object.getString(AIOpsObject.OBJECT_IP),object.getString(AIOpsObject.OBJECT_NAME),object.getString(AIOpsObject.OBJECT_CATEGORY), object.getString(AIOpsObject.OBJECT_TYPE) , object.getLong(AIOpsObject.OBJECT_ID)
                    ));

                }

                // Wait for all updates to complete
                Future.join(futures).onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        LOGGER.info(String.format("successfully executed patch 8.0.26 - updated %d objects with template IDs", futures.size()));
                        promise.complete();
                    }
                    else
                    {
                        LOGGER.error(result.cause());
                        promise.fail(result.cause());
                    }
                });
            }
            else
            {
                LOGGER.info("no objects found in ObjectConfigStore, patch 8.0.26 completed with no changes");
                promise.complete();
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            promise.fail(exception);
        }

        return promise.future();
    }

    /**
     * Updates an object in the database and refreshes the store.
     *
     * @param object The object to update
     * @return A Future that completes when the update is successful
     */
    private Future<Void> updateObject(JsonObject object)
    {
        var promise = Promise.<Void>promise();

        var objectId = object.getLong(ID);

        Bootstrap.configDBService.update(DBConstants.TBL_OBJECT,
                new JsonObject().put(FIELD_NAME, ID).put(VALUE, objectId),
                object,
                DEFAULT_USER, MOTADATA_SYSTEM, result ->
                {
                    if (result.succeeded())
                    {
                        // Refresh the object in the store
                        ObjectConfigStore.getStore().updateItem(objectId).onComplete(asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                LOGGER.debug(String.format("successfully updated object %d with template ID %d",
                                        objectId, object.getLong(VisualizationConstants.TEMPLATE_ID)));
                                promise.complete();
                            }
                            else
                            {
                                LOGGER.warn(String.format("failed to refresh object %d in store: %s",
                                        objectId, asyncResult.cause().getMessage()));
                                promise.fail(asyncResult.cause());
                            }
                        });
                    }
                    else
                    {
                        LOGGER.warn(String.format("failed to update object %d in database: %s",
                                objectId, result.cause().getMessage()));
                        promise.fail(result.cause());
                    }
                });

        return promise.future();
    }

    private Future<Void> removeTabs()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var futures = new ArrayList<Future<Void>>();

            var templates = TemplateConfigStore.getStore().getItems();

            for (int i = 0; i < templates.size(); i++)
            {

                var template = templates.getJsonObject(i);

                template.put(GARBAGE_FIELDS, new JsonArray().add(TEMPLATE_TABS).add("custom.tabs"));

                var future = updateTemplate(template);

                futures.add(future);

            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    LOGGER.info(String.format("successfully executed patch 8.0.26 - updated %d tab templates and removed template and custom tabs ", futures.size()));

                    promise.complete();
                }
                else
                {
                    LOGGER.error(result.cause());

                    promise.fail(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();

    }



    private Future<Void> updateDockerParentTemplateId()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var template = TemplateConfigStore.getStore().getItem(10000000000360L);

            template.put(TEMPLATE_PARENT_ID,DEFAULT_TEMPLATE_ID);

            updateTemplate(template).onComplete(result -> {

                if (result.succeeded())
                {
                    LOGGER.info("Successfully upated parent id for docker template");

                    promise.complete();
                }
                else
                {
                    LOGGER.error(result.cause());

                    promise.fail(result.cause());
                }

            });

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    private Future<Void> executePasswordPolicyPatch()
    {

        var promise = Promise.<Void>promise();

        var item = PasswordPolicyConfigStore.getStore().getItem();

        var id = 10000000000001L;

        item.put(PASSWORD_POLICY_MAXIMUM_LENGTH, 64);

        Bootstrap.configDBService().update(DBConstants.TBL_PASSWORD_POLICY,
                new JsonObject().put(DBConstants.FIELD_NAME, GlobalConstants.ID).put(GlobalConstants.VALUE, id),
                item,
                GlobalConstants.DEFAULT_USER, GlobalConstants.MOTADATA_SYSTEM, result ->
                {
                    if (result.succeeded())
                    {
                        PasswordPolicyConfigStore.getStore().updateItem(id).onComplete(asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                LOGGER.info("successfully added password policy");

                                promise.complete();
                            }
                            else
                            {
                                promise.fail(result.cause());

                                LOGGER.error(result.cause());
                            }
                        });
                    }
                    else
                    {
                        promise.fail(result.cause());

                        LOGGER.error(result.cause());
                    }
                });

        return promise.future();
    }

    private Future<Void> executeMetricPolicyPatch()
    {
        var promise = Promise.<Void>promise();

        // availability default policy
        var id = 10000000000012L;

        var category = 10000000000136L;

        var item = MetricPolicyConfigStore.getStore().getItem(id);

        if (!item.getJsonObject(PolicyEngineConstants.POLICY_CONTEXT).getJsonArray(GlobalConstants.ENTITIES).contains(category))
        {
            item.getJsonObject(PolicyEngineConstants.POLICY_CONTEXT).getJsonArray(GlobalConstants.ENTITIES).add(category);
        }

        Bootstrap.configDBService().updateAll(DBConstants.TBL_METRIC_POLICY,
                new JsonObject().put(DBConstants.FIELD_NAME, GlobalConstants.ID).put(GlobalConstants.VALUE, id),
                item,
                GlobalConstants.DEFAULT_USER, GlobalConstants.MOTADATA_SYSTEM, result ->
                {
                    if (result.succeeded())
                    {
                        MetricPolicyConfigStore.getStore().updateItem(id).onComplete(asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                LOGGER.info("successfully updated availability default policy");

                                promise.complete();
                            }
                            else
                            {
                                promise.fail(result.cause());

                                LOGGER.error(result.cause());
                            }
                        });
                    }
                    else
                    {
                        promise.fail(result.cause());

                        LOGGER.error(result.cause());
                    }
                });

        return promise.future();
    }


}
