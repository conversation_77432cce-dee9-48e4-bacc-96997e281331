/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/* Change Logs:
 *  Date			Author			Notes
 *  21-Feb-2025		<PERSON><PERSON>		MOTADATA-5177 : if interface traffic utilization will exceeds 100 percent then we will assign its value to 100 instead of 0.
 *  23-Jul-2025     Chopra Deven    MOTADATA-6889: Remove monitor from pollingErrors Map if case of deletion of Object.
 *  04-July-2025    <PERSON><PERSON>    MOTADATA_6661 : Support for bits column in metric policy.
 *  Aug-08-2025     <PERSON><PERSON>    Change Event for shadow counter.
 */

package com.mindarray.nms;

import com.mindarray.ErrorCodes;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Metric;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.notification.Notification;
import com.mindarray.store.*;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.*;
import static com.mindarray.notification.Notification.NOTIFICATION_TYPE;

/**
 * MetricEnricher is responsible for processing, enriching, and storing metric data collected from monitored systems.
 * <p>
 * This class performs several key functions:
 * - Processes raw metric data from various sources (SNMP, WMI, etc.)
 * - Calculates derived metrics (rates, utilization percentages, etc.)
 * - Handles metric correlation between related metrics
 * - Manages shadow counters for rate calculations
 * - Tracks and processes polling errors
 * - Formats and stores metric data in the datastore
 * - Sends notifications about metric processing issues
 * <p>
 * The class operates as a Vert.x verticle and uses the event bus for communication with other components.
 * It subscribes to metric events and processes them asynchronously.
 */
public class MetricEnricher extends AbstractVerticle
{

    private static final Logger LOGGER = new Logger(MetricEnricher.class, GlobalConstants.MOTADATA_NMS, "Metric Enricher");
    private static final int INTERVAL_SECONDS = MotadataConfigUtil.getPollingErrorCleanupTimerSeconds();
    private static final String FIRST_TRIGGERED = "first.triggered";
    private final Map<Long, Map<String, Map<String, Long>>> contextsByMetric = new HashMap<>();
    private final StringBuilder builder = new StringBuilder(0);
    private final StringBuilder correlationMetricBuilder = new StringBuilder(0);
    private final Map<Long, Integer> pollingErrorTrackers = new HashMap<>();
    private final Map<Long, JsonObject> pollingErrors = new HashMap<>();
    private EventEngine eventEngine;
    private Set<String> correlationMetricMappers;
    private Set<String> mappers;
    /**
     * Initializes the MetricEnricher verticle.
     * <p>
     * This method:
     * 1. Initializes shadow counters for rate calculations
     * 2. Sets up data structures for tracking metrics and mappers
     * 3. Creates and configures an EventEngine to process metric events
     * 4. Sets up event bus consumers for change notifications
     * 5. Starts a periodic timer to clean up polling errors
     * <p>
     * The method handles various change notifications:
     * - DISABLE_METRIC, SUSPEND_METRIC, DELETE_METRIC: Removes metrics from tracking
     * - DELETE_OBJECT: Cleans up metrics associated with deleted objects
     * - UPDATE_CACHE: Updates shadow counters from cache files
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        mappers = new HashSet<>();

        correlationMetricMappers = new HashSet<>();

        eventEngine = new EventEngine()
                .setEventType(config().getString(EventBusConstants.EVENT_TYPE))
                .setPersistEventOffset(true).setLogger(LOGGER)
                .setEventHandler(this::calculate).start(vertx, promise);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
        {
            var event = message.body();

            switch (EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)))
            {

                case DISABLE_METRIC, SUSPEND_METRIC, DELETE_METRIC ->
                        contextsByMetric.remove(event.getLong(GlobalConstants.ID));

                case DELETE_OBJECT ->
                {
                    pollingErrors.remove(event.getLong(GlobalConstants.ID));

                    pollingErrorTrackers.remove(event.getLong(GlobalConstants.ID));

                    if (!event.containsKey(NMSConstants.METRIC_INSTANCES) || event.getValue(METRIC_INSTANCES) == null)
                    {
                        if (event.getJsonArray(NMSConstants.OBJECTS) != null)
                        {
                            var id = event.getLong(GlobalConstants.ID);

                            event.getJsonArray(NMSConstants.OBJECTS).forEach(object ->
                            {
                                if (contextsByMetric.containsKey(id))
                                {
                                    contextsByMetric.get(id).keySet().stream()
                                            .filter(key -> key.startsWith(CommonUtil.getString(object)))
                                            .collect(Collectors.toSet())
                                            .forEach(key -> contextsByMetric.get(id).remove(CommonUtil.getString(key)));
                                }

                            });

                        }
                        else
                        {
                            contextsByMetric.remove(event.getLong(GlobalConstants.ID));
                        }
                    }
                }
                default ->
                {
                }
            }

        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_POLLING_ERROR_QUERY + DOT_SEPARATOR + config().getString(EVENT_TYPE).split("\\.")[2], message ->
        {
            try
            {
                for (var entry : pollingErrors.entrySet())
                {
                    entry.getValue().put(DURATION, DateTimeUtil.currentSeconds() - entry.getValue().getLong(FIRST_TRIGGERED));
                }

                var reply = JsonObject.mapFrom(pollingErrors);

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace(String.format("Sending reply for polling errors %s", reply.encodePrettily()));
                }

                message.reply(reply);
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                message.reply(new JsonObject());
            }
        });

        vertx.setPeriodic(INTERVAL_SECONDS * 1000L, timer ->
        {
            var iterator = pollingErrorTrackers.entrySet().iterator();

            while (iterator.hasNext())
            {
                var item = iterator.next();

                item.setValue(item.getValue() - INTERVAL_SECONDS);

                if (item.getValue() <= 0)
                {
                    iterator.remove();
                }
            }
        });

        if (MotadataConfigUtil.getEnvironmentType().equalsIgnoreCase(ENV_TEST))
        {
            LOGGER.info("PollingErrors : " + pollingErrorTrackers);

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_POLLING_ERROR_NOTIFICATION_TEST, message -> message.reply(JsonObject.mapFrom(pollingErrorTrackers)));
        }

    }

    @Override
    public void stop(Promise<Void> promise)
    {
        eventEngine.stop(vertx, promise);
    }

    /**
     * Processes metric data received from monitoring agents and prepares it for storage.
     * <p>
     * This method:
     * 1. Extracts metric data from the event
     * 2. Categorizes metrics into different types:
     * - Scalar metrics (single values)
     * - Instance metrics (multiple instances of the same metric)
     * - Correlated metrics (metrics that are related to each other)
     * 3. Processes each type of metric appropriately
     * 4. Calculates derived metrics (rates, utilization percentages, etc.)
     * 5. Formats the data for storage in the datastore
     * 6. Handles errors and sends notifications if needed
     * <p>
     * Special handling is provided for different types of metrics, such as SNMP interface
     * metrics which require uptime information for rate calculations.
     *
     * @param event A JsonObject containing the metric data to process
     */
    private void calculate(JsonObject event)
    {
        try
        {
            var entries = event.getMap();

            if (entries != null && CommonUtil.traceEnabled())
            {
                LOGGER.trace(String.format("metric poll received for enriching: %s", CommonUtil.removeSensitiveFields(entries, false).encodePrettily()));

            }

            if (entries != null && entries.get(GlobalConstants.RESULT) != null)
            {
                builder.setLength(0);

                entries.entrySet().removeIf(item -> NMSConstants.GARBAGE_FIELDS.contains(item.getKey()));

                var id = CommonUtil.getLong(entries.get(GlobalConstants.ID)); //object id if it's metric agent data else metric id....

                var result = (Map<String, Object>) entries.get(GlobalConstants.RESULT);

                var uptime = new AtomicLong();

                if (CommonUtil.getString(entries.get(Metric.METRIC_PLUGIN)).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()))
                {
                    uptime.set(CommonUtil.getLong(result.remove(NMSConstants.STARTED_TIME_SECONDS), DateTimeUtil.currentSeconds()));

                    result.remove(NMSConstants.STARTED_TIME);
                }

                var correlationMetrics = result.containsKey(NMSConstants.CORRELATION_METRICS) ? (ArrayList<String>) result.get(NMSConstants.CORRELATION_METRICS) : null;

                if (!contextsByMetric.containsKey(id))
                {
                    contextsByMetric.put(id, new HashMap<>());
                }

                result.remove(NMSConstants.CORRELATION_METRICS);

                result.remove(ERRORS);

                result.remove(NMSConstants.SNMP_OID_GROUP_INVALID_OIDS);


                var buffer = Buffer.buffer("UTF-8");

                appendBytes(event, buffer);

                buffer.appendIntLE(EMPTY_VALUE.length());

                buffer.appendString(EMPTY_VALUE);

                // filtering result

                var iterator = result.entrySet().iterator();

                var correlatedMetrics = new HashMap<String, Object>();

                var scalarMetrics = new HashMap<String, Object>();

                var instanceMetrics = new HashMap<String, Object>();

                while (iterator.hasNext())
                {
                    var entry = iterator.next();

                    if (entry.getValue() instanceof List)
                    {
                        if (correlationMetrics != null && correlationMetrics.contains(entry.getKey()))
                        {
                            correlatedMetrics.put(entry.getKey(), entry.getValue());
                        }
                        else if (correlationMetrics == null || !correlationMetrics.contains(entry.getKey()))
                        {
                            instanceMetrics.put(entry.getKey(), entry.getValue());
                        }
                    }
                    else
                    {
                        scalarMetrics.put(entry.getKey(), entry.getValue());
                    }
                }

                entries.put(TAGS, new JsonArray(entries.get(AIOpsObject.OBJECT_TAGS) != null ? (ArrayList<Long>) entries.get(AIOpsObject.OBJECT_TAGS) : new ArrayList<Long>()));

                if (!correlatedMetrics.isEmpty())
                {
                    processCorrelatedMetrics(correlatedMetrics, entries);
                }

                if (!instanceMetrics.isEmpty())
                {
                    processInstanceMetrics(instanceMetrics, entries, buffer, result, event, id, uptime);
                }

                if (!scalarMetrics.isEmpty())
                {
                    processScalarMetrics(scalarMetrics, buffer, result, event, id);
                }

                vertx.eventBus().publish(EventBusConstants.EVENT_METRIC_POLICY, new JsonObject(entries).put(EVENT_COPY_REQUIRED, false));

                vertx.eventBus().send(EVENT_AIOPS_METRIC_POLICY_MANAGER, new JsonObject(entries).put(EVENT_COPY_REQUIRED, false));

                if (MotadataConfigUtil.devMode())
                {
                    vertx.eventBus().publish(EventBusConstants.EVENT_METRIC_ENRICHER_TEST, new JsonObject(entries));
                }

                if (!event.getJsonObject(RESULT).isEmpty() && (!event.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.AVAILABILITY.getName()) && !event.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.NETWORK_SERVICE.getName())))
                {
                    if (!pollingErrorTrackers.isEmpty())
                    {
                        pollingErrorTrackers.remove(event.getLong(Metric.METRIC_OBJECT));
                    }

                    if (!pollingErrors.isEmpty())
                    {
                        pollingErrors.remove(event.getLong(Metric.METRIC_OBJECT));
                    }
                }
            }

            if (event.containsKey(ERROR_CODE) && (event.getString(ERROR_CODE).equalsIgnoreCase(ErrorCodes.ERROR_CODE_TIMEOUT) || event.getString(ERROR_CODE).equalsIgnoreCase(ErrorCodes.ERROR_CODE_INVALID_CREDENTIALS) || event.getString(ERROR_CODE).equalsIgnoreCase(ErrorCodes.ERROR_CODE_INVALID_PORT) || event.getString(ERROR_CODE).equalsIgnoreCase(ErrorCodes.ERROR_CODE_CONNECTION_FAILED)))
            {

                if (!pollingErrors.containsKey(event.getLong(Metric.METRIC_OBJECT)))
                {
                    var object = ObjectConfigStore.getStore().getItem(event.getLong(Metric.METRIC_OBJECT));

                    if (object != null)
                    {
                        pollingErrors.put(event.getLong(Metric.METRIC_OBJECT), new JsonObject().put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP))
                                .put(MESSAGE, event.getString(MESSAGE)).put(FIRST_TRIGGERED, event.getLong(EVENT_TIMESTAMP)));
                    }
                }
                else if (pollingErrors.containsKey(event.getLong(Metric.METRIC_OBJECT)))
                {
                    pollingErrors.get(event.getLong(Metric.METRIC_OBJECT)).put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP));
                }

                notify(event.getLong(Metric.METRIC_OBJECT), event);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void processCorrelatedMetrics(Map<String, Object> correlatedMetrics, Map<String, Object> entries)
    {
        correlatedMetrics.forEach((key, value) ->
        {
            vertx.fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + CACHE_DIR + PATH_SEPARATOR + entries.get(AIOpsObject.OBJECT_ID) + DASH_SEPARATOR + key + GlobalConstants.DASH_SEPARATOR + new SimpleDateFormat(DateTimeUtil.CORRELATED_METRIC_CACHE_FILE_TIMESTAMP_FORMAT).format(new Date()),
                    Buffer.buffer(CodecUtil.compress(new JsonObject().put(RESULT, value).encode().getBytes())));

            ((List<Map<String, java.lang.Object>>) value).forEach(instances ->
            {
                correlationMetricBuilder.setLength(0);

                DatastoreConstants.write(new JsonObject().mergeIn(new JsonObject(instances)).put(NMSConstants.CORRELATION_METRICS, YES).put(PLUGIN_ID, DatastoreConstants.PluginId.CORRELATED_METRIC.getName()).put(EventBusConstants.EVENT_TIMESTAMP, entries.get(EventBusConstants.EVENT_TIMESTAMP))
                        .put(DatastoreConstants.DATASTORE_CATEGORY, DatastoreConstants.DatastoreCategory.EVENT.getName()).put(DatastoreConstants.DATASTORE_TYPE, DatastoreConstants.DatastoreType.CORRELATED_METRIC.ordinal())
                        .put(EVENT_SOURCE, entries.getOrDefault(AIOpsObject.OBJECT_IP, AIOpsObject.OBJECT_TARGET)).put(AIOpsObject.OBJECT_ID, entries.get(AIOpsObject.OBJECT_ID)), key, correlationMetricMappers, correlationMetricBuilder);
            });
        });
    }

    /**
     * Processes scalar metrics (single value metrics) and prepares them for storage.
     * <p>
     * This method:
     * 1. Iterates through all scalar metrics
     * 2. Identifies and handles special cases:
     * - Rate metrics (metrics that track changes over time)
     * - Skip-rate metrics (metrics that should not be processed as rates)
     * - Status metrics (handled separately)
     * - Bytes-to-bits conversion for metrics containing "bytes.per.sec"
     * 3. Calculates derived rate metrics using the calculateMetricRate method
     * 4. Formats the data and adds it to the buffer for storage
     * 5. Converts bytes.per.sec metrics to bits.per.sec and stores them in result map
     * 6. Updates the result map with processed metrics
     * 7. Updates shadow counters map for bytes-to-bits conversions
     * <p>
     * The method maintains a set of "void" metrics that should be removed from the result
     * after processing (typically metrics that are only used for rate calculations).
     *
     * @param scalarMetrics A map of scalar metrics to process
     * @param buffer        A buffer to store the formatted data
     * @param result        A map to store the processed results
     * @param event         The original event containing metadata
     * @param id            The ID of the metric or object
     */
    private void processScalarMetrics(Map<String, Object> scalarMetrics, Buffer buffer, Map<String, Object> result, JsonObject event, long id)
    {
        var voidScalarMetrics = new HashSet<String>();

        var scalarRateMetrics = new JsonObject();

        var scalarBitsMetrics = new JsonObject();

        scalarMetrics.forEach((key, value) ->
        {
            var skipMetric = false;

            if (key.endsWith(NMSConstants.METRIC_SKIP_RATE))
            {
                scalarRateMetrics.put(key.substring(0, key.lastIndexOf(".")), CommonUtil.getLong(value));

                voidScalarMetrics.add(key);

                skipMetric = true;
            }
            else if (key.endsWith(NMSConstants.METRIC_RATE))
            {
                calculateMetricRate(id, key, key, CommonUtil.getLong(value), scalarRateMetrics);

                voidScalarMetrics.add(key);

                skipMetric = true;
            }
            else if (key.equalsIgnoreCase(STATUS))
            {
                skipMetric = true;
            }

            if (!skipMetric && value != null)
            {
                updateBuffer(key, CommonUtil.getString(value), event.getInteger(PLUGIN_ID), buffer);

                // Handle bytes to bits conversion for scalar metrics - store in JsonObject
                // Only process if the metric contains "bytes.per.sec" to avoid unnecessary processing
                if (key.contains("bytes.per.sec"))
                {
                    processBytesToBitsConversion(key, CommonUtil.getString(value), scalarBitsMetrics, event.getInteger(PLUGIN_ID));
                }
            }
        });

        if (!scalarRateMetrics.isEmpty())
        {
            result.putAll(scalarRateMetrics.getMap());

            scalarRateMetrics.getMap().forEach((key, value) ->
            {
                if (value != null)
                {
                    updateBuffer(key, CommonUtil.getString(value), event.getInteger(PLUGIN_ID), buffer);

                    // Handle bytes to bits conversion for rate metrics - store in JsonObject
                    // Only process if the metric contains "bytes.per.sec" to avoid unnecessary processing
                    if (key.contains("bytes.per.sec"))
                    {
                        processBytesToBitsConversion(key, CommonUtil.getString(value), scalarBitsMetrics, event.getInteger(PLUGIN_ID));
                    }
                }
            });

            scalarRateMetrics.clear();
        }

        // Add converted bits metrics to result map
        if (!scalarBitsMetrics.isEmpty())
        {
            result.putAll(scalarBitsMetrics.getMap());

            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace(String.format("Added %d scalar bits metrics to result: %s",
                        scalarBitsMetrics.size(), scalarBitsMetrics.encodePrettily()));
            }

            scalarBitsMetrics.clear();
        }

        if (!voidScalarMetrics.isEmpty())
        {
            voidScalarMetrics.forEach(result::remove);

            voidScalarMetrics.clear();
        }

        vertx.eventBus().send(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.METRIC.getName(), buffer.getBytes());
    }

    /**
     * Processes metrics that contain "bytes.per.sec" and converts them to "bits.per.sec".
     * <p>
     * This unified method handles both scalar and instance metrics:
     * 1. Checks if the metric name contains "bytes.per.sec"
     * 2. If found, creates a corresponding "bits.per.sec" metric name
     * 3. Converts the value from bytes to bits (multiply by 8)
     * 4. Stores the bits metric in the provided JsonObject map
     * 5. Updates the shadow counters map and flags for batch save at end of processing cycle
     * <p>
     * The method automatically detects metric type:
     * - Scalar metrics: No "~" in the name
     * - Instance metrics: Contains "~" in the name
     *
     * @param metricName    The metric name (key)
     * @param value         The metric value
     * @param bitsMetrics   JsonObject to store the converted bits metrics
     */
    private void processBytesToBitsConversion(String metricName, String value, JsonObject bitsMetrics, int pluginId )
    {
        // Process any metric that contains "bytes.per.sec" (both scalar and instance)
        try
        {
            // Create the corresponding bits metric name
            var bitsColumn = metricName.replace("bytes.per.sec", "bits.per.sec");

            var bitsValue = CommonUtil.getDouble(value) * 8;

            // Store the bits metric in the JsonObject map
            bitsMetrics.put(bitsColumn, bitsValue);

            //Append in the mapper
            builder.setLength(0);

            builder.append(DatastoreConstants.getDataCategory(true, bitsColumn, CommonUtil.getString(bitsValue))).append(COLUMN_SEPARATOR).append(pluginId).append(COLUMN_SEPARATOR).append(bitsColumn).append(COLUMN_SEPARATOR).append(NO);

            if (!mappers.contains(builder.toString()))
            {
                mappers.add(builder.toString());

                vertx.eventBus().publish(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE,
                        new JsonObject()
                                .put(CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_METRIC_COLUMN.name())
                                .put(DatastoreConstants.MAPPER, builder.toString()));

                vertx.eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION,
                        new JsonObject()
                                .put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_SHADOW_COUNTERS.name())
                                .put(KEY, metricName).put(VALUE, bitsColumn));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Processes instance metrics (metrics with multiple instances) and prepares them for storage.
     * <p>
     * This method:
     * 1. Iterates through all instance metrics
     * 2. Handles special cases for different types of metrics:
     * - SNMP interface metrics (calculates interface counts, bandwidth, utilization)
     * - Virtual machine metrics (calculates VM counts)
     * - Other instance-based metrics
     * 3. Processes each instance individually:
     * - Extracts instance name and properties
     * - Calculates derived metrics for each instance
     * - Handles rate calculations for counter metrics
     * - Converts bytes.per.sec metrics to bits.per.sec and stores them in result map
     * - Formats the data for storage
     * 4. Updates the buffer with the processed metrics
     * 5. Sends the data to the appropriate datastore
     * <p>
     * For network interfaces, the method performs additional calculations:
     * - Interface bandwidth and utilization
     * - Traffic rates (bytes/packets per second)
     * - Error and discard rates
     *
     * @param instanceMetrics A map of instance metrics to process
     * @param entries         The original event entries containing metadata
     * @param buffer          A buffer to store the formatted data
     * @param result          A map to store the processed results
     * @param event           The original event containing metadata
     * @param id              The ID of the metric or object
     * @param uptime          The system uptime (used for rate calculations)
     */
    private void processInstanceMetrics(Map<String, Object> instanceMetrics, Map<String, Object> entries, Buffer buffer, Map<String, Object> result, JsonObject event, long id, AtomicLong uptime)
    {
        var objectId = CommonUtil.getLong(entries.get(Metric.METRIC_OBJECT));

        var enrichedMetrics = new JsonObject();

        var instanceBitsMetrics = new JsonObject();

        var tags = (JsonArray) entries.get(TAGS);

        instanceMetrics.forEach((key, value) ->
        {
            var alteredInstances = (List<Map<String, java.lang.Object>>) value;

            var items = new HashMap<String, JsonObject>();

            var instanceRateMetrics = new JsonObject();

            var voidInstanceMetrics = new HashSet<String>();

            if (CommonUtil.getString(entries.get(Metric.METRIC_PLUGIN)).equalsIgnoreCase(NMSConstants.MetricPlugin.SNMP_INTERFACE.getName()))
            {
                enrichedMetrics.put(NMSConstants.TOTAL_INTERFACES, alteredInstances.size());

                updateBuffer(NMSConstants.TOTAL_INTERFACES, CommonUtil.getString(alteredInstances.size()), event.getInteger(PLUGIN_ID), buffer);

                vertx.eventBus().send(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.METRIC.getName(), buffer.getBytes());
            }
            else if (NMSConstants.isVirtualMachineMetric(CommonUtil.getString(entries.get(Metric.METRIC_PLUGIN))))
            {
                enrichedMetrics.put(NMSConstants.TOTAL_VMS, alteredInstances.size());

                updateBuffer(NMSConstants.TOTAL_VMS, CommonUtil.getString(alteredInstances.size()), event.getInteger(PLUGIN_ID), buffer);

                vertx.eventBus().send(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.METRIC.getName(), buffer.getBytes());
            }

            for (var index = 0; index < alteredInstances.size(); index++)
            {
                var context = alteredInstances.get(index);

                var instance = new HashMap<String, java.lang.Object>();

                var statusColumn = context.containsKey(GlobalConstants.STATUS);

                if (context.get(key) != null)
                {
                    var instanceMetricBuffer = Buffer.buffer("UTF-8");

                    appendBytes(event, instanceMetricBuffer);

                    instance.put(key, CommonUtil.getString(context.get(key)).replace(GlobalConstants.INSTANCE_SEPARATOR, EMPTY_VALUE).replace("^", EMPTY_VALUE).replace("@#$", EMPTY_VALUE).replace("#@#", EMPTY_VALUE).replace("#", EMPTY_VALUE).replace("@", EMPTY_VALUE));

                    instance.put(key + INSTANCE_SEPARATOR + NMSConstants.INSTANCE_NAME, CommonUtil.getString(instance.get(key)));

                    for (var contextEntry : context.entrySet())
                    {
                        if (!key.equalsIgnoreCase(contextEntry.getKey()))
                        {
                            if (contextEntry.getKey().startsWith(key))
                            {
                                instance.put(contextEntry.getKey().replace(key + ".", key + GlobalConstants.INSTANCE_SEPARATOR), context.get(contextEntry.getKey()));
                            }
                            else
                            {
                                instance.put(key + GlobalConstants.INSTANCE_SEPARATOR + contextEntry.getKey(), context.get(contextEntry.getKey()));
                            }
                        }
                    }

                    if (key.equalsIgnoreCase(NMSConstants.INTERFACE) && !entries.get(Metric.METRIC_PLUGIN).equals(NMSConstants.MetricPlugin.RUCKUS_WIRELESS.getName()))
                    {
                        if (items.isEmpty())
                        {
                            var objects = MetricConfigStore.getStore().getObjects(id);

                            if (objects != null)
                            {
                                for (var item : objects.entrySet())
                                {
                                    if (item.getValue() != null)
                                    {
                                        items.put(item.getValue().getString(NMSConstants.INTERFACE_INDEX), item.getValue());
                                    }
                                }
                            }
                        }

                        var item = CommonUtil.getString(context.get(NMSConstants.INTERFACE));

                        if (!items.isEmpty() && items.containsKey(item))
                        {
                            items.get(item).remove(AIOpsObject.OBJECT_NAME);

                            items.get(item).remove(GlobalConstants.STATUS);

                            items.get(item).remove(AIOpsObject.OBJECT_TYPE);

                            context.putAll(items.get(item).getMap());

                            for (var entry : items.get(item).getMap().entrySet())
                            {
                                if (!key.equalsIgnoreCase(entry.getKey()))
                                {
                                    instance.put(entry.getKey().replace(key + ".", key + GlobalConstants.INSTANCE_SEPARATOR), context.get(entry.getKey()));
                                }
                            }

                            instance.put(key, CommonUtil.getString(context.get(key)).replace(GlobalConstants.INSTANCE_SEPARATOR, EMPTY_VALUE).replace("^", EMPTY_VALUE).replace("@#$", EMPTY_VALUE).replace("#@#", EMPTY_VALUE).replace("#", EMPTY_VALUE).replace("@", EMPTY_VALUE));

                            instance.put(key + INSTANCE_SEPARATOR + NMSConstants.INSTANCE_NAME, CommonUtil.getString(instance.get(key)));

                            alteredInstances.set(index, instance);
                        }

                        if (key.equalsIgnoreCase(NMSConstants.INTERFACE) && instance.get(NMSConstants.INTERFACE_BIT_TYPE) != null)
                        {
                            if (CommonUtil.getString(instance.get(key + GlobalConstants.INSTANCE_SEPARATOR + GlobalConstants.STATUS)).equalsIgnoreCase(GlobalConstants.STATUS_UP))
                            {
                                calculateInterfaceMetricRate(id, CommonUtil.getString(instance.get("interface~index")) + ":", instanceRateMetrics, instance, uptime.get(), CommonUtil.getString(entries.get(AIOpsObject.OBJECT_NAME)));
                            }
                            else
                            {
                                instanceRateMetrics.put(NMSConstants.INTERFACE_OUT_TRAFFIC_BYTES_PER_SEC, 0).put(NMSConstants.INTERFACE_OUT_TRAFFIC_UTILIZATION, 0).put(NMSConstants.INTERFACE_IN_TRAFFIC_BYTES_PER_SEC, 0).put(NMSConstants.INTERFACE_IN_TRAFFIC_UTILIZATION, 0).put(NMSConstants.INTERFACE_TRAFFIC_BYTES_PER_SEC, 0).put(NMSConstants.INTERFACE_TRAFFIC_UTILIZATION, 0);
                            }

                            voidInstanceMetrics.add(NMSConstants.INTERFACE_BIT_TYPE);

                            voidInstanceMetrics.add(NMSConstants.INTERFACE_SPEED_BYTES_PER_SEC);
                        }
                    }
                    else
                    {
                        alteredInstances.set(index, instance);
                    }

                    if (NMSConstants.INSTANCE_IP_METRICS.containsKey(key))
                    {
                        ObjectCacheStore.getStore().update(objectId, context, key, CommonUtil.getString(instance.get(key)));
                    }

                    if (statusColumn)
                    {
                        ObjectStatusCacheStore.getStore().updateItem(objectId,
                                key + GlobalConstants.INSTANCE_SEPARATOR + CommonUtil.getString(instance.get(key)) + GlobalConstants.INSTANCE_SEPARATOR + entries.get(PLUGIN_ID),
                                CommonUtil.getString(instance.get(key + GlobalConstants.INSTANCE_SEPARATOR + GlobalConstants.STATUS)),
                                CommonUtil.getLong(entries.get(EVENT_TIMESTAMP)));
                    }

                    if (!instanceRateMetrics.isEmpty())
                    {
                        instance.putAll(instanceRateMetrics.getMap());

                        instanceRateMetrics.clear();
                    }

                    var bytes = CommonUtil.getString(context.get(key)).getBytes(StandardCharsets.UTF_8);

                    instanceMetricBuffer.appendIntLE(bytes.length);

                    instanceMetricBuffer.appendBytes(bytes);

                    for (var metric : instance.entrySet())
                    {
                        var metricName = metric.getKey();

                        var skipMetric = false;

                        if (metricName.endsWith(NMSConstants.METRIC_SKIP_RATE))
                        {
                            instanceRateMetrics.put(metricName.substring(0, metricName.lastIndexOf(".")), CommonUtil.getLong(instance.get(metricName)));

                            voidInstanceMetrics.add(metricName);

                            skipMetric = true;
                        }
                        else if (metricName.endsWith(NMSConstants.METRIC_RATE))
                        {
                            calculateMetricRate(id, metricName, context.get(key) + ":" + metricName, CommonUtil.getLong(instance.get(metricName)), instanceRateMetrics);

                            voidInstanceMetrics.add(metricName);

                            skipMetric = true;
                        }

                        if (!skipMetric && metric.getValue() != null)
                        {
                            updateBuffer(metricName, CommonUtil.getString(metric.getValue()), event.getInteger(PLUGIN_ID), instanceMetricBuffer);

                            // Handle bytes to bits conversion for instance metrics - store in JsonObject
                            // Only process if the metric contains "bytes.per.sec" to avoid unnecessary processing
                            if (metricName.contains("bytes.per.sec"))
                            {
                                processBytesToBitsConversion(metricName, CommonUtil.getString(metric.getValue()), instanceBitsMetrics, event.getInteger(PLUGIN_ID));
                            }
                        }
                    }

                    if (!voidInstanceMetrics.isEmpty())
                    {
                        voidInstanceMetrics.forEach(instance::remove);

                        voidInstanceMetrics.clear();
                    }

                    if (!instanceRateMetrics.isEmpty())
                    {
                        instance.putAll(instanceRateMetrics.getMap());

                        instanceRateMetrics.getMap().forEach((k, v) ->
                        {
                            if (v != null)
                            {
                                updateBuffer(k, CommonUtil.getString(v), event.getInteger(PLUGIN_ID), instanceMetricBuffer);

                                // Handle bytes to bits conversion for instance rate metrics - store in JsonObject
                                // Only process if the metric contains "bytes.per.sec" to avoid unnecessary processing
                                if (k.contains("bytes.per.sec"))
                                {
                                    processBytesToBitsConversion(k, CommonUtil.getString(v), instanceBitsMetrics, event.getInteger(PLUGIN_ID));
                                }
                            }
                        });

                        instanceRateMetrics.clear();
                    }

                    if (!instanceBitsMetrics.isEmpty())
                    {
                        instance.putAll(instanceBitsMetrics.getMap());

                        instanceBitsMetrics.clear();
                    }

                    var instanceTags = TagCacheStore.getStore().getTags(entries.get(AIOpsObject.OBJECT_ID) + KEY_SEPARATOR + key + KEY_SEPARATOR + context.get(event.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(MetricPlugin.SNMP_INTERFACE.getName()) ? NMSConstants.INTERFACE_INDEX : key));

                    if (instanceTags != null && !instanceTags.isEmpty())
                    {
                        tags.addAll(instanceTags);
                    }

                    vertx.eventBus().send(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreConstants.DatastoreCategory.METRIC.getName(), instanceMetricBuffer.getBytes());
                }
            }
        });

        if (!enrichedMetrics.isEmpty())
        {
            result.putAll(enrichedMetrics.getMap());
        }
    }

    private void appendBytes(JsonObject event, Buffer buffer)
    {
        buffer.setLongLE(0, event.getLong(EVENT_TIMESTAMP));

        var plugin = event.getInteger(PLUGIN_ID) + DASH_SEPARATOR + MetricConfigStore.getStore().getMetricName(event.getInteger(PLUGIN_ID));

        var bytes = plugin.getBytes(StandardCharsets.UTF_8);

        buffer.appendIntLE(bytes.length);

        buffer.appendBytes(bytes);

        buffer.appendByte(DatastoreConstants.DatastoreFormat.VERTICAL.getName().byteValue());

        buffer.appendByte(CommonUtil.getByteValue(DatastoreConstants.DatastoreType.PERFORMANCE_METRIC.ordinal()));

        buffer.appendIntLE(event.getInteger(AIOpsObject.OBJECT_ID));
    }

    private void updateBuffer(String column, String value, int pluginId, Buffer buffer)
    {
        try
        {
            if (!column.equalsIgnoreCase(STATUS))
            {
                builder.setLength(0);

                var category = DatastoreConstants.getDataCategory(true, column, value);

                buffer.appendByte(category);

                var bytes = column.getBytes(StandardCharsets.UTF_8);

                buffer.appendIntLE(bytes.length);

                buffer.appendBytes(bytes);

                if (category == DatastoreConstants.DataCategory.FLOAT.getName())
                {
                    ByteUtil.writeDouble(buffer, CommonUtil.getDouble(value));
                }
                else if (category == DatastoreConstants.DataCategory.NUMERIC.getName())//if it has decimal number than convert it into integer
                {
                    buffer.appendLongLE(Math.round(CommonUtil.getDouble(value)));
                }
                else
                {
                    bytes = value.getBytes(StandardCharsets.UTF_8);

                    buffer.appendIntLE(bytes.length);

                    buffer.appendBytes(bytes);
                }

                builder.append(category).append(COLUMN_SEPARATOR).append(pluginId).append(COLUMN_SEPARATOR).append(column).append(COLUMN_SEPARATOR).append(NO);

                if (!mappers.contains(builder.toString()))
                {
                    mappers.add(builder.toString());

                    vertx.eventBus().publish(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE,
                            new JsonObject()
                                    .put(CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_METRIC_COLUMN.name())
                                    .put(DatastoreConstants.MAPPER, builder.toString()));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Calculates rate metrics (changes over time) for counter-type metrics.
     * <p>
     * This method:
     * 1. Retrieves the previous value and timestamp for the metric
     * 2. Calculates the time difference between the current and previous measurements
     * 3. Calculates the rate (change per second) based on the value difference and time difference
     * 4. Handles counter resets (when the current value is less than the previous value)
     * 5. Stores the calculated rate in the rateMetrics JsonObject
     * 6. Updates the context map with the current value and timestamp for future calculations
     * <p>
     * Rate metrics are important for monitoring systems as they show the change in a value over time,
     * which is often more useful than the absolute value (e.g., network traffic rate vs. total bytes).
     *
     * @param id           The ID of the metric or object
     * @param metricName   The name of the metric (with .rate suffix)
     * @param metricKey    The key to use for storing the rate in the rateMetrics object
     * @param currentValue The current value of the metric
     * @param rateMetrics  A JsonObject to store the calculated rate
     */
    private void calculateMetricRate(long id, String metricName, String metricKey, long currentValue, JsonObject rateMetrics)
    {
        var currentTimestamp = DateTimeUtil.currentSeconds();

        metricName = metricName.substring(0, metricName.lastIndexOf('.'));

        Map<String, Long> metricRateContext;

        rateMetrics.put(metricName + ".per.sec", 0);

        if (contextsByMetric.get(id).containsKey(metricKey))
        {
            metricRateContext = contextsByMetric.get(id).get(metricKey);

            var previousValue = metricRateContext.get(VALUE);

            if (currentValue > previousValue)
            {
                previousValue = currentValue - previousValue;

                var previousTimestamp = metricRateContext.get(TIME_STAMP);

                previousTimestamp = currentTimestamp - previousTimestamp; // to get time in seconds need to divide by 1000

                if (previousTimestamp > 0)
                {
                    rateMetrics.put(metricName + ".per.sec", CommonUtil.getFloat(previousValue / previousTimestamp));
                }
            }
        }

        else
        {
            metricRateContext = new HashMap<>();

            contextsByMetric.get(id).put(metricKey, metricRateContext);
        }

        metricRateContext.put(TIME_STAMP, currentTimestamp);

        metricRateContext.put(VALUE, currentValue);

        rateMetrics.put(metricName, currentValue);
    }

    /**
     * Calculates network interface metrics including bandwidth, utilization, and traffic rates.
     * <p>
     * This method:
     * 1. Retrieves previous values and timestamps for the interface
     * 2. Calculates traffic rates for both inbound and outbound traffic
     * 3. Handles counter rollovers (when a 32-bit counter reaches its maximum value and resets)
     * 4. Calculates interface utilization percentages based on traffic rates and interface speed
     * 5. Detects device reboots by comparing uptime values
     * 6. Stores the calculated metrics in the rateMetrics JsonObject
     * 7. Updates the context map with current values for future calculations
     * <p>
     * The method handles different counter types (32-bit vs 64-bit) and accounts for
     * different interface speeds. It also calculates combined metrics like total traffic
     * and average utilization.
     *
     * @param id          The ID of the metric or object
     * @param metricKey   The key used to identify this interface in the context map
     * @param rateMetrics A JsonObject to store the calculated metrics
     * @param context     A map containing the current interface metrics
     * @param uptime      The current system uptime
     * @param objectName  The name of the object (for logging purposes)
     */
    private void calculateInterfaceMetricRate(long id, String metricKey, JsonObject rateMetrics, Map<String, java.lang.Object> context, long uptime, String objectName)
    {
        try
        {
            Map<String, Long> metricRateContext;

            var currentTimestamp = DateTimeUtil.currentSeconds();

            var currentSentBytes = CommonUtil.getLong(context.get(NMSConstants.INTERFACE_SENT_OCTETS));//octet means Bytes

            var currentReceivedBytes = CommonUtil.getLong(context.get(NMSConstants.INTERFACE_RECEIVED_OCTETS));//octet means Bytes

            if (contextsByMetric.get(id).get(metricKey) != null)
            {
                metricRateContext = contextsByMetric.get(id).get(metricKey);

                var previousUptime = metricRateContext.get(NMSConstants.STARTED_TIME);

                if (uptime >= previousUptime)
                {
                    var previousSentBytes = metricRateContext.get(NMSConstants.METRIC_ENRICHER_CALCULATOR_SENT_BYTES);

                    var previousReceivedBytes = metricRateContext.get(NMSConstants.METRIC_ENRICHER_CALCULATOR_RECEIVE_BYTES);

                    var previousTimestamp = metricRateContext.get(TIME_STAMP);

                    var interfaceBitType = CommonUtil.getInteger(context.get(NMSConstants.INTERFACE_BIT_TYPE));

                    var pollTimeDifference = currentTimestamp - previousTimestamp;//time difference between two poll times in seconds

                    var interfaceSpeed = CommonUtil.getFloat(context.get(NMSConstants.INTERFACE_SPEED_BYTES_PER_SEC));

                    if (currentSentBytes > previousSentBytes)
                    {
                        calculateInterfaceBandwidth(currentSentBytes, previousSentBytes, pollTimeDifference, rateMetrics, NMSConstants.INTERFACE_OUT_TRAFFIC_BYTES_PER_SEC, interfaceSpeed);
                    }
                    else
                    {

                        if (interfaceBitType == NMSConstants.INTERFACE_COUNTER_TYPE_32_BIT)//for 32 bit interface rollover
                        {
                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("interface metric has been rolled over for interface %s of object %s", context.get(NMSConstants.INTERFACE), objectName));
                            }

                            currentSentBytes += NMSConstants.MAX_INTERFACE_SPEED_32_BIT;

                            if (currentSentBytes > previousSentBytes)
                            {

                                calculateInterfaceBandwidth(currentSentBytes, previousSentBytes, pollTimeDifference, rateMetrics, NMSConstants.INTERFACE_OUT_TRAFFIC_BYTES_PER_SEC, interfaceSpeed);
                            }
                        }
                    }

                    if (currentReceivedBytes > previousReceivedBytes)
                    {
                        calculateInterfaceBandwidth(currentReceivedBytes, previousReceivedBytes, pollTimeDifference, rateMetrics, NMSConstants.INTERFACE_IN_TRAFFIC_BYTES_PER_SEC, interfaceSpeed);
                    }

                    else
                    {

                        if (interfaceBitType == NMSConstants.INTERFACE_COUNTER_TYPE_32_BIT)//for 32 bit interface rollover
                        {
                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("interface metric has been rolled over for interface %s of object %s", context.get(NMSConstants.INTERFACE), objectName));
                            }

                            currentReceivedBytes += NMSConstants.MAX_INTERFACE_SPEED_32_BIT;

                            if (currentReceivedBytes > previousReceivedBytes)
                            {

                                calculateInterfaceBandwidth(currentSentBytes, previousSentBytes, pollTimeDifference, rateMetrics, NMSConstants.INTERFACE_IN_TRAFFIC_BYTES_PER_SEC, interfaceSpeed);
                            }
                        }
                    }

                    rateMetrics.put(NMSConstants.INTERFACE_TRAFFIC_BYTES_PER_SEC, CommonUtil.getFloat(rateMetrics.getValue(NMSConstants.INTERFACE_IN_TRAFFIC_BYTES_PER_SEC)) + CommonUtil.getFloat(rateMetrics.getValue(NMSConstants.INTERFACE_OUT_TRAFFIC_BYTES_PER_SEC)));

                    rateMetrics.put(NMSConstants.INTERFACE_TRAFFIC_UTILIZATION, CommonUtil.getFloat((CommonUtil.getFloat(rateMetrics.getValue(NMSConstants.INTERFACE_OUT_TRAFFIC_UTILIZATION)) + CommonUtil.getFloat(rateMetrics.getValue(NMSConstants.INTERFACE_IN_TRAFFIC_UTILIZATION))) / 2));
                }

                else
                {
                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("device was rebooted hence discarding the first poll of object %s", objectName));
                    }
                }
            }

            else
            {
                metricRateContext = new HashMap<>();

                contextsByMetric.get(id).put(metricKey, metricRateContext);
            }

            metricRateContext.put(TIME_STAMP, currentTimestamp);

            metricRateContext.put(NMSConstants.METRIC_ENRICHER_CALCULATOR_SENT_BYTES, currentSentBytes);

            metricRateContext.put(NMSConstants.METRIC_ENRICHER_CALCULATOR_RECEIVE_BYTES, currentReceivedBytes);

            metricRateContext.put(NMSConstants.STARTED_TIME, uptime);

        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void calculateInterfaceBandwidth(long currentValue, long previousValue, float timeDiff, JsonObject rateMetrics, String direction, float interfaceSpeed)
    {
        float result;

        if (timeDiff > 0)
        {
            result = CommonUtil.getFloat((currentValue - previousValue) / timeDiff);

            if (direction.equalsIgnoreCase(NMSConstants.INTERFACE_OUT_TRAFFIC_BYTES_PER_SEC))
            {
                rateMetrics.put(NMSConstants.INTERFACE_OUT_TRAFFIC_BYTES_PER_SEC, result);

                rateMetrics.put(NMSConstants.INTERFACE_OUT_TRAFFIC_UTILIZATION, calculateInterfaceUtilization(result, interfaceSpeed));
            }

            else
            {
                rateMetrics.put(NMSConstants.INTERFACE_IN_TRAFFIC_BYTES_PER_SEC, result);

                rateMetrics.put(NMSConstants.INTERFACE_IN_TRAFFIC_UTILIZATION, calculateInterfaceUtilization(result, interfaceSpeed));
            }
        }
    }

    private float calculateInterfaceUtilization(float trafficBytes, float interfaceSpeed)
    {
        var result = 0f;

        if (trafficBytes > 0)
        {
            result = (trafficBytes * 100) / interfaceSpeed;


            if (result > 100)
            {
                //ignore result if utilization exceeds 100

                result = 100;
            }
        }

        return CommonUtil.getFloat(result);
    }

    private void notify(long objectId, JsonObject event)
    {

        if (!pollingErrorTrackers.containsKey(objectId))
        {
            var smsRecipients = new HashSet<>();

            var emailRecipients = new HashSet<>();

            var object = ObjectConfigStore.getStore().getItem(objectId);

            if (object != null && object.containsKey(OBJECT_MONITOR_POLLING_FAILED_NOTIFICATION_STATUS) && object.getString(OBJECT_MONITOR_POLLING_FAILED_NOTIFICATION_STATUS).equalsIgnoreCase(YES))
            {
                if (object.getValue(OBJECT_SMS_NOTIFICATION_RECIPIENTS) != null && !object.getJsonArray(OBJECT_SMS_NOTIFICATION_RECIPIENTS).isEmpty())
                {
                    smsRecipients.addAll(object.getJsonArray(OBJECT_SMS_NOTIFICATION_RECIPIENTS).getList());
                }

                if (object.getValue(OBJECT_EMAIL_NOTIFICATION_RECIPIENTS) != null && !object.getJsonArray(OBJECT_EMAIL_NOTIFICATION_RECIPIENTS).isEmpty())
                {
                    emailRecipients.addAll(object.getJsonArray(OBJECT_EMAIL_NOTIFICATION_RECIPIENTS).getList());
                }

                if (object.containsKey(OBJECT_MONITOR_POLLING_FAILED_RENOTIFICATION_TIMER_SECONDS) && object.getInteger(OBJECT_MONITOR_POLLING_FAILED_RENOTIFICATION_TIMER_SECONDS) > 0)
                {
                    if (CommonUtil.debugEnabled())
                    {

                        LOGGER.debug("Polling error found for monitor : " + objectId);
                    }

                    pollingErrorTrackers.put(objectId, object.getInteger(OBJECT_MONITOR_POLLING_FAILED_RENOTIFICATION_TIMER_SECONDS));
                }
            }

            if (!emailRecipients.isEmpty())
            {
                var groups = new JsonArray();

                var groupBuilder = new StringBuilder();

                event.getJsonArray(AIOpsObject.OBJECT_GROUPS).forEach(group ->
                {
                    groupBuilder.setLength(0);

                    CommonUtil.normalizeGroupHierarchy(GroupConfigStore.getStore().getItem(CommonUtil.getLong(group)), groupBuilder, null);

                    groups.add(groupBuilder.toString());
                });

                Notification.sendEmail(new JsonObject()
                        .put(EventBusConstants.EVENT_REPLY, YES).put(NOTIFICATION_TYPE, Notification.NotificationType.EMAIL.getName())
                        .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                        .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add(Severity.CRITICAL.name().toLowerCase() + ".png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                        .put(Notification.EMAIL_NOTIFICATION_SUBJECT, event.getString(AIOpsObject.OBJECT_IP, AIOpsObject.OBJECT_TARGET) + "- Monitor Polling Failed")
                        .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, new ArrayList<>(emailRecipients))
                        .put(Notification.TEMPLATE_NAME, Notification.EMAIL_NOTIFICATION_POLLING_ERROR_HTML_TEMPLATE)
                        .put(Notification.EMAIL_NOTIFICATION_CONTENT, new JsonObject().mergeIn(event).put(SEVERITY, Severity.CRITICAL.name().toLowerCase()).put(AIOpsObject.OBJECT_GROUPS, StringUtils.join(groups, COMMA_SEPARATOR)).put(TIME_STAMP, DateTimeUtil.timestamp(event.getLong(EVENT_TIMESTAMP) * 1000L))));
            }
            if (!smsRecipients.isEmpty())
            {
                Notification.sendSMS(String.format(ErrorMessageConstants.POLLING_FAILED, ObjectConfigStore.getStore().getObjectName(objectId), event.getString(MESSAGE)), new JsonArray(new ArrayList<>(smsRecipients)));
            }
        }

    }
}
