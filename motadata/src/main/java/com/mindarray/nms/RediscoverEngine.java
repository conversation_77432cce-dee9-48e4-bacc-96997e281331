/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			   Notes
 *  05-Aug-2025     Viram              MOTADATA-6858 Removed redundant code to have atomic operations
 */

package com.mindarray.nms;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorCodes;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.Discovery;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.store.EventCacheStore;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.WorkerExecutor;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.mindarray.ErrorMessageConstants.REDISCOVER_FAILED;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;

/**
 * RediscoverEngine is responsible for managing and executing rediscovery operations for monitored objects.
 * <p>
 * This class handles:
 * - Scheduling and executing rediscovery jobs for different types of objects (VMs, processes, interfaces, etc.)
 * - Managing worker allocation for different rediscovery job types
 * - Batching rediscovery events for efficient processing
 * - Handling rediscovery stop requests and aborting pending jobs
 * - Tracking and reporting health statistics about pending events and idle workers
 * <p>
 * The class operates as a Vert.x verticle and uses the event bus for communication with other components.
 * It implements a worker pool model to efficiently process rediscovery requests in parallel while
 * managing system resources.
 */
public class RediscoverEngine extends AbstractVerticle
{
    /**
     * Logger instance for this class
     */
    private static final Logger LOGGER = new Logger(RediscoverEngine.class, MOTADATA_NMS, "Rediscover Engine");

    /**
     * Map to store regular (non-batch) rediscovery events organized by job type
     */
    private final Map<NMSConstants.RediscoverJob, Map<Long, JsonObject>> eventsByRediscoverJob = new EnumMap<>(NMSConstants.RediscoverJob.class);

    /**
     * Map to store batch rediscovery events organized by job type
     */
    private final Map<NMSConstants.RediscoverJob, Map<Long, JsonObject>> batchEventsByRediscoverJob = new EnumMap<>(NMSConstants.RediscoverJob.class);

    /**
     * Counter for tracking the total number of idle workers across all job types
     */
    private final AtomicInteger idleWorkers = new AtomicInteger(NMSConstants.RediscoverJob.values().length * MotadataConfigUtil.getRediscoveryWorkers());

    /**
     * Map to track the number of idle workers for each job type
     */
    private final Map<NMSConstants.RediscoverJob, Integer> idleWorkersByRediscoverJob = new EnumMap<>(NMSConstants.RediscoverJob.class);

    /**
     * Worker executor for processing rediscovery jobs with a 60-minute timeout
     */
    private final WorkerExecutor workerExecutor = Bootstrap.vertx().createSharedWorkerExecutor("Rediscover Engine", NMSConstants.RediscoverJob.values().length * MotadataConfigUtil.getRediscoveryWorkers(), 60L, TimeUnit.MINUTES);

    /**
     * Counter for tracking consecutive empty event checks to determine when to stop the timer
     */
    private int eventProbes;

    /**
     * Flag indicating whether the periodic timer for processing events is active
     */
    private boolean timeHandlerActive = false;

    /**
     * Initializes the RediscoverEngine verticle.
     * <p>
     * This method:
     * 1. Sets up data structures to track rediscovery events and worker allocation
     * 2. Registers event bus consumers for rediscovery events and stop requests
     * 3. Sets up a periodic timer to process pending rediscovery events
     * 4. Configures worker allocation for different rediscovery job types
     * <p>
     * The method handles different types of rediscovery events based on their job type
     * and plugin engine, and manages worker allocation to ensure efficient resource usage.
     * <p>
     * The RediscoverEngine supports various job types (VMs, processes, interfaces, etc.)
     * and can process them in batches for better performance. It also implements load
     * balancing to distribute work evenly across available workers.
     *
     * @param promise Promise to be completed when initialization is done
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {


        for (var rediscoverJob : NMSConstants.RediscoverJob.values())
        {
            eventsByRediscoverJob.put(rediscoverJob, new HashMap<>());

            batchEventsByRediscoverJob.put(rediscoverJob, new HashMap<>());

            idleWorkersByRediscoverJob.put(rediscoverJob, NMSConstants.getRediscoveryWorkersByJob(rediscoverJob));
        }

        vertx.eventBus().<Void>localConsumer(EventBusConstants.EVENT_ENGINE_STATS, message ->
        {
            if (MotadataConfigUtil.devMode())
            {
                vertx.eventBus().publish(Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_ENGINE_STATS_RESPONSE,
                        new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_ENGINE_STATS)
                                .put(EventBusConstants.ENGINE_TYPE, EVENT_REDISCOVER)
                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, Bootstrap.bootstrapType().name()).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, Bootstrap.getInstallationMode())
                                .put(HealthUtil.HEALTH_STATS, new JsonObject().put(HealthUtil.PENDING_EVENTS, eventsByRediscoverJob.values().stream().map(Map::size).reduce(0, Integer::sum) + batchEventsByRediscoverJob.values().stream().map(Map::size).reduce(0, Integer::sum))
                                        .put(HealthUtil.IDLE_WORKERS, idleWorkers.get())));
            }
            else
            {
                vertx.eventBus().send(Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_ENGINE_STATS_RESPONSE,
                        new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_ENGINE_STATS)
                                .put(EventBusConstants.ENGINE_TYPE, EVENT_REDISCOVER)
                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, Bootstrap.bootstrapType().name()).put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, Bootstrap.getInstallationMode())
                                .put(HealthUtil.HEALTH_STATS, new JsonObject().put(HealthUtil.PENDING_EVENTS, eventsByRediscoverJob.values().stream().map(Map::size).reduce(0, Integer::sum) + batchEventsByRediscoverJob.values().stream().map(Map::size).reduce(0, Integer::sum))
                                        .put(HealthUtil.IDLE_WORKERS, idleWorkers.get())));
            }
        });


        vertx.eventBus().<JsonObject>localConsumer(EVENT_REDISCOVER, message ->
        {
            var event = message.body();

            if (event != null && event.containsKey(NMSConstants.REDISCOVER_JOB))
            {
                if (CommonUtil.debugEnabled())
                {
                    LOGGER.debug(String.format("event received %s ", event.encode()));
                }

                event.put(Discovery.DISCOVER_DOWN_INTERFACE_STATUS, MotadataConfigUtil.getDownInterfaceDiscoveryStatus());

                var events = !event.getString(NMSConstants.REDISCOVER_JOB).equalsIgnoreCase(NMSConstants.RediscoverJob.WAN_LINK.getName()) && PluginEngineConstants.hasBatchSupport(event) ? batchEventsByRediscoverJob.get(NMSConstants.RediscoverJob.valueOfName(event.getString(NMSConstants.REDISCOVER_JOB)))
                        : eventsByRediscoverJob.get(NMSConstants.RediscoverJob.valueOfName(event.getString(NMSConstants.REDISCOVER_JOB)));

                if (!events.containsKey(event.getLong(ID)))
                {
                    events.put(event.getLong(ID), event);
                }

                else
                {

                    vertx.eventBus().send(Bootstrap.bootstrapType() == BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ? EVENT_REMOTE : EVENT_REDISCOVER_RESPONSE,
                            event.put(STATUS, STATUS_ABORT).put(ERROR_CODE, ErrorCodes.ERROR_CODE_DUPLICATE)
                                    .put(MESSAGE, String.format(REDISCOVER_FAILED, String.format("duplicate event [ref id: %s]",
                                            events.get(event.getLong(ID)).getLong(EVENT_ID)))));

                    /* most amazing line of code ... we have to serve multiple user with ad hoc app discovery without any unique or reference id... so
                    only session id is the game plan to notify the progress via websocket... */

                    if (event.getString(APIConstants.SESSION_ID) != null)
                    {
                        if (events.get(event.getLong(ID)).containsKey(EVENT_SUBSCRIBERS))
                        {
                            events.get(event.getLong(ID)).getJsonArray(EVENT_SUBSCRIBERS).add(event.getString(APIConstants.SESSION_ID));

                        }

                        else
                        {
                            events.get(event.getLong(ID)).put(EVENT_SUBSCRIBERS, new JsonArray().add(event.getString(APIConstants.SESSION_ID)));
                        }
                    }

                    EventCacheStore.getStore().deleteItem(event.getLong(EVENT_ID));
                }
            }

            if (!timeHandlerActive)
            {
                eventProbes = 0;

                timeHandlerActive = true;

                // Set up a periodic timer that runs every 1 second to process pending rediscovery events
                vertx.setPeriodic(1000, timer ->
                {
                    // Check if there are any pending rediscovery events
                    if (exists())
                    {
                        // Reset the event probes counter since we found events to process
                        eventProbes = 0;

                        // Only process events if there are idle workers available
                        if (idleWorkers.get() > 0)
                        {
                            var rediscoverJobs = new ArrayList<NMSConstants.RediscoverJob>();

                            var pendingEventsByRediscoverJob = new EnumMap<NMSConstants.RediscoverJob, Integer>(NMSConstants.RediscoverJob.class);

                            var pendingBatchEventsByRediscoverJob = new EnumMap<NMSConstants.RediscoverJob, Integer>(NMSConstants.RediscoverJob.class);

                            var pendingEvents = 0;

                            var pendingBatchEvents = 0;

                            var batchEvents = new ArrayList<Map<Long, JsonObject>>();

                            for (var rediscoverJob : NMSConstants.RediscoverJob.values())
                            {
                                //Checking the License for the Essential , Application, Cloud & WAN Link Discovery won't be accessible to them.
                                if (LicenseUtil.LICENSE_EDITION.get() == LicenseUtil.LicenseEdition.ESSENTIAL)
                                {
                                    if (!(rediscoverJob.getName().equalsIgnoreCase(NMSConstants.RediscoverJob.APP.getName()) || rediscoverJob.getName().equalsIgnoreCase(NMSConstants.RediscoverJob.CLOUD.getName()) || rediscoverJob.getName().equalsIgnoreCase(NMSConstants.RediscoverJob.WAN_LINK.getName())))
                                    {
                                        pendingBatchEvents += dequeue(rediscoverJob, rediscoverJobs, pendingBatchEventsByRediscoverJob, batchEvents, PluginEngineConstants.PluginEngine.GO);

                                        pendingEvents += dequeue(rediscoverJob, rediscoverJobs, pendingEventsByRediscoverJob, batchEvents, PluginEngineConstants.PluginEngine.PYTHON);
                                    }
                                }
                                else
                                {
                                    pendingBatchEvents += dequeue(rediscoverJob, rediscoverJobs, pendingBatchEventsByRediscoverJob, batchEvents, PluginEngineConstants.PluginEngine.GO);

                                    pendingEvents += dequeue(rediscoverJob, rediscoverJobs, pendingEventsByRediscoverJob, batchEvents, PluginEngineConstants.PluginEngine.PYTHON);
                                }
                            }

                            if (idleWorkers.get() > 0)
                            {
                                if (pendingBatchEvents > 0)
                                {
                                    dequeue(pendingBatchEventsByRediscoverJob, rediscoverJobs, batchEvents, PluginEngineConstants.PluginEngine.GO);
                                }

                                if (pendingEvents > 0)
                                {
                                    dequeue(pendingEventsByRediscoverJob, rediscoverJobs, batchEvents, PluginEngineConstants.PluginEngine.PYTHON);
                                }
                            }

                            for (var index = 0; index < batchEvents.size(); index++)
                            {
                                var rediscoverJob = rediscoverJobs.get(index);

                                var contexts = batchEvents.get(index);

                                if (!contexts.isEmpty())
                                {
                                    var eventIds = contexts.values().stream().map(item -> item.getLong(EVENT_ID)).collect(Collectors.toList());

                                    workerExecutor.<Void>executeBlocking(future ->
                                    {
                                        for (var eventId : eventIds)
                                        {
                                            EventBusConstants.startEvent(eventId, Thread.currentThread().getName());
                                        }

                                        var context = contexts.values().stream().findFirst().get();

                                        if (context.getString(NMSConstants.REDISCOVER_JOB).equalsIgnoreCase(NMSConstants.RediscoverJob.NETWORK_SERVICE.getName()))
                                        {
                                            try
                                            {
                                                var objects = context.getJsonArray(NMSConstants.OBJECTS);

                                                if (objects != null && !objects.isEmpty())
                                                {
                                                    PortUtil.isOpen(context.getString(AIOpsObject.OBJECT_IP), objects).onComplete(result ->
                                                    {

                                                        if (result.succeeded())
                                                        {
                                                            if (result.result() != null && !result.result().isEmpty())
                                                            {
                                                                var probes = new JsonArray();

                                                                for (var i = 0; i < result.result().size(); i++)
                                                                {
                                                                    var probe = result.result().getJsonObject(i);

                                                                    if (probe.containsKey(STATUS) && probe.getString(STATUS).equalsIgnoreCase(STATUS_UP))
                                                                    {
                                                                        probes.add(probe);
                                                                    }
                                                                }

                                                                send(context.put(STATUS, STATUS_SUCCEED).put(RESULT, new JsonObject()
                                                                        .put(NMSConstants.OBJECTS, probes)));
                                                            }

                                                            else
                                                            {
                                                                send(context.put(STATUS, STATUS_FAIL)
                                                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                                        .put(MESSAGE, String.format(REDISCOVER_FAILED, UNKNOWN)));
                                                            }
                                                        }

                                                        else
                                                        {
                                                            send(context.put(STATUS, STATUS_FAIL)
                                                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                                    .put(MESSAGE, String.format(REDISCOVER_FAILED, result.cause().getMessage()))
                                                                    .put(ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));
                                                        }

                                                        future.complete();
                                                    });
                                                }

                                                else
                                                {
                                                    send(context.put(STATUS, STATUS_FAIL)
                                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                                                            .put(MESSAGE, String.format(REDISCOVER_FAILED, "Port not found")));

                                                    future.complete();
                                                }
                                            }
                                            catch (Exception exception)
                                            {
                                                LOGGER.error(exception);

                                                future.complete();
                                            }
                                        }

                                        else
                                        {
                                            try
                                            {
                                                var pluginEngine = PluginEngineConstants.PluginEngine.valueOfName(context.getString(PluginEngineConstants.PLUGIN_ENGINE));

                                                WorkerUtil.spawnWorker(contexts, context, eventIds,
                                                        rediscoverJob == NMSConstants.RediscoverJob.CLOUD || rediscoverJob == NMSConstants.RediscoverJob.WAN_LINK ? context.getInteger(TIMEOUT, 60) : pluginEngine == PluginEngineConstants.PluginEngine.GO ? MotadataConfigUtil.getRediscoveryBatchTimeoutSeconds() : context.getInteger(TIMEOUT, 60),
                                                        false, pluginEngine, System.currentTimeMillis(), true);
                                            }

                                            catch (Exception exception)
                                            {
                                                LOGGER.error(exception);
                                            }

                                            finally
                                            {
                                                future.complete();
                                            }
                                        }
                                    }, false, result -> complete(rediscoverJob));
                                }
                                else
                                {
                                    complete(rediscoverJob);
                                }
                            }
                        }
                    }
                    else
                    {
                        eventProbes++;
                    }

                    if (eventProbes > 10) //max 10 probes...
                    {
                        //disable timer to save cpu cycle...

                        vertx.cancelTimer(timer);

                        timeHandlerActive = false;
                    }
                });
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_REDISCOVER_STOP, message ->
        {
            var event = message.body();

            if (Bootstrap.bootstrapType() == BootstrapType.APP)
            {
                vertx.eventBus().send(EventBusConstants.EVENT_PUBLICATION, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_CHANGE_NOTIFICATION)
                        .put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)
                        .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE,
                                EventBusConstants.ChangeNotificationType.STOP_REDISCOVER_JOB.name()).mergeIn(event));
            }

            abort(this.batchEventsByRediscoverJob, event.getString(APIConstants.SESSION_ID), NMSConstants.RediscoverJob.valueOfName(event.getString(NMSConstants.REDISCOVER_JOB)), event.getJsonArray(APIConstants.REQUEST_PARAM_IDS));

            abort(this.eventsByRediscoverJob, event.getString(APIConstants.SESSION_ID), NMSConstants.RediscoverJob.valueOfName(event.getString(NMSConstants.REDISCOVER_JOB)), event.getJsonArray(APIConstants.REQUEST_PARAM_IDS));
        }).exceptionHandler(LOGGER::error);


        promise.complete();
    }

    /**
     * Checks if any rediscovery requests are pending.
     * <p>
     * This method iterates through all rediscovery job types and checks if there are
     * any pending events in either the regular or batch event maps. It's used to determine
     * whether the periodic timer should continue running or be stopped to save resources.
     *
     * @return true if there are pending rediscovery requests, false otherwise
     */
    private boolean exists()
    {
        var valid = false;

        for (var rediscoverJob : NMSConstants.RediscoverJob.values())
        {
            if (!eventsByRediscoverJob.get(rediscoverJob).isEmpty() || !batchEventsByRediscoverJob.get(rediscoverJob).isEmpty())
            {
                valid = true;

                break;
            }
        }

        return valid;
    }

    /**
     * Marks a rediscovery job as completed and updates worker availability.
     * <p>
     * This method is called when a rediscovery job finishes execution. It:
     * 1. Increments the global idle worker count
     * 2. Increments the idle worker count for the specific rediscovery job type
     * <p>
     * This ensures that workers are properly released and made available for
     * processing other rediscovery jobs.
     *
     * @param rediscoverJob The type of rediscovery job that has completed
     */
    private void complete(NMSConstants.RediscoverJob rediscoverJob)
    {
        idleWorkers.getAndIncrement();

        idleWorkersByRediscoverJob.put(rediscoverJob, idleWorkersByRediscoverJob.get(rediscoverJob) + 1);
    }

    /**
     * Sends rediscovery event responses to the appropriate event bus address.
     * <p>
     * This method:
     * 1. Updates the event tracker with a response sent message
     * 2. Determines the appropriate event bus address based on the bootstrap type:
     * - For COLLECTOR or SECONDARY installation mode: Sends to EVENT_REMOTE with REMOTE_EVENT_PROCESSOR_TOPIC
     * - For other bootstrap types: Sends to EVENT_REDISCOVER_RESPONSE
     * <p>
     * The method handles the routing of rediscovery results differently based on
     * the deployment architecture. In collector or secondary mode, results are sent
     * to a remote event processor; otherwise, they're published locally.
     *
     * @param event The JsonObject containing the rediscovery results to send
     */
    private void send(JsonObject event)
    {
        EventBusConstants.updateEvent(event.getLong(EventBusConstants.EVENT_ID), String.format(InfoMessageConstants.EVENT_TRACKER_RESPONSE_SENT, DateTimeUtil.timestamp()));

        if (Bootstrap.bootstrapType() == BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()))
        {
            vertx.eventBus().send(EventBusConstants.EVENT_REMOTE, event.put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC));
        }
        else
        {
            vertx.eventBus().send(EVENT_REDISCOVER_RESPONSE, event);
        }
    }


    /**
     * Dequeues events for a specific rediscovery job type and plugin engine.
     * <p>
     * This method:
     * 1. Checks if there are idle workers available for the specified rediscovery job type
     * 2. Retrieves events from the appropriate event map (batch or regular) based on the plugin engine
     * 3. Processes the events and organizes them into batches for execution
     * 4. Tracks the number of pending events for the job type
     * <p>
     * The method is used to prepare events for a specific job type and plugin engine
     * combination before they are executed by worker threads.
     *
     * @param rediscoverJob                The type of rediscovery job to process
     * @param rediscoverJobs               List to track which rediscovery job types are being processed
     * @param pendingEventsByRediscoverJob Map to track pending event counts by job type
     * @param batches                      List of event batches to be processed
     * @param pluginEngine                 The plugin engine (GO or PYTHON) that will process the events
     * @return The number of pending events for the specified rediscovery job type
     */
    private int dequeue(NMSConstants.RediscoverJob rediscoverJob, List<NMSConstants.RediscoverJob> rediscoverJobs,
                        Map<NMSConstants.RediscoverJob, Integer> pendingEventsByRediscoverJob, List<Map<Long, JsonObject>> batches, PluginEngineConstants.PluginEngine pluginEngine)
    {
        var result = 0;

        try
        {
            var events = idleWorkersByRediscoverJob.get(rediscoverJob) > 0 && idleWorkers.get() > 0 ? pluginEngine == PluginEngineConstants.PluginEngine.GO ? this.batchEventsByRediscoverJob.get(rediscoverJob) : this.eventsByRediscoverJob.get(rediscoverJob) : null;

            if (events != null && !events.isEmpty())
            {
                pendingEventsByRediscoverJob.put(rediscoverJob,
                        dequeue(events, idleWorkersByRediscoverJob.get(rediscoverJob), rediscoverJob, batches, rediscoverJobs, rediscoverJob, pluginEngine));

                result += pendingEventsByRediscoverJob.get(rediscoverJob);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
        return result;
    }

    /**
     * Dequeues events for processing and organizes them into batches.
     * <p>
     * This method is the core of the worker allocation system. It:
     * 1. Determines the appropriate batch size based on job type and plugin engine
     * 2. Calculates how many workers can be allocated based on available resources
     * 3. Creates batches of events for processing
     * 4. Updates worker availability counts
     *
     * @param events                 Map of events to process
     * @param pendingEvents          Number of available workers for this job type
     * @param rediscoverJob          The job type being processed
     * @param batches                List to store created batches
     * @param rediscoverJobs         List to track job types for each batch
     * @param qualifiedRediscoverJob The original job type (may differ from rediscoverJob in load balancing)
     * @param pluginEngine           The plugin engine type (GO or PYTHON)
     * @return Number of remaining events after dequeuing
     */
    private int dequeue(Map<Long, JsonObject> events, int pendingEvents, NMSConstants.RediscoverJob rediscoverJob, List<Map<Long, JsonObject>> batches, List<NMSConstants.RediscoverJob> rediscoverJobs,
                        NMSConstants.RediscoverJob qualifiedRediscoverJob, PluginEngineConstants.PluginEngine pluginEngine)
    {
        var iterator = events.entrySet().iterator();

        // Determine batch size based on job type and plugin engine
        // Network service jobs and Python-based plugins use batch size of 1
        // Other jobs use the configured batch size
        var batchSize = qualifiedRediscoverJob == NMSConstants.RediscoverJob.NETWORK_SERVICE || pluginEngine == PluginEngineConstants.PluginEngine.PYTHON ? 1 : MotadataConfigUtil.getRediscoveryBatchSize();

        // Calculate how many workers can be allocated based on event count, batch size, and available workers
        var workers = WorkerUtil.getWorkers(events.size(), batchSize, pendingEvents);

        var allocations = 0;

        // Create batches until we've allocated all workers or run out of events
        while (allocations < workers && iterator.hasNext())
        {
            // Create a new batch for this allocation
            var batchEvents = new HashMap<Long, JsonObject>();
            batches.add(batchEvents);
            rediscoverJobs.add(rediscoverJob);

            // Fill the batch with up to batchSize events
            for (var index = 0; index < batchSize && iterator.hasNext(); index++)
            {
                var item = iterator.next().getValue();

                // Only include valid events (those still in the event cache)
                if (EventCacheStore.getStore().validItem(item.getLong(EVENT_ID)))
                {
                    batchEvents.put(item.getLong(EVENT_ID), item);
                }

                // Remove the event from the pending events map
                iterator.remove();
            }

            allocations++;
        }

        // Update worker availability counts
        idleWorkers.addAndGet(-workers);

        idleWorkersByRediscoverJob.put(rediscoverJob, pendingEvents - workers);

        // Return the number of remaining events
        return events.size();
    }

    /**
     * Handles load balancing of rediscovery jobs between different job types.
     * <p>
     * This method is used when one job type has idle workers and another has pending events.
     * It allows workers from one job type to process events from another job type,
     * improving overall resource utilization.
     *
     * @param rediscoverJobs               List to track job types for each batch
     * @param pendingEventsByRediscoverJob Map tracking pending event counts by job type
     * @param entry1                       The job type with pending events
     * @param entry2                       The job type with available workers
     * @param batches                      List to store created batches
     * @param pluginEngine                 The plugin engine type (GO or PYTHON)
     */
    private void dequeue(List<NMSConstants.RediscoverJob> rediscoverJobs, Map<NMSConstants.RediscoverJob, Integer> pendingEventsByRediscoverJob,
                         Map.Entry<NMSConstants.RediscoverJob, Integer> entry1, Map.Entry<NMSConstants.RediscoverJob, Integer> entry2, List<Map<Long, JsonObject>> batches,
                         PluginEngineConstants.PluginEngine pluginEngine)
    {
        try
        {
            // Select the appropriate event map based on plugin engine type
            // GO engine uses batch events, PYTHON engine uses regular events
            var events = pluginEngine == PluginEngineConstants.PluginEngine.GO ?
                    this.batchEventsByRediscoverJob.get(entry1.getKey()) :
                    this.eventsByRediscoverJob.get(entry1.getKey());

            if (events != null && !events.isEmpty())
            {
                // Dequeue events using workers from entry2 (the job type with available workers)
                // but processing events from entry1 (the job type with pending events)
                pendingEventsByRediscoverJob.put(entry1.getKey(),
                        dequeue(events, entry2.getValue(), entry2.getKey(), batches, rediscoverJobs, entry1.getKey(), pluginEngine));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Implements load balancing across all rediscovery job types.
     * <p>
     * This method is the top-level load balancer that:
     * 1. Sorts job types by available workers (most to least)
     * 2. Sorts job types by pending events (most to least)
     * 3. Matches job types with pending events to job types with available workers
     * 4. Delegates to the other dequeue methods to process the events
     * <p>
     * This approach ensures efficient resource utilization by allowing workers from
     * any job type to process events from any other job type when needed.
     *
     * @param pendingEventsByRediscoverJob Map tracking pending event counts by job type
     * @param rediscoverJobs               List to track job types for each batch
     * @param batchEvents                  List to store created batches
     * @param pluginEngine                 The plugin engine type (GO or PYTHON)
     */
    private void dequeue(Map<NMSConstants.RediscoverJob, Integer> pendingEventsByRediscoverJob, List<NMSConstants.RediscoverJob> rediscoverJobs, List<Map<Long, JsonObject>> batchEvents, PluginEngineConstants.PluginEngine pluginEngine)
    {
        // Sort job types by available workers (most to least)
        // This prioritizes job types with more available workers for processing
        var sortedByWorkers = idleWorkersByRediscoverJob.entrySet()
                .stream()
                .filter(entry -> entry.getValue() > 0)
                .sorted((Map.Entry.<NMSConstants.RediscoverJob, Integer>comparingByValue().reversed()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));

        // Process job types with pending events (most to least)
        // For each job type with pending events, find job types with available workers
        pendingEventsByRediscoverJob.entrySet()
                .stream()
                .filter(entry -> entry.getValue() > 0)
                .sorted((Map.Entry.<NMSConstants.RediscoverJob, Integer>comparingByValue().reversed()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new)).entrySet()
                .stream()
                // Only process while we still have idle workers available
                .takeWhile(entry1 -> idleWorkers.get() > 0)
                .forEach(entry1 -> sortedByWorkers.entrySet()
                        .stream()
                        // Only process while we still have idle workers available
                        .takeWhile(entry2 -> idleWorkers.get() > 0)
                        // Dequeue events from entry1 (job with pending events) using workers from entry2 (job with available workers)
                        .forEach(entry2 -> dequeue(rediscoverJobs, pendingEventsByRediscoverJob, entry1, entry2, batchEvents, pluginEngine)));
    }

    /**
     * Aborts pending rediscovery jobs based on specified criteria.
     * <p>
     * This method:
     * 1. Finds events for the specified rediscovery job type
     * 2. Filters events that match the provided scheduler IDs
     * 3. Updates the event status to ABORT with an appropriate error message
     * 4. Sends notifications to the appropriate event bus addresses
     * 5. Removes the aborted events from the tracking map
     * <p>
     * The method handles user-initiated abort requests, ensuring that all relevant
     * components are notified about the aborted rediscovery operations.
     *
     * @param eventsByRediscoverJob Map of rediscovery events organized by job type
     * @param sessionId             The session ID of the user who initiated the abort
     * @param rediscoverJob         The type of rediscovery job to abort
     * @param schedulerIds          List of scheduler IDs to abort
     */
    private void abort(Map<NMSConstants.RediscoverJob, Map<Long, JsonObject>> eventsByRediscoverJob, String sessionId, NMSConstants.RediscoverJob rediscoverJob, JsonArray schedulerIds)
    {
        if (eventsByRediscoverJob.containsKey(rediscoverJob))
        {
            var iterator = eventsByRediscoverJob.get(rediscoverJob).entrySet().iterator();

            while (iterator.hasNext())
            {
                var event = iterator.next().getValue();

                if (event != null && schedulerIds.contains(event.getLong(EVENT_SCHEDULER)))
                {
                    event.put(STATUS, STATUS_ABORT).put(ERROR_CODE, ErrorCodes.ERROR_CODE_MANUAL_ABORTED).put(MESSAGE, String.format(REDISCOVER_FAILED, "User aborted the event"));

                    EventCacheStore.getStore().deleteItem(event.getLong(EventBusConstants.EVENT_ID));

                    if (event.containsKey(APIConstants.SESSION_ID) && event.getString(APIConstants.SESSION_ID).equalsIgnoreCase(sessionId))
                    {
                        vertx.eventBus().send(Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR || Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) ?
                                EventBusConstants.EVENT_REMOTE : EventBusConstants.EVENT_REDISCOVER_RESPONSE, event);
                    }

                    send(event);

                    iterator.remove();
                }
            }
        }
    }


    /**
     * Cleans up resources when the RediscoverEngine verticle is stopped.
     * <p>
     * This method:
     * 1. Closes the worker executor to release thread resources
     * 2. Completes the promise to signal successful cleanup
     * <p>
     * Closing the worker executor ensures that any pending or in-progress tasks
     * are properly completed or terminated, and that thread resources are released.
     *
     * @param promise Promise to be completed when cleanup is done
     */
    @Override
    public void stop(Promise<Void> promise)
    {
        workerExecutor.close();

        promise.complete();
    }
}
