/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */
/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  31-Jul-2025		Aagam Salot		    MOTADATA-6874: DNS Flush supports in windows
 */

/*
 * Change Logs:
 *  Date			Author			         Notes
 *  23-Jul-2025	    Nikunj Patel		     MOTADATA-6236: Added DNS profile, lookup and reverseLookup functionality.
 */

package com.mindarray.util;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.DNSServerProfile;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.DNSCacheStore;
import com.mindarray.store.DNSServerProfileConfigStore;
import com.mindarray.store.ObjectConfigStore;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.dns.DnsClient;
import io.vertx.core.dns.DnsClientOptions;
import io.vertx.core.eventbus.Message;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.xbill.DNS.ResolverConfig;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;

/**
 * DNS Utility class that provides comprehensive DNS resolution services for the Motadata platform.
 *
 * <p>This class extends {@link AbstractVerticle} and serves as the central DNS management component,
 * handling both forward and reverse DNS lookups, DNS cache management, and DNS server profile configuration.
 * It automatically discovers system DNS servers and provides event-driven DNS resolution services.</p>
 *
 * <p><strong>Key Features:</strong></p>
 * <ul>
 *   <li>Automatic discovery and configuration of system DNS servers</li>
 *   <li>Forward DNS resolution (hostname to IP)</li>
 *   <li>Reverse DNS resolution (IP to hostname)</li>
 *   <li>DNS cache management and flushing</li>
 *   <li>Multiple DNS server profile support with fallback mechanism</li>
 *   <li>Event-driven architecture for asynchronous DNS operations</li>
 *   <li>Integration with Motadata's object management system</li>
 * </ul>
 *
 * <p><strong>DNS Server Priority:</strong></p>
 * <p>DNS servers are prioritized with automatic (system-discovered) servers taking precedence
 * over manually configured ones. The class implements a fallback mechanism where if one DNS
 * server fails to resolve a query, it automatically tries the next available server.</p>
 */
public class DNSUtil extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(DNSUtil.class, GlobalConstants.MOTADATA_UTIL, "DNS Util");

    /**
     * Initializes the DNS resolver module and sets up the complete DNS management infrastructure.
     *
     * <p>This method serves as the entry point for the DNS utility service, performing comprehensive
     * initialization of DNS server profiles, event bus consumers, and system integration. It automatically
     * discovers system DNS servers and configures them as default profiles if they don't already exist.</p>
     *
     * <p><strong>Initialization Process:</strong></p>
     * <ol>
     *   <li><strong>System DNS Discovery:</strong> Uses {@link ResolverConfig#getCurrentConfig()} to discover
     *       DNS servers configured in the system's network settings</li>
     *   <li><strong>Profile Creation:</strong> For each discovered DNS server, checks if a profile already
     *       exists in {@code DNSServerProfileConfigStore}. If not, creates a new profile with default settings</li>
     *   <li><strong>Database Persistence:</strong> Saves new DNS profiles to the database table
     *       {@code TBL_DNS_SERVER_PROFILE} with automatic resolver type</li>
     *   <li><strong>Event Bus Registration:</strong> Sets up local consumers for all DNS-related events</li>
     * </ol>
     *
     * <p><strong>Event Bus Consumers Registered:</strong></p>
     * <ul>
     *   <li><strong>{@code EVENT_DNS_SERVER_RESOLVER}:</strong> Triggers bulk DNS resolution for all cached IPs and objects</li>
     *   <li><strong>{@code EVENT_DNS_OBJECT_DELETE}:</strong> Handles cleanup when objects are deleted from the system</li>
     *   <li><strong>{@code EVENT_DNS_LOOKUP}:</strong> Performs forward DNS lookup for a single hostname</li>
     *   <li><strong>{@code EVENT_DNS_REVERSE_LOOKUP}:</strong> Performs reverse DNS lookup for a single IP address</li>
     *   <li><strong>{@code EVENT_DNS_CACHE_FLUSH}:</strong> Flushes both system and application DNS caches</li>
     * </ul>
     *
     * @param promise A {@link Promise} that will be completed when initialization succeeds or fails.
     *                Success indicates that all event consumers are registered and the service is ready
     *                to handle DNS requests. Failure indicates a critical error that prevents service startup.
     * @throws Exception If there is an unrecoverable error during startup, such as failure to access
     *                   system DNS configuration or register event bus consumers
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_DNS_RESOLVER, message -> resolve(DNSCacheStore.getStore().getIps(), ObjectConfigStore.getStore().getItems(), sort(), 0));

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_DNS_OBJECT_DELETE, message -> delete(message.body()));

            vertx.eventBus().<String>localConsumer(EventBusConstants.EVENT_DNS_LOOKUP, message -> resolve(message, sort(), 0, false));

            vertx.eventBus().<String>localConsumer(EventBusConstants.EVENT_DNS_REVERSE_LOOKUP, message -> resolve(message, sort(), 0, true));

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_DNS_CACHE_FLUSH, message ->
                    vertx.executeBlocking(future ->
                    {
                        try
                        {
                            var arguments = new ArrayList<String>();

                            if(OS_WINDOWS)
                            {
                                arguments.add("cmd.exe");

                                arguments.add("/c");

                                arguments.add("ipconfig /flushdns");
                            }
                            else
                            {
                                arguments.add("bash");

                                arguments.add("-c");

                                arguments.add("sudo systemd-resolve --flush-caches");
                            }

                            var processBuilder = new ProcessBuilder(arguments);

                            processBuilder.directory(new File(CURRENT_DIR + PATH_SEPARATOR));

                            var process = processBuilder.start();

                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("DNS cache flush process %s started", process.pid()));
                            }

                            var output = new StringBuilder();

                            var bufferedReader = new BufferedReader(new InputStreamReader(process.getInputStream()));

                            String line;

                            while ((line = bufferedReader.readLine()) != null)
                            {
                                output.append(line).append(NEW_LINE);
                            }

                            bufferedReader.close();

                            process.getErrorStream().close();

                            process.getOutputStream().close();

                            process.getInputStream().close();

                            process.destroy();

                            LOGGER.debug(String.format("Output flush DNS %s",output));

                            if ((!output.isEmpty() && !OS_WINDOWS) || (OS_WINDOWS && !output.isEmpty() && !output.toString().contains("Successfully flushed"))) //means getting some error so will not clear records
                            {
                                LOGGER.warn("Failed to flush DNS cache.....");

                                future.fail("Failed to flush DNS cache.....");
                            }
                            else
                            {
                                Bootstrap.configDBService().drop(DBConstants.TBL_DNS_RECORD, result ->
                                {
                                    if (result.succeeded())
                                    {
                                        DNSCacheStore.getStore().clear();

                                        LOGGER.info("DNS records flushed successfully...");

                                        future.complete();
                                    }
                                    else
                                    {
                                        LOGGER.error(result.cause());

                                        future.fail(result.cause().getMessage());
                                    }
                                });
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            future.fail(exception);
                        }
                    }, false, result ->
                    {
                    })).exceptionHandler(LOGGER::error);

            addSystemDNS();

            promise.complete();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }
    }

    /**
     * Resolves a single hostname or IP address using the available DNS server profiles with fallback support.
     *
     * <p>This method implements a recursive fallback mechanism that tries each DNS server profile in sequence
     * until a successful resolution is achieved or all servers have been exhausted. It handles both forward
     * and reverse DNS lookups based on the {@code reverseLookup} parameter.</p>
     *
     * <p><strong>Resolution Process:</strong></p>
     * <ol>
     *   <li>Attempts resolution using the DNS server at the current index</li>
     *   <li>If successful, replies to the message and optionally caches the result</li>
     *   <li>If failed, recursively tries the next DNS server in the list</li>
     *   <li>Continues until resolution succeeds or all servers are exhausted</li>
     * </ol>
     *
     * <p><strong>Caching Behavior:</strong></p>
     * <p>Successful resolutions are automatically cached in the DNS record table for future reference,
     * improving performance for subsequent lookups of the same hostname or IP.</p>
     *
     * @param message       The event bus message containing the hostname or IP to resolve
     * @param profiles      Array of DNS server profiles to use for resolution, sorted by priority
     * @param index         Current index in the profiles array (used for recursive fallback)
     * @param reverseLookup {@code true} for reverse DNS lookup (IP to hostname),
     *                      {@code false} for forward lookup (hostname to IP)
     */
    private void resolve(Message<String> message, JsonArray profiles, int index, boolean reverseLookup)
    {
        try
        {
            if (profiles.size() <= index)
            {
                return;
            }

            var host = message.body();

            var profile = profiles.getJsonObject(index);

            var dnsClient = getClient(profile.getJsonObject(DNSServerProfile.DNS_SERVER_PROFILE_CONTEXT));

            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace(String.format("Sending host %s for lookup on dns server %s", host, profile.getString(DNSServerProfile.DNS_SERVER_PROFILE_NAME)));
            }

            run(dnsClient, host, reverseLookup).onComplete(result ->
            {
                try
                {
                    if (result.succeeded())
                    {
                        message.reply(result.result());

                        if (result.result() != null)
                        {
                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("IP resolved for %s host. Result : %s", host, result.result()));
                            }

                            Bootstrap.configDBService().save(DBConstants.TBL_DNS_RECORD, new JsonObject().put("ip", message.body()).put("host", result.result()), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult -> {});
                        }

                        resolve(message, profiles, profiles.size(), reverseLookup); // To break recursion...
                    }
                    else
                    {
                        LOGGER.warn(String.format("DNS Lookup failed for %s on dns server %s with reason : %s", host, profile.getJsonObject(DNSServerProfile.DNS_SERVER_PROFILE_CONTEXT).getString(DNSServerProfile.DNS_SERVER_IP) ,result.cause().getMessage()));

                        resolve(message, profiles, index + 1, reverseLookup);
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    resolve(message, profiles, index + 1, reverseLookup);
                }
                finally
                {
                    dnsClient.close();
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            resolve(message, profiles, index + 1, reverseLookup);
        }
    }

    /**
     * Sorts DNS server profiles by resolver type, prioritizing automatic (system-discovered) servers.
     *
     * <p>This method retrieves all DNS server profiles from the configuration store and sorts them
     * based on their resolver type. Automatic DNS servers (those discovered from the system configuration)
     * are given higher priority than manually configured servers.</p>
     *
     * <p><strong>Sorting Logic:</strong></p>
     * <ul>
     *   <li>Servers with resolver type {@code AUTOMATIC} are placed first</li>
     *   <li>Manually configured servers are placed after automatic ones</li>
     *   <li>Within each category, the original order is preserved</li>
     * </ul>
     *
     * <p>This prioritization ensures that system-discovered DNS servers, which are typically
     * more reliable and properly configured for the local network environment, are tried first
     * before falling back to user-configured servers.</p>
     *
     * @return A {@link JsonArray} containing DNS server profiles sorted by priority,
     *         with automatic servers first, followed by manual servers
     */
    private JsonArray sort()
    {
        return new JsonArray(DNSServerProfileConfigStore.getStore().getItems()
                .stream()
                .sorted((profile1, profile2) -> Boolean.compare(isAutomatic(JsonObject.mapFrom(profile2)), isAutomatic(JsonObject.mapFrom(profile1))))
                .map(JsonObject::mapFrom)
                .collect(Collectors.toList()));
    }

    /**
     * Checks if a DNS server profile is of automatic resolver type.
     * @param profile Profile
     * @return Flag
     */
    private boolean isAutomatic(JsonObject profile)
    {
        return DNSServerProfile.ResolverType.AUTOMATIC.getName().equalsIgnoreCase(profile.getString(DNSServerProfile.DNS_SERVER_RESOLVER_TYPE, ""));
    }

    /**
     * Performs DNS and reverse DNS resolution using the provided DNS server.
     *
     * <p>This method is part of a recursive strategy that iterates through all available DNS server profiles
     * to resolve:</p>
     * <ul>
     *   <li>Hostnames for objects that don't already have IP addresses</li>
     *   <li>Reverse lookup (PTR) for a given set of IPs</li>
     * </ul>
     *
     * <p>For each object and IP, asynchronous DNS resolution is performed using Vert.x DNS client.
     * Successful resolutions are tracked and stored. Once all lookups for the current server are complete,
     * the DNS server profile is updated with the resolution metadata, and the method recursively continues with
     * the next DNS server.</p>
     *
     * @param ips         Set of IPs for which reverse resolution is to be performed.
     * @param objects     List of objects (in JSON form) that require forward DNS resolution.
     * @param profiles  List of available DNS server profiles to use, sorted by preference.
     * @param profileNumber       Current index in the dnsServers list to use for resolution.
     */
    private void resolve(Set<String> ips, JsonArray objects, JsonArray profiles, int profileNumber)
    {
        vertx.<Void>executeBlocking(event ->
        {
            if (profiles.size() == profileNumber)
            {
                return;
            }

            var profile = profiles.getJsonObject(profileNumber); // current DNS server profile

            var resolvedObjects = new HashSet<Long>(); // Stores successfully resolved object IDs

            var resolvedSources = new JsonArray(); // Stores successfully resolved IPs (reverse)

            var removableIps = new HashSet<String>(); // IPs that were successfully reverse-resolved

            var futures = new ArrayList<Future<Void>>();

            var dnsClient = getClient(profile.getJsonObject(DNSServerProfile.DNS_SERVER_PROFILE_CONTEXT));

            var startTime = DateTimeUtil.currentMilliSeconds();

            for (var count = 0; count < objects.size(); count++)
            {
                var future = Promise.<Void>promise();

                futures.add(future.future());

                try
                {
                    var object = objects.getJsonObject(count);

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("Resolving Host %s on dns server %s", object.getString(AIOpsObject.OBJECT_HOST), profile.getJsonObject(DNSServerProfile.DNS_SERVER_PROFILE_CONTEXT).getString(DNSServerProfile.DNS_SERVER_IP)));
                    }

                    run(dnsClient, object.getString(AIOpsObject.OBJECT_HOST), false).onComplete(result ->
                    {
                        try
                        {
                            if (result.succeeded())
                            {
                                objects.remove(object);

                                // store result in dns server profile.
                                resolvedObjects.add(object.getLong(ID));

                                // Update object IP if different from cached one
                                if (!object.getString(AIOpsObject.OBJECT_IP).equalsIgnoreCase(result.result()))
                                {
                                    updateObject(object, result.result());
                                }
                            }
                            else
                            {
                                LOGGER.warn(String.format("DNS Lookup failed for %s on dns server %s with reason : %s", object.getString(AIOpsObject.OBJECT_HOST), profile.getJsonObject(DNSServerProfile.DNS_SERVER_PROFILE_CONTEXT).getString(DNSServerProfile.DNS_SERVER_IP) ,result.cause().getMessage()));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }

                        future.complete();
                    });
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    future.complete();
                }
            }

            for (var ip : ips)
            {
                var future = Promise.<Void>promise();

                futures.add(future.future());

                try
                {
                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("Resolving ip %s on dns server %s", ip, profile.getJsonObject(DNSServerProfile.DNS_SERVER_PROFILE_CONTEXT).getString(DNSServerProfile.DNS_SERVER_IP)));
                    }

                    run(dnsClient, ip, true).onComplete(result ->
                    {
                        try
                        {
                            if (result.succeeded())
                            {
                                if (CommonUtil.traceEnabled())
                                {
                                    LOGGER.trace(String.format("IP %s resolved with %s on dns server %s", ip, result.result(), profile.getJsonObject(DNSServerProfile.DNS_SERVER_PROFILE_CONTEXT).getString(DNSServerProfile.DNS_SERVER_IP)));
                                }

                                // Update DNS cache and track resolved IP
                                DNSCacheStore.getStore().add(ip, result.result());

                                removableIps.add(ip);

                                resolvedSources.add(new JsonObject().put(EventBusConstants.EVENT_SOURCE, ip).put("domain", result.result()));
                            }
                            else
                            {
                                LOGGER.warn(String.format("DNS Reverse Lookup failed for ip %s with reason : %s on dns server %s", ip, result.cause().getMessage(), profile.getJsonObject(DNSServerProfile.DNS_SERVER_PROFILE_CONTEXT).getString(DNSServerProfile.DNS_SERVER_IP)));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }

                        future.complete();

                    });
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    future.complete();
                }
            }

            // Once all async tasks are completed
            Future.all(futures).onComplete(result ->
            {
                LOGGER.info(String.format("Total time taken to resolve %s", DateTimeUtil.currentMilliSeconds() - startTime));

                // Update DNS server profile with resolution stats
                Bootstrap.configDBService().update(DBConstants.TBL_DNS_SERVER_PROFILE,
                        new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, profile.getLong(ID)),
                        new JsonObject().put(DNSServerProfile.DNS_SERVER_RESOLVED_OBJECTS, new JsonArray(resolvedObjects.stream().toList()))
                                .put(DNSServerProfile.DNS_SERVER_RESOLVED_SOURCES, resolvedSources)
                                .put(DNSServerProfile.DNS_SERVER_RESOLVER_LAST_SYNC_TIME, DateTimeUtil.currentSeconds()),
                        DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                        asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                DNSServerProfileConfigStore.getStore().updateItem(profile.getLong(ID));
                            }
                            else
                            {
                                LOGGER.warn(String.format("Failed to update DNS Server Profile %s with reason %s", profile.getString(DNSServerProfile.DNS_SERVER_PROFILE_NAME), asyncResult.cause()));
                            }
                        });

                ips.removeAll(removableIps); // Remove resolved IPs from the list

                dnsClient.close().onComplete(handler -> resolve(ips, objects, profiles, profileNumber + 1));

            });
        });
    }

    /**
     * Performs a DNS lookup or reverse lookup for the given host using the provided {@link DnsClient}.
     * <p>
     * In test environments, returns a fixed IP for a specific host ("cisco_core.motadata.local")
     * and fails for all others. In non-test environments, performs an actual DNS or reverse lookup
     * based on the {@code reverseLookup} flag.
     *
     * @param dnsClient      the DNS client used for lookup
     * @param host           the host name or IP address to resolve
     * @param reverseLookup  if {@code true}, performs a reverse lookup; otherwise, a forward lookup
     * @return a {@link Future} that will be completed with the resolved host/IP or failed with an error message
     */
    private Future<String> run(DnsClient dnsClient, String host, boolean reverseLookup)
    {
        var promise = Promise.<String>promise();

        try
        {
            if (MotadataConfigUtil.getEnvironmentType().equalsIgnoreCase(ENV_TEST))
            {
                if (host != null)
                {
                    if (host.equalsIgnoreCase("***************"))
                    {
                        promise.complete("testcase-domain-fdqn.com");
                    }
                    else if (host.equalsIgnoreCase("cisco_core.motadata.local"))
                    {
                        promise.complete("************");
                    }
                    else
                    {
                        promise.fail("Not found");
                    }
                }
                else
                {
                    promise.fail("Not found");
                }
            }
            else
            {
                (reverseLookup ? dnsClient.reverseLookup(host) : dnsClient.lookup(host)).onComplete(result ->
                {
                    try
                    {
                        if (result.succeeded() && CommonUtil.isNotNullOrEmpty(result.result()))
                        {
                            promise.complete(result.result());
                        }
                        else
                        {
                            promise.fail(result.failed() ? result.cause().getMessage() : "Result is empty");
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        promise.fail(exception.getMessage());
                    }
                });
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    /**
     * Removes an object's reference from all DNS server profiles where it is marked as resolved.
     *
     * <p>This method checks if the given event contains an {@code ID}, then iterates over all DNS profiles
     * to remove that object ID from the {@code DNS_SERVER_RESOLVED_OBJECTS} list if present. After modifying
     * the list, it updates the profile in the database and in the in-memory store.</p>
     *
     * @param event A {@link JsonObject} representing the object to be deleted, containing at least an {@code ID}.
     */
    private void delete(JsonObject event)
    {
        try
        {
            if (event.containsKey(ID))
            {
                var profiles = DNSServerProfileConfigStore.getStore().getItems();

                for (var index = 0; index < profiles.size(); index++)
                {
                    var profile = profiles.getJsonObject(index);

                    if (profile.containsKey(DNSServerProfile.DNS_SERVER_RESOLVED_OBJECTS) &&
                            profile.getJsonArray(DNSServerProfile.DNS_SERVER_RESOLVED_OBJECTS) != null &&
                            profile.getJsonArray(DNSServerProfile.DNS_SERVER_RESOLVED_OBJECTS).contains(event.getLong(ID)))
                    {
                        // Delete record from it and then update the profile.
                        var objects = profile.getJsonArray(DNSServerProfile.DNS_SERVER_RESOLVED_OBJECTS);

                        objects.remove(event.getLong(ID));

                        Bootstrap.configDBService().update(DBConstants.TBL_DNS_SERVER_PROFILE,
                                new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, profile.getLong(ID)),
                                new JsonObject().put(DNSServerProfile.DNS_SERVER_RESOLVED_OBJECTS, objects),
                                DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                asyncResult ->
                                {
                                    if (asyncResult.succeeded())
                                    {
                                        DNSServerProfileConfigStore.getStore().updateItem(profile.getLong(ID));

                                        LOGGER.info(String.format("Object %s removed from dns server profile %s successfully...", event.getString(AIOpsObject.OBJECT_HOST), profile.getString(DNSServerProfile.DNS_SERVER_PROFILE_NAME)));
                                    }
                                    else
                                    {
                                        LOGGER.warn(String.format("Failed to remove object %s from dns server profile %s, reason : %s", event.getString(AIOpsObject.OBJECT_HOST), profile.getLong(ID), asyncResult.cause().getMessage()));

                                        LOGGER.error(asyncResult.cause());
                                    }
                                });
                    }
                    else
                    {
                        LOGGER.debug(String.format("Object %s is not deleted by dns server profile %s...", event.getString(AIOpsObject.OBJECT_IP), profile.getString(DNSServerProfile.DNS_SERVER_PROFILE_NAME)));
                    }
                }
            }
            else
            {
                LOGGER.warn("Object IP is not available in event");
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Updates the IP address of a given object and reflects the change in both the DNS cache and the database.
     *
     * <p>This method performs the following:</p>
     * <ul>
     *   <li>Logs the change if trace logging is enabled.</li>
     *   <li>Updates the in-memory DNS cache with the new IP for the given host.</li>
     *   <li>Updates the object's IP in the database table {@code TBL_OBJECT}.</li>
     *   <li>Updates the in-memory object config store with the new IP.</li>
     * </ul>
     *
     * @param object The {@link JsonObject} representing the object whose IP has changed.
     * @param ip     The newly resolved IP address to update.
     */
    private void updateObject(JsonObject object, String ip)
    {
        try
        {
            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace(String.format("IP resolved for %s host and object ip is updated with old ip %s and new ip %s", object.getString(AIOpsObject.OBJECT_HOST), object.getString(AIOpsObject.OBJECT_IP), ip));
            }

            DNSCacheStore.getStore().add(ip, object.getString(AIOpsObject.OBJECT_HOST));

            Bootstrap.configDBService().update(DBConstants.TBL_OBJECT,
                    new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, object.getLong(ID)),
                    new JsonObject().put(AIOpsObject.OBJECT_IP, ip),
                    DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                    handler ->
                    {
                        if (handler.succeeded())
                        {
                            ObjectConfigStore.getStore().updateItem(object.getLong(ID));

                            ObjectConfigStore.getStore().updateIP(object.getString(AIOpsObject.OBJECT_IP), ip);
                        }
                        else
                        {
                            LOGGER.warn(String.format("Failed to update object ip for %s with reason %s", object.getString(AIOpsObject.OBJECT_NAME), handler.cause().getMessage()));
                        }
                    });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Creates and configures a Vert.x DNS client using the specified DNS server profile.
     *
     * <p>This method instantiates a new {@link DnsClient} with configuration parameters
     * extracted from the provided DNS server profile. The client is configured with
     * the server's IP address, port, and query timeout settings.</p>
     *
     * <p><strong>Configuration Parameters:</strong></p>
     * <ul>
     *   <li><strong>Host:</strong> DNS server IP address from the profile</li>
     *   <li><strong>Port:</strong> DNS server port (typically 53 for standard DNS)</li>
     *   <li><strong>Query Timeout:</strong> Maximum time to wait for DNS responses</li>
     * </ul>
     *
     * <p><strong>Usage:</strong></p>
     * <p>The created DNS client should be properly closed after use to free up resources.
     * This is typically handled in the calling methods through the client's {@code close()} method.</p>
     *
     * @param profile A {@link JsonObject} containing DNS server configuration including
     *                IP address, port, and timeout settings
     * @return A configured {@link DnsClient} ready for DNS operations
     * @throws IllegalArgumentException if the profile is missing required configuration parameters
     */
    private DnsClient getClient(JsonObject profile)
    {
        return vertx.createDnsClient(new DnsClientOptions()
                .setHost(profile.getString(DNSServerProfile.DNS_SERVER_IP))
                .setPort(profile.getInteger(DNSServerProfile.DNS_SERVER_PORT))
                .setQueryTimeout(profile.getLong(DNSServerProfile.DNS_SERVER_RESOLVER_TIMEOUT)));
    }

    /**
     * Adds system DNS servers to the DNS server profile store if they do not already exist.
     */
    private void addSystemDNS()
    {
        try
        {
            var profiles = DNSServerProfileConfigStore.getStore().getItems();

            var ips = profiles.stream().map(item -> JsonObject.mapFrom(item).getJsonObject(DNSServerProfile.DNS_SERVER_PROFILE_CONTEXT).getString(DNSServerProfile.DNS_SERVER_IP)).collect(Collectors.toSet());

            for (var server : ResolverConfig.getCurrentConfig().servers())
            {
                var ip = server.getAddress().getHostAddress();

                if (!ips.contains(ip))
                {
                    LOGGER.info(String.format("Adding Default DNS Server %s", ip));

                    var profile = new JsonObject().put(DNSServerProfile.DNS_SERVER_PROFILE_NAME, ip)
                            .put(DNSServerProfile.DNS_SERVER_PROFILE_DESCRIPTION, "-")
                            .put(DNSServerProfile.DNS_SERVER_PROFILE_CONTEXT, new JsonObject()
                                    .put(DNSServerProfile.DNS_SERVER_IP, ip)
                                    .put(DNSServerProfile.DNS_SERVER_RESOLVER_TIMEOUT, MotadataConfigUtil.getDNSDefaultTimeoutInSeconds() * 1000)
                                    .put(DNSServerProfile.DNS_SERVER_PORT, 53))
                            .put(DNSServerProfile.DNS_SERVER_RESOLVER_TYPE, DNSServerProfile.ResolverType.AUTOMATIC.getName());

                    Bootstrap.configDBService().save(DBConstants.TBL_DNS_SERVER_PROFILE, profile, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                    {
                        if (result.succeeded())
                        {
                            DNSServerProfileConfigStore.getStore().addItem(result.result());

                            LOGGER.info(String.format("Added Default DNS Server %s", ip));
                        }
                        else
                        {
                            LOGGER.warn(String.format("Failed to add Default DNS Server %s with reason : %s", ip, result.cause().getMessage()));
                        }
                    });
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
