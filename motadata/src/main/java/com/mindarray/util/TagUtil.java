/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *   Change Logs:
 *   Date			 Author			           Notes
 *   28-Feb-2025     Smit Prajapati            MOTADATA-4956: Rule Based Tagging
 *   03-Apr-2025     Smit Prajapati            Edge case scenario Add: if object does not have counter it condition will remain false
 *   06-Aug-2025     Aagam Salot               MOTADATA-6672 : Add range in tag base rule
 *   12-May-2025	 Chopra Deven	           MOTADATA-6050: updating mapping of object ids by tags while change in tags
*/

package com.mindarray.util;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.api.*;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.store.*;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.http.HttpStatus;

import java.net.InetAddress;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.db.DBConstants.ENTITY_TYPE_SYSTEM;
import static com.mindarray.nms.NMSConstants.*;

public class TagUtil extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(TagUtil.class, MOTADATA_UTIL, "Tag Util");
    private static final int INTERVAL_SECONDS = MotadataConfigUtil.getTagRuleTriggerTimerSeconds();
    private final JsonArray objects = new JsonArray();

    @Override
    public void start(Promise<Void> promise)
    {
        /*
         * This event is triggered by the user and is responsible for processing a specified Rule.
         *
         * - When triggered, the event evaluates the Rule's conditions against available objects.
         * - It identifies the objects that meet the Rule's criteria.
         * - Once qualified objects are determined, the event assigns the appropriate tags to them.
         *
         * This ensures that objects are categorized correctly based on the Rule's logic.
         */
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_TAG_RULE_RUN, message ->
        {

            try
            {
                var event = message.body();

                if (event.containsKey(TagRule.TAG_RULE_CONDITIONS))
                {
                    var conditions = event.getJsonArray(TagRule.TAG_RULE_CONDITIONS);

                    if (APIConstants.Entity.OBJECT.getName().toLowerCase().equalsIgnoreCase(event.getString(TagRule.TAG_RULE_TYPE)))
                    {
                        //will return qualified monitors for the rule
                        var items = qualify(conditions, YES.equalsIgnoreCase(event.getString(TagRule.TAG_RULE_INCLUSIVE_CONDITION)), null);

                        if (event.getBoolean(TagRule.TAG_RULE_TEST))
                        {
                            if (!items.isEmpty())
                            {
                                message.reply(new JsonObject().put(RESULT, items).put(STATUS, STATUS_SUCCEED));
                            }
                            else
                            {
                                message.fail(HttpStatus.SC_NOT_FOUND, ErrorMessageConstants.INVALID_NO_DATA_FOUND);
                            }
                        }
                        else
                        {
                            if (!items.isEmpty())
                            {
                                //will assign tags to the qualified monitors
                                updateTags(items, event.getJsonArray(TagRule.TAG_RULE_TAGS), event.getJsonObject(TagRule.TAG_RULE_DYNAMIC_TAGS), TagRule.TAG_RULE_OPERATION_ASSIGN.equalsIgnoreCase(event.getString(TagRule.TAG_RULE_OPERATION))).onComplete(result ->
                                {

                                    if (result.succeeded())
                                    {
                                        message.reply(new JsonObject().put(STATUS, STATUS_SUCCEED));
                                    }
                                    else
                                    {
                                        message.fail(HttpStatus.SC_INTERNAL_SERVER_ERROR, result.cause().getMessage());
                                    }
                                });
                            }
                            else
                            {
                                message.reply(new JsonObject().put(STATUS, STATUS_SUCCEED));

                                LOGGER.info("No Monitor qualified for Rule: " + event.getString(TagRule.TAG_RULE_NAME));
                            }
                        }
                    }
                    else
                    {
                        //will return qualified instances for the rule
                        var items = qualify(conditions, event.getString(TagRule.TAG_RULE_TYPE), YES.equalsIgnoreCase(event.getString(TagRule.TAG_RULE_INCLUSIVE_CONDITION)), event.getBoolean(TagRule.TAG_RULE_TEST));

                        if (event.getBoolean(TagRule.TAG_RULE_TEST))
                        {
                            var objects = new JsonArray();

                            var count = 0;

                            //In Preview, we will only send 1000 instances to UI
                            for (var item : items.entrySet())
                            {
                                var entries = item.getValue();

                                if (count >= 1000)
                                {
                                    break;
                                }

                                objects.addAll(entries);

                                count += entries.size();
                            }

                            if (!objects.isEmpty())
                            {
                                message.reply(new JsonObject().put(RESULT, objects).put(STATUS, STATUS_SUCCEED));
                            }
                            else
                            {
                                message.fail(HttpStatus.SC_NOT_FOUND, ErrorMessageConstants.INVALID_NO_DATA_FOUND);
                            }
                        }
                        else
                        {
                            if (!items.isEmpty())
                            {
                                //will assign tags to the qualified instances
                                updateTags(items, event.getJsonArray(TagRule.TAG_RULE_TAGS), event.getJsonObject(TagRule.TAG_RULE_DYNAMIC_TAGS), TagRule.TAG_RULE_OPERATION_ASSIGN.equalsIgnoreCase(event.getString(TagRule.TAG_RULE_OPERATION))).onComplete(result ->
                                {

                                    if (result.succeeded())
                                    {
                                        message.reply(new JsonObject().put(RESULT, items).put(STATUS, STATUS_SUCCEED));
                                    }
                                    else
                                    {
                                        message.fail(HttpStatus.SC_INTERNAL_SERVER_ERROR, result.cause().getMessage());
                                    }
                                });
                            }
                            else
                            {
                                message.reply(new JsonObject().put(STATUS, STATUS_SUCCEED));

                                LOGGER.info("No Instance qualified for Rule: " + event.getString(TagRule.TAG_RULE_NAME));
                            }
                        }
                    }
                }

                //At every Run we will update the Last Run Time of the Rule
                if (!event.getBoolean(TagRule.TAG_RULE_TEST))
                {
                    Bootstrap.configDBService().update(DBConstants.TBL_TAG_RULE, new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, event.getLong(ID)),
                            TagRuleConfigStore.getStore().getItem(event.getLong(ID)).put(TagRule.TAG_RULE_TRIGGERED_TIME, System.currentTimeMillis()), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, future ->
                            {
                                if (future.succeeded())
                                {
                                    TagRuleConfigStore.getStore().updateItem(event.getLong(ID));
                                }
                            });
                }

            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
        {
            var event = message.body();

            //At every new Object Provision, we will store the Object ID and will process it later
            if (EventBusConstants.ChangeNotificationType.valueOf(event.getString(EventBusConstants.CHANGE_NOTIFICATION_TYPE)) == EventBusConstants.ChangeNotificationType.ADD_OBJECT)
            {
                objects.add(event.getLong(ID));
            }

        }).exceptionHandler(LOGGER::error);

        /*
         * This process runs at a fixed time interval to handle newly provisioned objects stored in records.
         *
         * - The process executes at a constant interval, with the default set to 120 seconds.
         * - It identifies and processes newly added objects in the records.
         * - After assigning the necessary attributes or tags, the processed objects are removed from the records.
         *
         * This ensures that new objects are handled efficiently and do not persist in the records unnecessarily.
         */
        vertx.setPeriodic(INTERVAL_SECONDS * 1000L, timer ->
        {
            try
            {
                if (!objects.isEmpty())//now once timer enabled after that will be running tag rules to assign it to new objects..
                {
                    assign(new JsonArray(objects.getList()));

                    objects.clear();
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        /*
         * This event is responsible for adding system tags that are fetched through polling.
         *
         * - It processes newly polled tags and assigns them to the appropriate monitors.
         * - Only new tags retrieved from the latest polling cycle are considered.
         * - Ensures that monitors are updated with the most recent system tags dynamically.
         *
         * This helps in maintaining up-to-date tagging for monitors based on the latest fetched data.
         */
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_TAG_SYNC, message ->
        {

            try
            {
                var event = message.body();

                if (CommonUtil.traceEnabled())
                {
                    LOGGER.trace("Event Received for : " + EventBusConstants.EVENT_TAG_SYNC);
                }

                //#24789 update system tag in object config store every poll. if system tag change then update object config store
                if (event.containsKey(SYSTEM_TAGS) && event.getValue(SYSTEM_TAGS) != null && !event.getJsonArray(SYSTEM_TAGS).isEmpty())
                {
                    //items (All tags including System tags and User Tags)
                    var items = ObjectConfigStore.getStore().getItem(event.getLong(Metric.METRIC_OBJECT)).getJsonArray(AIOpsObject.OBJECT_TAGS, new JsonArray());

                    var userTags = TagConfigStore.getStore().getItems(items, Tag.TagType.OBJECT, DBConstants.ENTITY_TYPE_USER);

                    var systemTags = new JsonArray();

                    for (var i = 0; i < event.getJsonArray(SYSTEM_TAGS).size(); i++)
                    {
                        systemTags.add(event.getJsonArray(SYSTEM_TAGS).getString(i).toLowerCase());
                    }

                    var tags = TagConfigStore.getStore().addItems(systemTags, Tag.TagType.OBJECT.getName(), ENTITY_TYPE_SYSTEM);

                    if (tags != null && userTags != null)
                    {
                        //we will add unique tag values in config store
                        Bootstrap.configDBService().update(DBConstants.TBL_OBJECT,
                                new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, event.getLong(Metric.METRIC_OBJECT)),
                                new JsonObject().put(AIOpsObject.OBJECT_TAGS, enrich(tags, TagConfigStore.getStore().getIdsByItems(userTags))),
                                DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                result -> ObjectConfigStore.getStore().updateItem(event.getLong(Metric.METRIC_OBJECT)));
                    }

                    event.put(AIOpsObject.OBJECT_TAGS, event.remove(SYSTEM_TAGS));
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        promise.complete();
    }

    /*
     * This method filters and qualifies instance objects based on the provided conditions.
     *
     * Parameters:
     * - filters: A list of conditions used to evaluate object qualification.
     * - type: The instance type being processed.
     * - inclusive: A boolean indicating whether all conditions must be met (true) or if any condition can qualify an object (false).
     *
     * Returns:
     * - A Map where:
     *   - The key is the MetricConfigStore ID.
     *   - The value is a list of objects that meet the specified filters.
     *
     * Example:
     * - Suppose we have an SNMP interface plugin with a metric-store ID of `1000000001` and 10 available interfaces.
     * - If only one interface qualifies based on the Rule, the method will return:
     *   `{1000000001: [{interface1}]}`.
     *
     * This ensures that only the relevant objects are selected based on the given filtering criteria.
     */
    private Map<Long, JsonArray> qualify(JsonArray filters, String type, boolean inclusive, boolean test)
    {
        var items = getInstances(type);

        var result = new HashMap<Long, JsonArray>();

        try
        {
            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                var objects = item.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(OBJECTS);

                if (objects != null && !objects.isEmpty())
                {
                    var qualifiedInstances = new JsonArray();

                    for (var objectIndex = 0; objectIndex < objects.size(); objectIndex++)
                    {
                        var object = objects.getJsonObject(objectIndex);

                        var condition = false;

                        for (var filterIndex = 0; filterIndex < filters.size(); filterIndex++)
                        {
                            var filterItem = filters.getJsonObject(filterIndex);

                            if (object.getValue(filterItem.getString(VisualizationConstants.DATA_POINT)) != null)
                            {
                                var value = PolicyEngineConstants.Operator.IN.getName().equalsIgnoreCase(filterItem.getString(OPERATOR)) ? new JsonArray(filterItem.getString(VALUE)) : filterItem.getValue(VALUE);

                                if (filterItem.getString(FILTER).equalsIgnoreCase(DatastoreConstants.ConditionGroup.AND.getName()))
                                {
                                    if(PolicyEngineConstants.Operator.RANGE.getName().equalsIgnoreCase(filterItem.getString(OPERATOR)))
                                    {
                                        if(PolicyEngineConstants.Operator.RANGE.getName().equalsIgnoreCase(filterItem.getString(OPERATOR)))
                                        {
                                            var start = value.toString().split("-")[0].trim();

                                            var end = start.substring(0, start.lastIndexOf('.')) + "." + value.toString().split("-")[1].trim();

                                            if (APIUtil.validateRange(start, end))
                                            {
                                                var ip = object.getValue(filterItem.getString(VisualizationConstants.DATA_POINT));

                                                condition = condition && CommonUtil.calculateIPRange(InetAddress.getByName(start), InetAddress.getByName(end)).stream().anyMatch(address -> address.equals(ip));
                                            }
                                        }
                                    }
                                    else
                                    {
                                        condition = condition && PolicyEngineConstants.evaluateCondition(inclusive, filterItem.getString(OPERATOR), value, object.getValue(filterItem.getString(VisualizationConstants.DATA_POINT)));
                                    }
                                }
                                else if (filterItem.getString(FILTER).equalsIgnoreCase(DatastoreConstants.ConditionGroup.OR.getName()))
                                {
                                    if(PolicyEngineConstants.Operator.RANGE.getName().equalsIgnoreCase(filterItem.getString(OPERATOR)))
                                    {
                                        if(PolicyEngineConstants.Operator.RANGE.getName().equalsIgnoreCase(filterItem.getString(OPERATOR)))
                                        {
                                            var start = value.toString().split("-")[0].trim();

                                            var end = start.substring(0, start.lastIndexOf('.')) + "." + value.toString().split("-")[1].trim();

                                            if (APIUtil.validateRange(start, end))
                                            {
                                                var ip = object.getValue(filterItem.getString(VisualizationConstants.DATA_POINT));

                                                condition = condition || CommonUtil.calculateIPRange(InetAddress.getByName(start), InetAddress.getByName(end)).stream().anyMatch(address -> address.equals(ip));
                                            }
                                        }
                                    }
                                    else
                                    {
                                        condition = condition || PolicyEngineConstants.evaluateCondition(inclusive, filterItem.getString(OPERATOR), value, object.getValue(filterItem.getString(VisualizationConstants.DATA_POINT)));
                                    }

                                }
                                else
                                {
                                    if(PolicyEngineConstants.Operator.RANGE.getName().equalsIgnoreCase(filterItem.getString(OPERATOR)))
                                    {
                                        var start = value.toString().split("-")[0].trim();

                                        var end = start.substring(0, start.lastIndexOf('.')) + "." + value.toString().split("-")[1].trim();

                                        if (APIUtil.validateRange(start, end))
                                        {
                                            var ip = object.getValue(filterItem.getString(VisualizationConstants.DATA_POINT));

                                            condition = CommonUtil.calculateIPRange(InetAddress.getByName(start), InetAddress.getByName(end)).stream().anyMatch(address -> address.equals(ip));
                                        }
                                    }
                                    else
                                    {
                                        condition = PolicyEngineConstants.evaluateCondition(inclusive, filterItem.getString(OPERATOR), value, object.getValue(filterItem.getString(VisualizationConstants.DATA_POINT)));
                                    }


                                }
                            }
                            else
                            {
                                if (DatastoreConstants.ConditionGroup.AND.getName().equalsIgnoreCase(filterItem.getString(FILTER)))
                                {
                                    condition = false;
                                }
                            }
                        }

                        if (condition)
                        {
                            if (INTERFACE.equalsIgnoreCase(type) && test)
                            {
                                object.put(AIOpsObject.OBJECT_NAME, object.getString(INTERFACE_NAME));
                            }

                            qualifiedInstances.add(object);
                        }
                    }

                    if (!qualifiedInstances.isEmpty())
                    {
                        result.put(item.getLong(ID), qualifiedInstances);
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }

    /*
     *  This method will qualify the objects based on the filters provided
     *  params: filters(conditions in list) , inclusive (all conditions are inclusive or not)
     *  return: List of Object IDs
     * */
    private JsonArray qualify(JsonArray conditions, boolean inclusive, JsonArray objects)
    {

        var ids = new JsonArray();

        try
        {
            var items = objects == null ? ObjectConfigStore.getStore().getItems() : ObjectConfigStore.getStore().getItems(objects);

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                var status = false;

                for (var i = 0; i < conditions.size(); i++)
                {
                    var condition = conditions.getJsonObject(i);

                    if (item.getValue(condition.getString(VisualizationConstants.DATA_POINT)) != null)
                    {
                        var value = PolicyEngineConstants.Operator.IN.getName().equalsIgnoreCase(condition.getString(OPERATOR)) ? new JsonArray(condition.getString(VALUE)) : condition.getValue(VALUE);

                        if (condition.getString(FILTER).equalsIgnoreCase(DatastoreConstants.ConditionGroup.AND.getName()))
                        {
                            status = status && PolicyEngineConstants.evaluateCondition(inclusive, condition.getString(OPERATOR), value, item.getValue(condition.getString(VisualizationConstants.DATA_POINT)));
                        }
                        else if (condition.getString(FILTER).equalsIgnoreCase(DatastoreConstants.ConditionGroup.OR.getName()))
                        {
                            status = status || PolicyEngineConstants.evaluateCondition(inclusive, condition.getString(OPERATOR), value, item.getValue(condition.getString(VisualizationConstants.DATA_POINT)));
                        }

                        else
                        {
                            if(PolicyEngineConstants.Operator.RANGE.getName().equalsIgnoreCase(condition.getString(OPERATOR)))
                            {
                                var start = value.toString().split("-")[0].trim();

                                var end = start.substring(0, start.lastIndexOf('.')) + "." + value.toString().split("-")[1].trim();

                                if (APIUtil.validateRange(start, end))
                                {
                                    var ip = item.getValue(condition.getString(VisualizationConstants.DATA_POINT));

                                    status = CommonUtil.calculateIPRange(InetAddress.getByName(start), InetAddress.getByName(end)).stream().anyMatch(object -> object.equals(ip));
                                }
                            }
                            else
                            {
                                status = PolicyEngineConstants.evaluateCondition(inclusive, condition.getString(OPERATOR), value, item.getValue(condition.getString(VisualizationConstants.DATA_POINT)));
                            }
                        }
                    }
                    else
                    {
                        if (DatastoreConstants.ConditionGroup.AND.getName().equalsIgnoreCase(condition.getString(FILTER)))
                        {
                            status = false;
                        }
                    }
                }

                if (status)
                {
                    ids.add(item.getLong(ID));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        LOGGER.trace(String.format("qualify ids : %s",ids.encodePrettily()));

        return ids;
    }

    /*
     *  This method will apply Tags based on rules to the given qualified Objects
     *  params: ids (List of Qualified Object IDs),
     *          objectTags (Static Tags - example-> department:dev etc. ),
     *          dynamicTags (Dynamic Tags - example-> vm:{vm.ip} etc.),
     *          assign (true or false based on Rule)
     *
     * */
    private Future<JsonObject> updateTags(JsonArray ids, JsonArray objectTags, JsonObject dynamicTags, boolean assign)
    {
        var future = Promise.<JsonObject>promise();

        try
        {

            //For Bulk Tagging Purpose old data and new data will be merged into one and stored in collections we will use only Update...
            if (!objectTags.isEmpty() || !dynamicTags.isEmpty())
            {
                var futures = new ArrayList<Future<Void>>();

                for (var id : ids)
                {
                    var tags = new JsonArray().addAll(objectTags);

                    var promise = Promise.<Void>promise();

                    futures.add(promise.future());

                    var items = ObjectConfigStore.getStore().getItem(CommonUtil.getLong(id)).getJsonArray(AIOpsObject.OBJECT_TAGS);

                    if (items != null)
                    {
                        if (assign)
                        {
                            for (var item : items)
                            {
                                if (!tags.contains(CommonUtil.getInteger(item)))
                                {
                                    tags.add(CommonUtil.getInteger(item));
                                }
                            }
                        }
                        else
                        {
                            if (!items.isEmpty())
                            {
                                items.remove(objectTags.getInteger(0));
                            }

                            tags = items;
                        }
                    }

                    if (!dynamicTags.isEmpty())
                    {
                        var newTags = processDynamicTags(ObjectConfigStore.getStore().getItem(CommonUtil.getLong(id)), dynamicTags);

                        if (assign)
                        {
                            for (var index = 0; index < newTags.size(); index++)
                            {
                                if (!tags.contains(newTags.getInteger(index)))
                                {
                                    tags.add(newTags.getInteger(index));
                                }
                            }
                        }
                        else if (items != null && !newTags.isEmpty())
                        {
                            items.remove(newTags.getInteger(0));
                        }
                        else if (items == null)
                        {
                            tags.clear();
                        }
                    }

                    Bootstrap.configDBService().update(DBConstants.TBL_OBJECT, new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, id),
                            new JsonObject().put(AIOpsObject.OBJECT_TAGS, tags),
                            DEFAULT_USER,
                            SYSTEM_REMOTE_ADDRESS,
                            result ->
                            {
                                if (result.succeeded() && !result.result().isEmpty())
                                {
                                    ObjectConfigStore.getStore().updateItem(CommonUtil.getLong(id)).onComplete(asyncResult -> promise.complete());
                                }
                                else
                                {
                                    promise.fail(result.cause());
                                }
                            });
                }

                Future.join(futures).onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        future.complete(new JsonObject().put(GlobalConstants.STATUS, STATUS_SUCCEED));
                    }
                    else
                    {
                        future.fail(result.cause());
                    }
                });
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            future.fail(exception);
        }

        return future.future();
    }

    /*
     *  This method will apply Tags based on rules to the given qualified Instances
     *  params: instances (Metric ID and List of Qualified instances),
     *          objectTags (Static Tags - example-> department:dev etc. ),
     *          dynamicTags (Dynamic Tags - example-> vm:{vm.ip} etc.),
     *          assign (true or false based on Rule)
     *
     * */
    private Future<JsonObject> updateTags(Map<Long, JsonArray> instances, JsonArray instanceTags, JsonObject dynamicTags, boolean assign)
    {
        var future = Promise.<JsonObject>promise();

        try
        {
            var futures = new ArrayList<Future<Void>>();

            var ids = new JsonArray();

            for (var entities : instances.entrySet())
            {
                var item = MetricConfigStore.getStore().getItem(entities.getKey());

                var promise = Promise.<Void>promise();

                futures.add(promise.future());

                ids.add(item.getLong(ID));

                var objects = new JsonArray();

                if (item.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(OBJECTS) != null && !item.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(OBJECTS).isEmpty())
                {
                    objects.addAll(item.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(OBJECTS));
                }

                setTags(instanceTags, dynamicTags, entities.getValue(), objects, assign);

                TagCacheStore.getStore().updateInstanceTags(item.getLong(ID), objects, NMSConstants.INSTANCE_TYPES.getOrDefault(item.getString(Metric.METRIC_PLUGIN), EMPTY_VALUE));

                ObjectConfigStore.getStore().updateInstanceTags(item.getLong(ID), objects);

                Bootstrap.configDBService().update(DBConstants.TBL_METRIC,
                        new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                        item,
                        DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                        result ->
                        {
                            if (result.succeeded())
                            {
                                promise.complete();
                            }
                            else
                            {
                                promise.fail(result.cause().getMessage());
                            }
                        });
            }

            Future.join(futures).onComplete(result ->
            {
                if (!ids.isEmpty())
                {
                    MetricConfigStore.getStore().updateItems(ids);

                    future.complete(new JsonObject().put(GlobalConstants.STATUS, STATUS_SUCCEED));

                    LOGGER.info("Tags are Updated in Instances..");
                }
                else
                {
                    future.fail("No instance qualified");
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            future.fail(exception);
        }

        return future.future();
    }

    /*
     *  Extend Method of processInstanceTags
     * */
    private void setTags(JsonArray instanceTags, JsonObject dynamicTags, JsonArray qualifiedObjects, JsonArray objects, boolean assign)
    {
        try
        {
            var qualified = new HashSet<String>();

            for (var index = 0; index < qualifiedObjects.size(); index++)
            {
                qualified.add(qualifiedObjects.getJsonObject(index).getString(AIOpsObject.OBJECT_NAME));
            }

            for (var index = 0; index < objects.size(); index++)
            {
                var object = objects.getJsonObject(index);

                // will apply tag only to the objects which are qualified
                if (object.getString(AIOpsObject.OBJECT_NAME) != null && qualified.contains(object.getString(AIOpsObject.OBJECT_NAME)))
                {
                    for (var tagIndex = 0; tagIndex < instanceTags.size(); tagIndex++)
                    {
                        var tag = instanceTags.getInteger(tagIndex);

                        var objectInstanceTags = (JsonArray) object.getMap().computeIfAbsent(INSTANCE_TAGS, val -> new JsonArray());

                        if (!objectInstanceTags.contains(tag) && assign)
                        {
                            objectInstanceTags.add(tag);
                        }
                        else if (objectInstanceTags.contains(tag) && !assign)
                        {
                            objectInstanceTags.remove(tag);
                        }
                    }

                    if (!dynamicTags.isEmpty())
                    {
                        if (assign)
                        {
                            var newTags = processDynamicTags(object, dynamicTags);

                            var tags = object.getJsonArray(INSTANCE_TAGS, new JsonArray());

                            for (var j = 0; j < newTags.size(); j++)
                            {
                                if (!tags.contains(newTags.getInteger(j)))
                                {
                                    tags.add(newTags.getInteger(j));
                                }
                            }

                            object.put(INSTANCE_TAGS, tags);
                        }
                        else
                        {
                            object.getJsonArray(INSTANCE_TAGS, new JsonArray()).remove(processDynamicTags(object, dynamicTags).getInteger(0));
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /*
     *  This method will process the Dynamic Tags and will return the list of tags
     *  params: object (Object), tags (Dynamic Tags)
     *  example: object: {object.name: vm1, object.id: 1000000001, object.ip:**********}
     *           tags: {tag1: object.name, tag2: object.ip}
     *
     *           return: [tag1:vm1, tag2:**********] but in hashcode format [1000000001, 1000000002]
     * */
    private JsonArray processDynamicTags(JsonObject object, JsonObject tags)
    {
        var dynamicTags = new JsonArray();

        var newTags = new JsonArray();

        try
        {
            for (var key : tags.fieldNames())
            {
                var value = tags.getString(key);

                if (value != null && !value.isEmpty() && object.containsKey(value))
                {
                    dynamicTags.add(key + COLON_SEPARATOR + object.getString(value));
                }
            }

            if (!dynamicTags.isEmpty())
            {
                newTags = TagConfigStore.getStore().addItems(dynamicTags, Tag.TagType.OBJECT.getName(), DBConstants.ENTITY_TYPE_USER);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return newTags;
    }

    /*
     * This method will return the Instances based on the Instance Type
     * params: instanceType(example: interface, vm, etc.)
     * */
    private JsonArray getInstances(String instanceType)
    {
        var items = new JsonArray();

        try
        {
            var plugins = PLUGIN_BY_INSTANCES.getOrDefault(instanceType, new JsonArray());

            if (!plugins.isEmpty())
            {
                for (var plugin : plugins)
                {
                    items.addAll(MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_PLUGIN, CommonUtil.getString(plugin)));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return items;
    }

    /*
     *  This method will process New Provisioned Objects and tags will be assigned to them if they qualify the conditions
     *
     *  params: objects (List of Object IDs)
     *  Only "Objects" Based Rules AND Rules with "Assign" operation are going to be processed
     * */
    private void assign(JsonArray objects)
    {
        try
        {
            var staticTags = new HashMap<Long, JsonArray>();

            var dynamicTags = new HashMap<Long, JsonObject>();

            if (!objects.isEmpty())
            {
                var items = TagRuleConfigStore.getStore().getItems();

                for (var index = 0; index < items.size(); index++)
                {
                    var rule = items.getJsonObject(index);

                    if (rule.containsKey(TagRule.TAG_RULE_CONDITIONS) &&
                            APIConstants.Entity.OBJECT.getName().toLowerCase().equalsIgnoreCase(rule.getString(TagRule.TAG_RULE_TYPE)) &&
                            TagRule.TAG_RULE_OPERATION_ASSIGN.equalsIgnoreCase(rule.getString(TagRule.TAG_RULE_OPERATION)))
                    {

                        for (var item : qualify(rule.getJsonArray(TagRule.TAG_RULE_CONDITIONS), YES.equalsIgnoreCase(rule.getString(TagRule.TAG_RULE_INCLUSIVE_CONDITION)), objects))
                        {
                            staticTags.computeIfAbsent(CommonUtil.getLong(item), value -> new JsonArray()).addAll(rule.getJsonArray(TagRule.TAG_RULE_TAGS));

                            dynamicTags.computeIfAbsent(CommonUtil.getLong(item), value -> new JsonObject()).mergeIn(rule.getJsonObject(TagRule.TAG_RULE_DYNAMIC_TAGS));
                        }
                    }
                }

                for (var index = 0; index < objects.size(); index++)
                {
                    updateTags(new JsonArray().add(objects.getLong(index)), staticTags.getOrDefault(objects.getLong(index), new JsonArray()), dynamicTags.getOrDefault(objects.getLong(index), new JsonObject()), true).onComplete(result ->
                    {

                        if (result.succeeded())
                        {
                            LOGGER.info("Monitor Tags are assigned..");
                        }
                    });
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private JsonArray enrich(JsonArray tags, JsonArray newTags)
    {
        try
        {
            for (var index = 0; index < newTags.size(); index++)
            {
                if (!tags.contains(newTags.getLong(index)))
                {
                    tags.add(newTags.getLong(index));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return tags;
    }
}
