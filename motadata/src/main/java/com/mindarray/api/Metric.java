/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 * Change Logs:
 *  Date			Author			Notes
 *  5-Feb-2025		<PERSON><PERSON>		MOTADATA-5007: Overrided method getReferencesPreHook to get references of application in runbook plugin and metric plugin
 *  26-Feb-2025		Darshan Parmar	MOTADATA-5215: SonarQube Suggestions Resolution
 *  24-Mar-2025     Chandresh       MOTADATA-5426: Docker discovery and polling support added
 *  12-May-2025	    Chopra Deven	MOTADATA-6050: updating mapping of object ids by tags while change in metric
 *  28-Jul-2025     Umang           MOTADATA-6754 Support for Tags and Severity
 *  05-Aug-2025     Viram           MOTADATA-6858 Refactored license to have common method to update license
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.ArrayList;

import static com.mindarray.ErrorMessageConstants.INTERNAL_SERVER_EXCEPTION;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.nms.NMSConstants.*;

public class Metric extends AbstractAPI
{

    public static final String METRIC_POLLING_TIME = "metric.polling.time";
    public static final String METRIC_OBJECT = "metric.object";
    public static final String METRIC_NAME = "metric.name";
    public static final String METRIC_CREDENTIAL_PROFILE = "metric.credential.profile";
    public static final String METRIC_CREDENTIAL_PROFILE_PROTOCOL = "metric.credential.profile.protocol";
    public static final String METRIC_TYPE = "metric.type";
    public static final String METRIC_CATEGORY = "metric.category";
    public static final String METRIC_DISCOVERY_METHOD = "metric.discovery.method";
    public static final String METRIC_PLUGIN = "metric.plugin";
    public static final String METRIC_CONTEXT = "metric.context";
    public static final String METRIC_STATE = "metric.state";
    private static final Logger LOGGER = new Logger(Metric.class, MOTADATA_API, "Metric API");

    public Metric()
    {
        super("metrics", MetricConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            router.get("/" + endpoint + "/:id").handler(this::get);

            router.get("/" + endpoint).handler(this::getAll);

            router.put("/" + endpoint + "/:id").handler(this::update);

            router.post("/" + endpoint).handler(this::updateAll);

            router.delete("/" + endpoint + "/:id").handler(this::delete);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    // task - 4277 metric (application) unprovision
    protected void delete(RoutingContext routingContext)
    {
        try
        {
            this.configStore.getReferenceEntities(CommonUtil.getLong(routingContext.request().getParam(ID))).onComplete(result ->
            {
                if (result.succeeded())
                {
                    getReferencesPreHook(routingContext, result.result()).onComplete(asyncResult ->
                    {
                        if (asyncResult.succeeded() && !asyncResult.result().isEmpty())
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                    .put(RESULT, asyncResult.result()) // ui will display this references in tooltip
                                    .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED_IN_USED, Entity.METRIC.getName()))
                                    .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL));
                        }
                        else
                        {
                            var id = CommonUtil.getLong(routingContext.request().getParam(ID));

                            var type = routingContext.request().getParam(Metric.METRIC_TYPE);

                            var items = new ArrayList<JsonObject>();

                            for (var item : MetricConfigStore.getStore().getItemsByObject(id))
                            {
                                if (item.getString(Metric.METRIC_TYPE).equalsIgnoreCase(type))
                                {
                                    items.add(new JsonObject().put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host())
                                            .put(ID, item.getLong(ID)).put(USER_NAME, routingContext.user().principal().getString(USER_NAME)));
                                }
                            }

                            NMSConstants.updateMetrics(items, EventBusConstants.EVENT_METRIC_UNPROVISION);

                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(ID, CommonUtil.getLong(routingContext.request().getParam(ID))));

                            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_CHANGE_LOCAL_NOTIFICATION,
                                    new JsonObject().put(ID, id).put(Metric.METRIC_TYPE, type).put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_APPLICATION.name()));
                        }
                    });
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                            .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, result.cause().getMessage()))
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                            .put(ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));
                }
            });
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected void get(RoutingContext routingContext)
    {
        try
        {
            var object = ObjectConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID)));

            // for agent based process/service/file/directory listing we have only 1 tab for update time and delete objects and for that we need to pass key with context to UI for tab display
            var agent = object != null && object.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVER.getName()) && object.containsKey(AIOpsObject.OBJECT_AGENT);

            var items = new JsonObject(); // listing metrics with key : type and value : qualified metrics of that category

            for (var item : MetricConfigStore.getStore().getItemsByObject(CommonUtil.getLong(routingContext.request().getParam(ID))))
            {
                // for agent metric we don't need to display availability metric as we have hardcoded 10 sec polling for agent availability when user select agent availability with ping and if user changes polling time then it will create issue in agent heartbeat status
                if (!agent || !item.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.AVAILABILITY.getName()))
                {
                    if (item.containsKey(Metric.METRIC_CONTEXT) &&
                            item.getJsonObject(METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS) != null
                            && !item.getJsonObject(METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS).isEmpty()) // interface,process,service listing
                    {
                        items.put(item.getString(Metric.METRIC_NAME), new JsonArray(new ArrayList<JsonObject>(item.size())).add(item));
                    }

                    var type = item.getString(Metric.METRIC_TYPE);

                    //Custom category
                    if (NMSConstants.CUSTOM_METRIC_TYPES.contains(type))
                    {
                        type = NMSConstants.Category.CUSTOM.getName();
                    }

                    if (items.containsKey(type))
                    {
                        items.getJsonArray(type).add(item);
                    }
                    else
                    {
                        items.put(type, new JsonArray().add(item));
                    }
                }
            }

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                    .put(GlobalConstants.RESULT, items));
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected void update(RoutingContext routingContext)
    {
        try
        {
            var requestParameters = routingContext.body().asJsonObject().getJsonArray(APIConstants.REQUEST_PARAMS); // params

            var futures = new ArrayList<Future<Void>>();

            var ids = new JsonArray();

            for (var index = 0; index < requestParameters.size(); index++)
            {
                var item = requestParameters.getJsonObject(index);

                var metricPlugin = item.getString(Metric.METRIC_PLUGIN);

                if (validateInstances(metricPlugin, item))
                {
                    var promise = Promise.<Void>promise();

                    futures.add(promise.future());

                    ids.add(item.getLong(ID));

                    var objects = new JsonArray();

                    if (metricPlugin != null && isWANLinkMetric(metricPlugin))
                    {
                        updateIPSLAMetrics(metricPlugin, item.getLong(Metric.METRIC_OBJECT), objects);
                    }

                    if (item.getJsonObject(METRIC_CONTEXT).getJsonArray(OBJECTS) != null && !item.getJsonObject(METRIC_CONTEXT).getJsonArray(OBJECTS).isEmpty())
                    {
                        objects.addAll(item.getJsonObject(METRIC_CONTEXT).getJsonArray(OBJECTS));
                    }

                    TagCacheStore.getStore().updateInstanceTags(item.getLong(ID), objects, NMSConstants.INSTANCE_TYPES.getOrDefault(item.getString(Metric.METRIC_PLUGIN), EMPTY_VALUE));

                    ObjectConfigStore.getStore().updateInstanceTags(item.getLong(ID), objects);

                    Bootstrap.configDBService().update(DBConstants.TBL_METRIC,
                            new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                            item,
                            routingContext.user().principal().getString(User.USER_NAME), routingContext.request().remoteAddress().host(),
                            result ->
                            {
                                if (result.succeeded())
                                {
                                    promise.complete();
                                }
                                else
                                {
                                    promise.fail(result.cause().getMessage());
                                }
                            });
                }
            }

            Future.join(futures).onComplete(result ->
            {
                if (!ids.isEmpty())
                {
                    this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK).put(APIConstants.REQUEST_PARAM_IDS, ids)
                            .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.OBJECT_METRIC_UPDATE_SUCCEEDED, ObjectConfigStore.getStore().getObjectName(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID))))));

                    MetricConfigStore.getStore().updateItems(ids);

                    //24349
                    if ((routingContext.body().asJsonObject().containsKey(REQUEST_PARAM_TYPE) && routingContext.body().asJsonObject().getString(REQUEST_PARAM_TYPE).equalsIgnoreCase(REQUEST_DELETE))
                            && requestParameters.getJsonObject(0) != null) //at a time user can only delete instances from one metric group
                    {
                        var metric = requestParameters.getJsonObject(0);

                        if (metric.getJsonArray(METRIC_INSTANCES) != null)
                        {
                            LicenseCacheStore.getStore().updateLicense(metric, false, metric.getJsonArray(METRIC_INSTANCES).size());
                        }

                        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_OBJECT).put(ID, requestParameters.getJsonObject(0).getLong(Metric.METRIC_OBJECT)).put(NMSConstants.METRIC_INSTANCES, requestParameters.getJsonObject(0).getJsonArray(NMSConstants.METRIC_INSTANCES)));
                    }
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, STATUS_FAIL)
                            .put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.METRIC_UPDATE_FAILED, "Application is provisioned into system with selected object")));
                }
            });
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected void getAll(RoutingContext routingContext)
    {
        try
        {
            var filter = JsonObject.mapFrom(Json.decodeValue(routingContext.request().getParam(FILTER)));

            if (filter != null && filter.containsKey(AIOpsObject.OBJECT_TYPE))
            {
                var items = new JsonArray();

                if (filter.containsKey(Metric.METRIC_NAME)) // filter contains metric.name so list down all objects that contains same metric name and after that filter object.type with requested metric.type
                {
                    var objects = ObjectConfigStore.getStore().getItems(MetricConfigStore.getStore().flatItems(Metric.METRIC_NAME, filter.getString(Metric.METRIC_NAME), Metric.METRIC_OBJECT));

                    if (objects != null)
                    {
                        for (var index = 0; index < objects.size(); index++)
                        {
                            var object = objects.getJsonObject(index);

                            if (object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(filter.getString(AIOpsObject.OBJECT_TYPE)))
                            {
                                items.add(NMSConstants.enrich(object));
                            }
                        }
                    }
                }
                else //filter only contains object type so based on object type list down all unique metric with their name
                {
                    var metrics = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT,
                            ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_TYPE, filter.getString(AIOpsObject.OBJECT_TYPE), ID));

                    var qualifiedMetrics = new JsonArray();

                    for (var index = 0; index < metrics.size(); index++)
                    {
                        var metric = metrics.getJsonObject(index);

                        if (!qualifiedMetrics.contains(metric.getString(Metric.METRIC_NAME)))
                        {
                            qualifiedMetrics.add(metric.getString(Metric.METRIC_NAME));

                            items.add(metric);
                        }
                    }
                }

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, items));
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                        .put(GlobalConstants.MESSAGE, ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void updateAll(RoutingContext routingContext)
    {
        try
        {
            var requestBody = routingContext.body().asJsonObject();

            if (requestBody.containsKey(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL)) // credential update request of metric from credential profile assign monitors screen
            {
                var items = requestBody.getJsonArray(APIConstants.REQUEST_PARAMS); // updated credential profile context with main metric types only (ex.. Windows, PostgreSql)

                requestBody.remove(APIConstants.REQUEST_PARAMS);

                var futures = new ArrayList<Future<Void>>();

                var updatedIds = new JsonArray();

                var snmp = requestBody.getString(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL).equalsIgnoreCase(NMSConstants.Protocol.SNMPV1V2c.getName()) || requestBody.getString(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL).equalsIgnoreCase(NMSConstants.Protocol.SNMPV3.getName());

                for (var index = 0; index < items.size(); index++)
                {
                    var item = items.getJsonObject(index);

                    for (var metric : MetricConfigStore.getStore().getItemsByObject(item.getLong(Metric.METRIC_OBJECT)))
                    {
                        if (metric.getString(Metric.METRIC_CREDENTIAL_PROFILE_PROTOCOL) != null && metric.getString(Metric.METRIC_TYPE) != null)
                        {
                            var valid = snmp ? metric.getString(Metric.METRIC_CREDENTIAL_PROFILE_PROTOCOL).equalsIgnoreCase(NMSConstants.Protocol.SNMPV1V2c.getName()) || metric.getString(Metric.METRIC_CREDENTIAL_PROFILE_PROTOCOL).equalsIgnoreCase(NMSConstants.Protocol.SNMPV3.getName())
                                    : metric.getString(Metric.METRIC_TYPE).equalsIgnoreCase(item.getString(Metric.METRIC_TYPE));    //#24468

                            if (valid)
                            {
                                var promise = Promise.<Void>promise();

                                futures.add(promise.future());

                                updatedIds.add(metric.getLong(ID));

                                var context = new JsonObject().put(Metric.METRIC_CREDENTIAL_PROFILE, item.getLong(Metric.METRIC_CREDENTIAL_PROFILE));

                                if (snmp && requestBody.getString(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL) != null)
                                {
                                    context.put(Metric.METRIC_CREDENTIAL_PROFILE_PROTOCOL, requestBody.getString(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL));
                                }

                                // if metric state is suspended update that metric state to enable
                                if (metric.getString(Metric.METRIC_STATE).equalsIgnoreCase(NMSConstants.State.INVALID.name()))
                                {
                                    context.put(Metric.METRIC_STATE, NMSConstants.State.ENABLE.name());
                                }

                                Bootstrap.configDBService().update(DBConstants.TBL_METRIC,
                                        new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, metric.getLong(ID)),
                                        context,
                                        routingContext.user().principal().getString(User.USER_NAME), routingContext.request().remoteAddress().host(),
                                        result ->
                                        {
                                            promise.complete();

                                            if (!metric.getString(Metric.METRIC_DISCOVERY_METHOD).equalsIgnoreCase(NMSConstants.DiscoveryMethod.INLINE.name()))
                                            {
                                                MetricCacheStore.getStore().addMetric(metric.getLong(ID), metric.getInteger(METRIC_POLLING_TIME));
                                            }
                                        });
                            }
                        }
                    }
                }

                Future.join(futures).onComplete(asyncResult ->
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                            .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.CREDENTIAL_PROFILE.getName())));

                    if (!updatedIds.isEmpty())
                    {
                        Bootstrap.vertx().executeBlocking(future -> MetricConfigStore.getStore().updateItems(updatedIds).onComplete(result -> future.complete()), false, result ->
                        {
                        });
                    }
                });
            }
            else
            {
                var objectIds = requestBody.getJsonArray(APIConstants.REQUEST_PARAM_IDS);

                requestBody.remove(APIConstants.REQUEST_PARAM_IDS);

                var futures = new ArrayList<Future<Void>>();

                var metricIds = new JsonArray();

                MetricConfigStore.getStore().flatItemsByMultipleValues(METRIC_NAME, CommonUtil.getString(requestBody.remove(METRIC_NAME)), Metric.METRIC_OBJECT, objectIds)
                        .forEach(metric ->
                        {
                            var promise = Promise.<Void>promise();

                            futures.add(promise.future());

                            if (!metric.getString(METRIC_STATE).equalsIgnoreCase(STATUS_SUSPEND))
                            {
                                metric.put(METRIC_STATE, requestBody.getString(METRIC_STATE));
                            }

                            metric.put(METRIC_POLLING_TIME, requestBody.getInteger(METRIC_POLLING_TIME));

                            metric.getJsonObject(Metric.METRIC_CONTEXT, new JsonObject()).put(TIMEOUT, requestBody.getInteger(TIMEOUT));

                            Bootstrap.configDBService().update(DBConstants.TBL_METRIC,
                                    new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, metric.getLong(ID)),
                                    metric,
                                    routingContext.user().principal().getString(User.USER_NAME), routingContext.request().remoteAddress().host(),
                                    result ->
                                    {
                                        metricIds.add(metric.getLong(ID));

                                        promise.complete();
                                    });
                        });

                Future.join(futures).onComplete(asyncResult ->
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                            .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.METRIC.getName()))
                            .put(APIConstants.REQUEST_PARAM_IDS, objectIds));

                    if (!metricIds.isEmpty())
                    {
                        Bootstrap.vertx().executeBlocking(future -> MetricConfigStore.getStore().updateItems(metricIds).onComplete(result -> future.complete()), false, result ->
                        {
                        });
                    }
                });
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        try
        {
            var items = RunbookPluginConfigStore.getStore().getItemsByMultiValueFieldAny(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, new JsonArray().add(CommonUtil.getLong(routingContext.request().getParam(ID))));

            if (!items.isEmpty())
            {
                for (var index = 0; index < items.size(); index++)
                {
                    var context = items.getJsonObject(index).getJsonObject(RunbookPlugin.RUNBOOK_PLUGIN_CONTEXT);

                    // if application is used in runbook plugin then add that runbook plugin name in response
                    if (context.containsKey(METRIC_TYPE) && context.getString(METRIC_TYPE).equalsIgnoreCase(routingContext.request().getParam(METRIC_TYPE)))
                    {
                        response.getMap().computeIfAbsent(Entity.RUNBOOK_PLUGIN.getName(), value -> new JsonArray());

                        response.put(Entity.RUNBOOK_PLUGIN.getName(), new JsonArray().add(items.getJsonObject(index).getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME)));
                    }
                }
            }

            items = MetricPluginConfigStore.getStore().getItemsByMultiValueFieldAny(MetricPlugin.METRIC_PLUGIN_ENTITIES, new JsonArray().add(CommonUtil.getLong(routingContext.request().getParam(ID))));

            if (!items.isEmpty())
            {
                for (var index = 0; index < items.size(); index++)
                {
                    var context = items.getJsonObject(index).getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT);

                    if (context.containsKey(METRIC_TYPE) && context.getString(METRIC_TYPE).equalsIgnoreCase(routingContext.request().getParam(METRIC_TYPE)))
                    {
                        response.getMap().computeIfAbsent(Entity.METRIC_PLUGIN.getName(), value -> new JsonArray());

                        response.put(Entity.METRIC_PLUGIN.getName(), new JsonArray().add(items.getJsonObject(index).getString(MetricPlugin.METRIC_PLUGIN_NAME)));
                    }
                }
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture(response);
    }
}
