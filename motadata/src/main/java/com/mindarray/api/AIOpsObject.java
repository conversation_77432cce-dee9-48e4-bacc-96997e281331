/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *	24-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 *	24-Mar-2025     Chandresh           MOTADATA-5426: Docker discovery and polling support added
 *  22-Apr-2025     Darshan Parmar      MOTADATA-5382: objects/types api to get types
 *  30-Jul-2025		Vismit		        MOTADATA-6312 Refactored regarding modifications to the AIOps scheduling workflow and removed redundant code
 *  24-Jul-2025     Umang               MOTADATA-6754 Support for Param as Tags
 *  05-Aug-2025     Aagam Salot         MOTADATA-6851: Added reason in object state maintenance
*/

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.config.ConfigConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.LicenseUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.Scheduler.SCHEDULER_CONTEXT;
import static com.mindarray.api.Scheduler.SCHEDULER_STATE;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.EVENT_CHANGE_LOCAL_NOTIFICATION;
import static com.mindarray.eventbus.EventBusConstants.UI_EVENT_UUID;
import static com.mindarray.flow.FlowEngineConstants.PROTOCOL;
import static com.mindarray.nms.NMSConstants.*;
import static com.mindarray.nms.NMSConstants.RediscoverJob.valueOfName;
import static com.mindarray.util.CronExpressionUtil.CRON_ONCE;

public class AIOpsObject extends AbstractAPI
{

    public static final String OBJECT_EVENT_PROCESSORS = "object.event.processors";
    public static final String OBJECT_NAME = "object.name";
    public static final String OBJECT_TYPE = "object.type";
    public static final String OBJECT_CATEGORY = "object.category";
    public static final String OBJECT_CONTEXT = "object.context";
    public static final String OBJECT_IP = "object.ip";
    public static final String OBJECT_ID = "object.id";
    public static final String OBJECT_VENDOR = "object.vendor";
    public static final String OBJECT_INSTANCES = "object.instances";
    public static final String OBJECT_PARENT_IP = "object.parent.ip";


    public static final String OBJECT_SYSTEM_OID = "object.system.oid";
    public static final String OBJECT_SNMP_DEVICE_CATALOG = "object.snmp.device.catalog";
    public static final String OBJECT_CREATION_TIME = "object.creation.time";
    public static final String OBJECT_CREATION_TIME_SECONDS = "object.creation.time.seconds";
    public static final String OBJECT_MODIFICATION_TIME = "object.modification.time";
    public static final String OBJECT_TARGET = "object.target"; //service check target //cloud instance etc....
    public static final String OBJECT_HOST = "object.host";
    public static final String OBJECT_ACCOUNT_ID = "object.account.id"; //used for cloud
    public static final String OBJECT_GROUPS = "object.groups";
    public static final String OBJECT_CREDENTIAL_PROFILE = "object.credential.profile";
    public static final String OBJECT_BUSINESS_HOUR_PROFILE = "object.business.hour.profile";
    public static final String OBJECT_STATE = "object.state";
    public static final String OBJECT_DISCOVERY_METHOD = "object.discovery.method";
    public static final String OBJECT_AGENT = "object.agent";
    public static final String OBJECT_SCHEDULER = "object.scheduler";
    public static final String OBJECT_REGION = "object.region";
    public static final String OBJECT_RESOURCE_GROUP = "object.resource.group";
    public static final String OBJECT_CUSTOM_FIELDS = "object.custom.fields";
    public static final String OBJECT_SYSTEM_TAGS = "object.system.tags";
    public static final String OBJECT_USER_TAGS = "object.user.tags";
    public static final String OBJECT_TAGS = "object.tags";
    public static final String OBJECT_MAKE_MODEL = "object.make.model";
    private static final Logger LOGGER = new Logger(AIOpsObject.class, GlobalConstants.MOTADATA_API, "Object API");

    public AIOpsObject()
    {
        super("objects", ObjectConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router, Set.of(REQUEST_GET));

            router.get("/" + endpoint).handler(this::getAll);

            router.get("/" + endpoint + "/types").handler(this::getTypes);

            router.get("/" + endpoint + "/:id").handler(this::get);

            router.put("/" + endpoint + "/:id/state").handler(this::updateState); //update  object state API

            router.post("/" + endpoint + "/state").handler(this::updateStates); // update All objects state API

            router.post("/" + endpoint + "/provision").handler(this::provision); // provision object API

            router.post("/" + endpoint + "/update").handler(this::updateAll); //update All objects API

            router.get("/" + endpoint + "/:id/references").handler(this::getReferences);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(ID));

            var items = SchedulerConfigStore.getStore().getItemsByMapMultiValueField(Scheduler.SCHEDULER_CONTEXT, TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS, id);

            if (items != null && !items.isEmpty())
            {
                if (!response.containsKey(Entity.SCHEDULER.getName()))
                {
                    response.put(Entity.SCHEDULER.getName(), new JsonArray());
                }
                response.getJsonArray(Entity.SCHEDULER.getName()).addAll(items);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture(response);
    }

    //This method is used to update states of object
    private void updateState(RoutingContext routingContext)
    {
        try
        {
            String eventType;

            var object = new JsonObject().put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(USER_NAME, routingContext.user().principal().getString(USER_NAME)).put(GlobalConstants.ID, CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID)));

            if (routingContext.body().asJsonObject().getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()))
            {
                eventType = EventBusConstants.EVENT_OBJECT_ENABLE;
            }
            else if (routingContext.body().asJsonObject().getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.DISABLE.name()))
            {
                eventType = EventBusConstants.EVENT_OBJECT_DISABLE;
            }
            else
            {
                eventType = EventBusConstants.EVENT_OBJECT_MAINTENANCE;

                object.put(REASON,routingContext.body().asJsonObject().getString(REASON));
            }

            Bootstrap.vertx().eventBus().<JsonObject>request(eventType,
                   object,
                    reply ->
                    {
                        var result = reply.result().body();

                        if (result.getString(GlobalConstants.STATUS).equalsIgnoreCase(GlobalConstants.STATUS_SUCCEED))
                        {
                            this.send(routingContext, result.put(RESPONSE_CODE, HttpStatus.SC_OK));
                        }
                        else
                        {
                            this.send(routingContext, result.put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST));
                        }
                    });
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void updateStates(RoutingContext routingContext)
    {
        try
        {
            var futures = new ArrayList<Future<Void>>();

            var ids = routingContext.body().asJsonObject().getJsonArray(REQUEST_PARAM_IDS);

            String eventType;

            var reason = EMPTY_VALUE;

            if (routingContext.body().asJsonObject().getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(State.ENABLE.name()))
            {
                eventType = EventBusConstants.EVENT_OBJECT_ENABLE;
            }
            else if (routingContext.body().asJsonObject().getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(State.DISABLE.name()))
            {
                eventType = EventBusConstants.EVENT_OBJECT_DISABLE;
            }
            else
            {
                eventType = EventBusConstants.EVENT_OBJECT_MAINTENANCE;

                reason = routingContext.body().asJsonObject().getString(REASON);
            }

            for (var index = 0; index < ids.size(); index++)
            {
                var promise = Promise.<Void>promise();

                futures.add(promise.future());

                var object = new JsonObject().put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(ID, ids.getLong(index)).put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(USER_NAME, routingContext.user().principal().getString(USER_NAME));

                if (eventType.equalsIgnoreCase(EventBusConstants.EVENT_OBJECT_MAINTENANCE))
                {
                    object.put(REASON,reason);
                }

                Bootstrap.vertx().eventBus().<JsonObject>request(eventType,
                        object, reply ->
                        {
                            if (reply.result().body().getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                            {
                                promise.complete();
                            }
                            else
                            {
                                promise.fail(reply.cause().getMessage());
                            }
                        });
            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK)
                            .put(REQUEST_PARAM_IDS, ids)
                            .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.OBJECT.getName())));
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                            .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace()))
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_UPDATE_FAILED, Entity.OBJECT.getName(), result.cause().getMessage())));
                }
            });
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected void getAll(RoutingContext routingContext)
    {
        try
        {
            var user = UserConfigStore.getStore().getItem(routingContext.user().principal().getLong(ID));

            if (user.getLong(ID).equals(DEFAULT_ID))//default
            {
                user.put(User.USER_GROUPS, GroupConfigStore.getStore().flatItems(ID));
            }

            if (routingContext.request().getParam(VisualizationConstants.ADMIN_ROLE) != null && routingContext.request().getParam(VisualizationConstants.ADMIN_ROLE).equalsIgnoreCase(YES))
            {

                this.send(routingContext, new JsonObject().put(STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK).put(RESULT, enrich(ObjectConfigStore.getStore().getItems())));

            }
            else if (routingContext.request().params().contains(FILTER))
            {
                var requestParameters = JsonObject.mapFrom(Json.decodeValue(routingContext.request().getParam(FILTER)));

                if (requestParameters != null)
                {
                    if (requestParameters.containsKey(KEY) && requestParameters.containsKey(VALUE))
                    {
                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                                .put(GlobalConstants.RESULT, enrich(ObjectConfigStore.getStore().getItems(ObjectConfigStore.getStore().getItemsByGroups(user.getJsonArray(User.USER_GROUPS), requestParameters.getString(KEY), requestParameters.getJsonArray(VALUE))))));
                    }
                    else if (requestParameters.containsKey(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL))
                    {
                        var objects = ObjectConfigStore.getStore().getItemsByGroups(user.getJsonArray(User.USER_GROUPS));

                        var credentialProtocols = requestParameters.getJsonArray(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL);

                        if (requestParameters.containsKey(Metric.METRIC_TYPE)) //for ssh/powershell/jdbc metric plugin -> custom metric plugin screen
                        {
                            var metrics = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_TYPE, requestParameters.getJsonArray(Metric.METRIC_TYPE));

                            var qualifiedObjects = new JsonArray();

                            for (var index = 0; index < metrics.size(); index++)
                            {
                                var metric = metrics.getJsonObject(index);

                                var credential = CredentialProfileConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_CREDENTIAL_PROFILE));

                                if ((credential.containsKey(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL)
                                        && credentialProtocols.contains(credential.getString(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL))
                                        && !qualifiedObjects.contains(metric.getLong(Metric.METRIC_OBJECT)) && objects.contains(metric.getLong(Metric.METRIC_OBJECT)))
                                        || requestParameters.getJsonArray(Metric.METRIC_TYPE).contains(Type.PING.getName()))
                                {
                                    qualifiedObjects.add(metric.getLong(Metric.METRIC_OBJECT));
                                }
                            }

                            objects = ObjectConfigStore.getStore().getItems(qualifiedObjects);

                            if (objects == null)
                            {
                                objects = new JsonArray();
                            }

                            //for list down agent objects
                            if (requestParameters.containsKey(AIOpsObject.OBJECT_TYPE)) // if type is linux than list down only linux type objects
                            {
                                ObjectConfigStore.getStore().getItemsByAgentIds().stream().filter(item -> requestParameters.getJsonArray(AIOpsObject.OBJECT_TYPE).contains(item.getString(AIOpsObject.OBJECT_TYPE))).toList().forEach(objects::add);
                            }

                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                                    .put(GlobalConstants.RESULT, enrich(objects)));
                        }
                        else  // for metric/application/monitor listing in credential profile for assign credentials -> credential screen
                        {
                            var items = new JsonObject().put(Entity.OBJECT.getName(), new JsonArray()).put(NMSConstants.APPS, new JsonArray()).put(Entity.METRIC.getName(), new JsonArray());

                            var metrics = credentialProtocols.contains(NMSConstants.Protocol.SNMPV1V2c.getName()) || credentialProtocols.contains(NMSConstants.Protocol.SNMPV3.getName()) ?
                                    MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_CREDENTIAL_PROFILE_PROTOCOL, new JsonArray().add(NMSConstants.Protocol.SNMPV1V2c.getName()).add(NMSConstants.Protocol.SNMPV3.getName()))
                                    : MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_CREDENTIAL_PROFILE_PROTOCOL, credentialProtocols);

                            var qualifiedIds = new HashSet<Long>();

                            for (var index = 0; index < metrics.size(); index++)
                            {
                                var metric = metrics.getJsonObject(index);

                                if (NMSConstants.CUSTOM_METRIC_TYPES.contains(metric.getString(Metric.METRIC_TYPE))) // custom metric category
                                {
                                    items.getJsonArray(Entity.METRIC.getName()).add(metric);

                                    qualifiedIds.add(metric.getLong(Metric.METRIC_OBJECT));
                                }

                                else if (NMSConstants.isBaseTypeMetricPlugin(metric.getString(Metric.METRIC_PLUGIN))
                                        || NMSConstants.CLOUD_PLUGINS.contains(metric.getString(Metric.METRIC_PLUGIN))) // if parent metric plugin or cloud type than only send UI
                                {
                                    if (NMSConstants.APPLICATION_TYPES.contains(metric.getString(Metric.METRIC_TYPE)))
                                    {
                                        items.getJsonArray(NMSConstants.APPS).add(metric);
                                    }

                                    else
                                    {
                                        items.getJsonArray(Entity.OBJECT.getName()).add(metric);
                                    }

                                    if (objects.contains(metric.getLong(Metric.METRIC_OBJECT)))
                                    {
                                        qualifiedIds.add(metric.getLong(Metric.METRIC_OBJECT));
                                    }
                                }
                            }

                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                                    .put(GlobalConstants.RESULT, new JsonObject().put(RESULT, items).put(OBJECTS, new JsonArray(new ArrayList<>(qualifiedIds)))));
                        }
                    }

                    else
                    {
                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                                .put(GlobalConstants.MESSAGE, ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS));
                    }
                }

                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                            .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                            .put(GlobalConstants.MESSAGE, ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS));
                }
            }

            else
            {
                JsonArray ids;

                //ui will pass array of category from params
                var categories = (JsonArray) Json.decodeValue(routingContext.request().getParam(OBJECT_CATEGORY));

                if (routingContext.request().params().contains(AIOpsObject.OBJECT_DISCOVERY_METHOD)) //for device monitoring settings ui needs to pass discovery method [remote]
                {
                    var discoveryMethod = (JsonArray) Json.decodeValue(routingContext.request().getParam(AIOpsObject.OBJECT_DISCOVERY_METHOD));

                    ids = ObjectConfigStore.getStore().getItemsByGroups(user.getJsonArray(User.USER_GROUPS), AIOpsObject.OBJECT_CATEGORY, categories, AIOpsObject.OBJECT_DISCOVERY_METHOD, discoveryMethod);
                }
                else
                {
                    ids = ObjectConfigStore.getStore().getItemsByGroups(user.getJsonArray(User.USER_GROUPS), AIOpsObject.OBJECT_CATEGORY, categories);
                }

                var visualizationTags = APIConstants.getTags(routingContext);

                var objects = ObjectConfigStore.getStore().getItems(ids);

                if (objects != null && !objects.isEmpty())
                {
                    //will return schedule object ids
                    var schedulers = SchedulerConfigStore.getStore().getSchedulerObjects(Scheduler.SCHEDULER_CONTEXT, OBJECTS, ids, JobScheduler.JobType.MAINTENANCE.getName());

                    var items = new JsonArray();

                    for (var index = 0; index < objects.size(); index++)
                    {
                        var object = objects.getJsonObject(index);

                        if (object.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVER.getName()))
                        {
                            var result = NMSConstants.getServerApps(MetricConfigStore.getStore().getItemsByObject(objects.getJsonObject(index).getLong(ID)));

                            object.put(NMSConstants.APPS, result.getJsonArray(NMSConstants.APPS));

                            object.put(NMSConstants.APP_PROCESS, result.getJsonObject(NMSConstants.APP_PROCESS));
                        }

                        NMSConstants.enrich(visualizationTags, object);

                        object.put(OBJECT_INSTANCES, MetricConfigStore.getStore().getInstances(object.getLong(ID)));

                        items.add(object.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.CLOUD.getName())
                                ? CommonUtil.removeSensitiveFields(object.put(OBJECT_SCHEDULER, schedulers.contains(object.getLong(GlobalConstants.ID)) ? GlobalConstants.YES : GlobalConstants.NO), true)
                                : object.put(OBJECT_SCHEDULER, schedulers.contains(object.getLong(GlobalConstants.ID)) ? GlobalConstants.YES : GlobalConstants.NO));
                    }

                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED).put(GlobalConstants.RESULT, enrich(items)));

                }

                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED).put(GlobalConstants.RESULT, new JsonArray()));
                }
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected void get(RoutingContext routingContext)
    {
        try
        {
            var object = this.configStore.getItem(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID)));

            if (object != null && !object.isEmpty())
            {
                var visualizationTags = APIConstants.getTags(routingContext);

                var item = CommonUtil.removeSensitiveFields(object, true);

                if (object.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVER.getName()))
                {
                    var result = NMSConstants.getServerApps(MetricConfigStore.getStore().getItemsByObject(item.getLong(ID)));

                    item.put(NMSConstants.APPS, result.getJsonArray(NMSConstants.APPS));

                    item.put(NMSConstants.APP_PROCESS, result.getJsonObject(NMSConstants.APP_PROCESS));
                }

                if (item.getJsonArray(OBJECT_TAGS) != null && !item.getJsonArray(OBJECT_TAGS).isEmpty())
                {
                    //sending all tags(user and default), hence type = EMPTY_VALUE
                    item.put(OBJECT_TAGS, TagConfigStore.getStore().getItems(item.getJsonArray(OBJECT_TAGS), Tag.TagType.OBJECT, EMPTY_VALUE));
                }

                NMSConstants.enrich(visualizationTags,item);

                item.put(OBJECT_SCHEDULER, SchedulerConfigStore.getStore().containsObject(Scheduler.SCHEDULER_CONTEXT, OBJECTS, item.getLong(ID), JobScheduler.JobType.MAINTENANCE.getName()) ? GlobalConstants.YES : GlobalConstants.NO)
                        .put(OBJECT_INSTANCES, MetricConfigStore.getStore().getInstances(object.getLong(ID)));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                        .put(GlobalConstants.RESULT, item));
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED).put(GlobalConstants.RESULT, new JsonObject()));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected Future<JsonObject> beforeDelete(RoutingContext routingContext)
    {
        return Future.succeededFuture(new JsonObject().put(SHALLOW_DELETE, YES));
    }

    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_OBJECT_UNPROVISION, ObjectConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID)))
                .put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(USER_NAME, routingContext.user().principal().getString(USER_NAME)));

        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.MESSAGE, InfoMessageConstants.OBJECT_ARCHIVED_SUCCEEDED)
                .put(ID, CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID))));

        return Future.succeededFuture();
    }

    //If any monitor is used in  discovery, scheduler and metric then it will not allow to delete that monitor using reference entity.
    @Override
    protected void deleteAll(RoutingContext routingContext)
    {
        try
        {
            var items = routingContext.body().asJsonObject().getJsonArray(APIConstants.REQUEST_PARAM_IDS);

            if (items != null && !items.isEmpty())
            {
                var archivedItems = new JsonArray();

                var futures = new ArrayList<Future<Void>>();

                var references = new JsonArray();

                for (var item : items)
                {
                    var promise = Promise.<Void>promise();

                    futures.add(promise.future());

                    this.configStore.getReferenceEntities(CommonUtil.getLong(item)).onComplete(result ->
                    {
                        var qualify = false;

                        if (result.succeeded())
                        {
                            var topologyPlugins = SchedulerConfigStore.getStore().getItemsByMapMultiValueField(Scheduler.SCHEDULER_CONTEXT, TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS, CommonUtil.getLong(item));

                            if (topologyPlugins != null && !topologyPlugins.isEmpty())
                            {
                                if (!result.result().containsKey(Entity.SCHEDULER.getName()))
                                {
                                    result.result().put(Entity.SCHEDULER.getName(), new JsonArray());
                                }

                                result.result().getJsonArray(Entity.SCHEDULER.getName()).addAll(topologyPlugins);
                            }

                            if (!result.result().isEmpty())
                            {
                                qualify = true;
                            }
                        }

                        else
                        {
                            qualify = true;
                        }

                        if (qualify)
                        {
                            var object = ObjectConfigStore.getStore().getObjectName(CommonUtil.getLong(item));

                            result.result().getMap().keySet().forEach(key ->
                            {
                                for (var index = 0; index < result.result().getJsonArray(key).size(); index++)
                                {
                                    var reference = result.result().getJsonArray(key).getJsonObject(index);

                                    if (key.equalsIgnoreCase(Entity.SCHEDULER.getName()))
                                    {
                                        references.add(new JsonObject().put(ENTITY_PROPERTY_TYPE, key)
                                                .put(ENTITY_NAME, object)
                                                .put(ENTITY_PROPERTY_NAME, reference.getString(Scheduler.SCHEDULER_JOB_TYPE).equalsIgnoreCase(JobScheduler.JobType.REDISCOVER.getName()) ?
                                                        reference.getJsonObject(Scheduler.SCHEDULER_CONTEXT).getString(NMSConstants.REDISCOVER_JOB) : reference.getString(Scheduler.SCHEDULER_JOB_TYPE)));
                                    }
                                    else
                                    {
                                        references.add(new JsonObject().put(ENTITY_PROPERTY_TYPE, key)
                                                .put(ENTITY_NAME, object)
                                                .put(ENTITY_PROPERTY_NAME, reference.getString(APIConstants.REF_PROPS_BY_ENTITY.get(key))));
                                    }
                                }
                            });
                        }
                        else
                        {
                            archivedItems.add(item);

                            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_OBJECT_UNPROVISION, ObjectConfigStore.getStore().getItem(CommonUtil.getLong(item)).put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(USER_NAME, routingContext.user().principal().getString(USER_NAME)));
                        }

                        promise.complete();
                    });

                }

                Future.join(futures).onComplete(result ->
                {
                    if (!references.isEmpty())
                    {
                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                .put(RESULT, references) // ui will display this references in tooltip
                                .put(REQUEST_PARAM_IDS, archivedItems)
                                .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED_IN_USED, Entity.OBJECT.getName()))
                                .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL));
                    }

                    else
                    {
                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.MESSAGE, InfoMessageConstants.OBJECT_ARCHIVED_SUCCEEDED)
                                .put(REQUEST_PARAM_IDS, archivedItems));
                    }
                });
            }

            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL)
                        .put(MESSAGE, ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS));
            }
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void provision(RoutingContext routingContext)
    {
        try
        {
            var requestParameters = routingContext.body().asJsonObject().getJsonArray(APIConstants.REQUEST_PARAMS);

            if (requestParameters != null && !requestParameters.isEmpty())
            {
                var discovery = DiscoveryConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.body().asJsonObject().getLong(ID)));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                        .put(GlobalConstants.MESSAGE, InfoMessageConstants.OBJECT_PROVISION_START_SUCCEEDED));

                Bootstrap.vertx().executeBlocking(future ->
                {

                    try
                    {
                        var futures = new ArrayList<Future<Void>>();

                        var objects = new JsonArray();

                        var uuid = routingContext.body().asJsonObject().getString(UI_EVENT_UUID);

                        for (var index = 0; index < requestParameters.size(); index++)
                        {
                            var context = requestParameters.getJsonObject(index);

                            if (uuid != null)
                            {
                                context.put(UI_EVENT_UUID, uuid);
                            }

                            // For old discovery, We will be taking manage status as NO

                            /* Method to set configuration specific data which will be
                             * used while creating configuration object
                             * -> It will only add configuration if user have valid Config license.*/

                            if (LicenseUtil.CONFIG_MANAGEMENT_ENABLED.get() && discovery != null && context.containsKey(AIOpsObject.OBJECT_TYPE) && ConfigConstants.qualify(NMSConstants.Type.valueOfName(context.getString(AIOpsObject.OBJECT_TYPE))) != null)
                            {
                                context.put(Discovery.DISCOVERY_CONFIG_MANAGEMENT_STATUS, discovery.getString(Discovery.DISCOVERY_CONFIG_MANAGEMENT_STATUS, NO));

                                if (context.getString(Discovery.DISCOVERY_CONFIG_MANAGEMENT_STATUS).equals(YES))
                                {
                                    context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, discovery.getJsonArray(Discovery.DISCOVERY_CREDENTIAL_PROFILES));
                                }

                                if (discovery.containsKey(Discovery.DISCOVERY_EMAIL_RECIPIENTS))
                                {
                                    context.put(Discovery.DISCOVERY_EMAIL_RECIPIENTS, discovery.getJsonArray(Discovery.DISCOVERY_EMAIL_RECIPIENTS));
                                }

                                if (discovery.containsKey(Discovery.DISCOVERY_SMS_RECIPIENTS))
                                {
                                    context.put(Discovery.DISCOVERY_SMS_RECIPIENTS, discovery.getJsonArray(Discovery.DISCOVERY_SMS_RECIPIENTS));
                                }
                            }

                            if (context.containsKey(NMSConstants.REDISCOVER_JOB))
                            {
                                var rediscoverJob = valueOfName(context.getString(NMSConstants.REDISCOVER_JOB));

                                if (rediscoverJob == NMSConstants.RediscoverJob.PROCESS || rediscoverJob == NMSConstants.RediscoverJob.WINDOWS_SERVICE
                                        || rediscoverJob == NMSConstants.RediscoverJob.FILE_DIRECTORY || rediscoverJob == NMSConstants.RediscoverJob.VIRTUAL_MACHINE
                                        || rediscoverJob == NMSConstants.RediscoverJob.NETWORK_INTERFACE || rediscoverJob == NMSConstants.RediscoverJob.ACCESS_POINT
                                        || rediscoverJob == RediscoverJob.WAN_LINK || rediscoverJob == RediscoverJob.VIRTUAL_MACHINE_HCI || rediscoverJob == RediscoverJob.CONTAINER)
                                {
                                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_OBJECT_PROVISION, context
                                            .put(OBJECTS, new JsonArray().add(context.getJsonObject(NMSConstants.OBJECT)))
                                            .put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                                            .put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(USER_NAME, routingContext.user().principal().getString(USER_NAME)));
                                }
                                else
                                {
                                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_OBJECT_PROVISION, context
                                            .put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                                            .put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(USER_NAME, routingContext.user().principal().getString(USER_NAME)));
                                }
                            }
                            else if (context.containsKey(AIOpsObject.OBJECT_TYPE) && NMSConstants.NETWORK_DEVICES.contains(context.getString(AIOpsObject.OBJECT_TYPE)))
                            {
                                var promise = Promise.<Void>promise();

                                futures.add(promise.future());

                                Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_OBJECT_PROVISION, context
                                                .put(EventBusConstants.EVENT_REPLY, YES)
                                                .put(APIConstants.SESSION_ID, routingContext.user().principal().getString(APIConstants.SESSION_ID))
                                                .put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(USER_NAME, routingContext.user().principal().getString(USER_NAME)),
                                        new DeliveryOptions().setSendTimeout(300000L), reply ->
                                        {
                                            if (reply.succeeded())
                                            {
                                                try
                                                {
                                                    var result = reply.result().body();

                                                    if (result != null && result.getString(STATUS) != null && result.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                                                    {
                                                        objects.add(result.getLong(ID));
                                                    }
                                                }
                                                catch (Exception exception)
                                                {
                                                    LOGGER.error(exception);
                                                }
                                            }

                                            promise.complete();
                                        });
                            }
                            else
                            {
                                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_OBJECT_PROVISION, context
                                        .put(APIConstants.SESSION_ID, routingContext.user().principal().getString(APIConstants.SESSION_ID))
                                        .put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(USER_NAME, routingContext.user().principal().getString(USER_NAME)));
                            }
                        }

                        Future.join(futures).onComplete(result ->
                        {
                            if (!objects.isEmpty() && discovery != null)
                            {
                                if (discovery.containsKey(Discovery.DISCOVERY_CONTEXT))
                                {
                                    discovery.mergeIn(discovery.getJsonObject(Discovery.DISCOVERY_CONTEXT));

                                    discovery.remove(Discovery.DISCOVERY_CONTEXT);
                                }

                                if (discovery.containsKey(TopologyPlugin.TOPOLOGY_PLUGIN_DISCOVERY) && discovery.getString(TopologyPlugin.TOPOLOGY_PLUGIN_DISCOVERY).equalsIgnoreCase(YES))
                                {
                                    discovery.put(OBJECTS, objects);

                                    var dateTime = DateTimeUtil.getScheduledTimestamp(System.currentTimeMillis() + (60 * 1000L));

                                    var item = new JsonObject().put(Scheduler.SCHEDULER_TIMELINE, CRON_ONCE)
                                            .put(Scheduler.SCHEDULER_TIMES, new JsonArray().add(dateTime.split(" ")[1]))
                                            .put(Scheduler.SCHEDULER_START_DATE, dateTime.split(" ")[0])
                                            .put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.TOPOLOGY.getName())
                                            .put(SCHEDULER_STATE, YES)
                                            .put(SCHEDULER_CONTEXT, new JsonObject().put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS, discovery.getJsonArray(TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS))
                                                    .put(NMSConstants.TOPOLOGY_PROTOCOLS, discovery.getJsonArray(TOPOLOGY_PROTOCOLS))
                                                    .put(NMSConstants.TOPOLOGY_LINK_LAYER, discovery.getJsonArray(TOPOLOGY_LINK_LAYER))
                                                    .put(TopologyPlugin.TOPOLOGY_PLUGIN_DISCOVERY, YES));

                                    Bootstrap.configDBService().save(DBConstants.TBL_SCHEDULER, item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, response ->
                                    {
                                        if (response.succeeded())
                                        {
                                            SchedulerConfigStore.getStore().addItem(response.result()).onComplete(asyncResult -> JobScheduler.scheduleCustomJob(SchedulerConfigStore.getStore().getItem(response.result())));
                                        }
                                    });
                                }
                            }
                        });
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                    finally
                    {
                        future.complete();
                    }
                }, false, result ->
                {
                });
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.OBJECT_PROVISIONING_FAILED, ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS)));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private JsonArray enrich(JsonArray items)
    {
        for (var index = 0; index < items.size(); index++)
        {
            var entity = items.getJsonObject(index);

            if (entity.getJsonArray(OBJECT_TAGS) != null && !entity.getJsonArray(OBJECT_TAGS).isEmpty())
            {
                //sending all tags(user and default), hence type = EMPTY_VALUE
                entity.put(OBJECT_TAGS, TagConfigStore.getStore().getItems(entity.getJsonArray(OBJECT_TAGS), Tag.TagType.OBJECT, EMPTY_VALUE));
            }
        }

        return items;
    }

    @Override
    protected Future<JsonObject> afterUpdate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        //bug - 4210
        var item = ObjectConfigStore.getStore().getItem(entity.getLong(ID));

        var context = item.getJsonObject(AIOpsObject.OBJECT_CONTEXT);

        if (context != null && (CommonUtil.isNotNullOrEmpty(context.getString(NMSConstants.PING_CHECK_STATUS)) || CommonUtil.isNotNullOrEmpty(context.getString(NMSConstants.INTERFACE_DISCOVERY))))
        {
            var state = context.getString(NMSConstants.PING_CHECK_STATUS).equalsIgnoreCase(YES) ? NMSConstants.State.ENABLE.name() : NMSConstants.State.DISABLE.name();

            if (state.equalsIgnoreCase(NMSConstants.State.DISABLE.name()))
            {
                changeState(NMSConstants.State.ENABLE.name(), item, NMSConstants.MetricPlugin.OBJECT_STATUS.getName());

                changeState(state, item, NMSConstants.MetricPlugin.AVAILABILITY.getName());
            }
            else
            {
                //For PING status Enable
                changeState(state, item, NMSConstants.MetricPlugin.AVAILABILITY.getName());

                changeState(NMSConstants.State.DISABLE.name(), item, NMSConstants.MetricPlugin.OBJECT_STATUS.getName());
            }

            if (context.containsKey(NMSConstants.INTERFACE_DISCOVERY))
            {
                changeState(context.getString(NMSConstants.INTERFACE_DISCOVERY).equalsIgnoreCase(YES) ? NMSConstants.State.ENABLE.name() : NMSConstants.State.DISABLE.name(), item, NMSConstants.MetricPlugin.SNMP_INTERFACE.getName());
            }


        }

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_OBJECT.name())
                .put(ID, entity.getLong(ID)).put(AIOpsObject.OBJECT_GROUPS, item.getJsonArray(AIOpsObject.OBJECT_GROUPS)));

        notify(entity.getLong(ID));

        return Future.succeededFuture();
    }

    private void changeState(String state, JsonObject item, String metricPlugin)
    {
        if (state != null)
        {
            var metric = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(item.getLong(ID), metricPlugin));

            if (metric != null && !metric.getString(Metric.METRIC_STATE).equalsIgnoreCase(state))
            {
                Bootstrap.vertx().eventBus().send(state.equalsIgnoreCase(NMSConstants.State.ENABLE.name()) ? EventBusConstants.EVENT_METRIC_ENABLE : EventBusConstants.EVENT_METRIC_DISABLE, metric.put(Metric.METRIC_STATE, state));
            }
        }
    }

    private void updateAll(RoutingContext routingContext, JsonObject requestParameters)
    {
        try
        {
            var ids = requestParameters.getJsonArray(REQUEST_PARAM_IDS);

            requestParameters.remove(REQUEST_PARAM_IDS);

            //For Bulk Tagging Purpose old data and new data will be merged into one and stored in collections we will use only Update...
            if (requestParameters.containsKey(AIOpsObject.OBJECT_TAGS) || requestParameters.containsKey(AIOpsObject.OBJECT_GROUPS))
            {
                var requestParam = requestParameters.containsKey(OBJECT_TAGS) ? AIOpsObject.OBJECT_TAGS : AIOpsObject.OBJECT_GROUPS;

                var futures = new ArrayList<Future<Void>>();

                for (var id : ids)
                {
                    var objects = new JsonArray().addAll(requestParameters.getJsonArray(requestParam));

                    var promise = Promise.<Void>promise();

                    futures.add(promise.future());

                    var items = ObjectConfigStore.getStore().getItem(CommonUtil.getLong(id)).getJsonArray(requestParam);

                    if (items != null && !items.isEmpty())
                    {
                        for (var item : items)
                        {
                            if (!objects.contains(CommonUtil.getLong(item)))
                            {
                                objects.add(CommonUtil.getLong(item));
                            }
                        }
                    }

                    Bootstrap.configDBService().update(DBConstants.TBL_OBJECT, new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, id),
                            new JsonObject().put(requestParam, objects),
                            routingContext.user().principal().getString(USER_NAME),
                            routingContext.request().remoteAddress().host(),
                            result ->
                            {
                                if (result.succeeded() && !result.result().isEmpty())
                                {
                                    ObjectConfigStore.getStore().updateItem(CommonUtil.getLong(id)).onComplete(asyncResult -> promise.complete());
                                }
                                else
                                {
                                    promise.fail(result.cause());
                                }
                            });
                }

                Future.join(futures).onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK)
                                .put(REQUEST_PARAM_IDS, ids)
                                .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.OBJECT.getName())));
                    }
                    else
                    {
                        this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                .put(GlobalConstants.ERROR, result.failed() ? CommonUtil.formatStackTrace(result.cause().getStackTrace()) : UNKNOWN)
                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_UPDATE_FAILED, Entity.OBJECT.getName(), result.failed() ? result.cause().getMessage() : UNKNOWN)));
                    }
                });
            }
            else
            {
                Bootstrap.configDBService().updateAll(DBConstants.TBL_OBJECT,
                        new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, ids),
                        requestParameters,
                        routingContext.user().principal().getString(USER_NAME),
                        routingContext.request().remoteAddress().host(),
                        result ->
                        {
                            if (result.succeeded() && !result.result().isEmpty())
                            {
                                ObjectConfigStore.getStore().updateItems(ids).onComplete(asyncResult ->
                                {
                                    this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK)
                                            .put(REQUEST_PARAM_IDS, ids)
                                            .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.OBJECT.getName())));

                                    for (var index = 0; index < ids.size(); index++)
                                    {
                                        notify(ids.getLong(index));
                                    }
                                });
                            }
                            else
                            {
                                this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                        .put(GlobalConstants.ERROR, result.failed() ? CommonUtil.formatStackTrace(result.cause().getStackTrace()) : UNKNOWN)
                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_UPDATE_FAILED, Entity.OBJECT.getName(), result.failed() ? result.cause().getMessage() : UNKNOWN)));
                            }
                        });

            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void updateAll(RoutingContext routingContext)
    {
        try
        {
            var requestBody = routingContext.body().asJsonObject();

            if (requestBody.containsKey(AIOpsObject.OBJECT_TAGS))
            {
                requestBody.put(OBJECT_TAGS, TagConfigStore.getStore().addItems(requestBody.getJsonArray(AIOpsObject.OBJECT_TAGS), Tag.TagType.OBJECT.getName(), DBConstants.ENTITY_TYPE_USER));

                updateAll(routingContext, requestBody);
            }
            else
            {
                updateAll(routingContext, requestBody);
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void getTypes(RoutingContext routingContext)
    {
        try
        {
            var types = new HashSet<String>();

            if (routingContext.request().params().contains(FILTER))
            {
                var filter = new JsonObject(routingContext.request().getParam(FILTER));

                var key = filter.getString(KEY);

                var values = filter.getJsonArray(VALUE);

                if (PROTOCOL.equals(key))
                {
                    var filters = values.stream().map(CommonUtil::getString).map(Protocol::valueOfName)
                            .collect(Collectors.toSet());

                    OBJECT_TYPES.stream().map(JsonObject::mapFrom)
                            .filter(object -> filters.contains(Protocol.valueOf(object.getString(PROTOCOL))))
                            .map(object -> Type.valueOf(object.getString(OBJECT_TYPE)).getName())
                            .forEach(types::add);

                }
                else if (OBJECT_CATEGORY.equals(key))
                {
                    var filters = values.stream().map(CommonUtil::getString).map(Category::valueOfName)
                            .collect(Collectors.toSet());

                    OBJECT_TYPES.stream().map(JsonObject::mapFrom)
                            .filter(object ->
                                    filters.contains(Category.valueOf(object.getString(OBJECT_CATEGORY)))
                            )
                            .map(object -> Type.valueOf(object.getString(OBJECT_TYPE)).getName())
                            .forEach(types::add);
                }
                else
                {
                    OBJECT_TYPES.stream().map(JsonObject::mapFrom)
                            .map(object -> Type.valueOf(object.getString(OBJECT_TYPE)).getName())
                            .forEach(types::add);
                }
            }
            else
            {
                OBJECT_TYPES.stream().map(JsonObject::mapFrom)
                        .map(object -> Type.valueOf(object.getString(OBJECT_TYPE)).getName())
                        .forEach(types::add);
            }

            this.send(routingContext, new JsonObject()
                    .put(RESPONSE_CODE, HttpStatus.SC_OK)
                    .put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                    .put(GlobalConstants.RESULT, new JsonArray(new ArrayList<>(types))));
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void notify(long id)
    {
        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_PUBLICATION, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_CHANGE_NOTIFICATION)
                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_OBJECT.name())
                .put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)
                .put(ID, id));

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_OBJECT.name())
                .put(ID, id));
    }

    // Because when we update the object then new value will merge with old value.
    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        var promise = Promise.<JsonObject>promise();

        var requestBody = routingContext.body().asJsonObject();

        if (requestBody.getJsonArray(OBJECT_TAGS) != null && !requestBody.getJsonArray(OBJECT_TAGS).isEmpty())
        {
            requestBody.put(OBJECT_TAGS, TagConfigStore.getStore().addItems(requestBody.getJsonArray(OBJECT_TAGS), Tag.TagType.OBJECT.getName(), DBConstants.ENTITY_TYPE_USER));
        }

        var item = ObjectConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID)));

        //MOTADATA-4662
        if (requestBody.containsKey(AIOpsObject.OBJECT_GROUPS) && GroupConfigStore.getStore().getItems(requestBody.getJsonArray(AIOpsObject.OBJECT_GROUPS)).isEmpty())
        {
            requestBody.getJsonArray(AIOpsObject.OBJECT_GROUPS).clear().add(GroupConfigStore.getStore().getItemsByMapMultiValueField(Group.GROUP_CONTEXT, OBJECT_TYPE, item.getString(OBJECT_TYPE)).stream().filter(group -> JsonObject.mapFrom(group).getLong(Group.FIELD_PARENT_GROUP) == DUMMY_NUMERIC_VALUE).mapToLong(group -> JsonObject.mapFrom(group).getLong(ID)).findFirst().orElse(DEFAULT_ID));
        }

        if (item.containsKey(OBJECT_MONITOR_POLLING_FAILED_NOTIFICATION_STATUS) && item.getString(OBJECT_MONITOR_POLLING_FAILED_NOTIFICATION_STATUS).equalsIgnoreCase(YES) && routingContext.body().asJsonObject().getString(OBJECT_MONITOR_POLLING_FAILED_NOTIFICATION_STATUS).equalsIgnoreCase(NO))
        {
            requestBody.put(NMSConstants.OBJECT_EMAIL_NOTIFICATION_RECIPIENTS, new JsonArray()).put(NMSConstants.OBJECT_SMS_NOTIFICATION_RECIPIENTS, new JsonArray()); // need to remove emails and sms if user turned of "object.monitor.polling.failed.notification.status"
        }

        // updation case for monitor name
        if (requestBody.containsKey(OBJECT_NAME) && !requestBody.getString(OBJECT_NAME).equalsIgnoreCase(item.getString(OBJECT_NAME)) && ObjectConfigStore.getStore().getIdByObjectName(requestBody.getString(OBJECT_NAME)) != null)
        {
            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_UPDATE_FAILED, Entity.OBJECT.getName(), "provided monitor name is already in use")).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL));

            promise.fail(String.format(ErrorMessageConstants.ENTITY_UPDATE_FAILED, Entity.OBJECT.getName(), "provided monitor name is already in use"));
        }
        else
        {
            ObjectConfigStore.getStore().updateItem(item.getString(OBJECT_NAME), requestBody.getString(OBJECT_NAME));

            promise.complete(ObjectConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID))).mergeIn(routingContext.body().asJsonObject()));
        }

        return promise.future();
    }
}
