/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *	23-Jul-2025		Nikun<PERSON> Patel		MOTADATA-6236: Initial Commit
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.db.DBConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mindarray.ErrorMessageConstants.INTERNAL_SERVER_EXCEPTION;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.GlobalConstants.STATUS;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.EVENT_AUDIT;

/**
 * Handles API endpoints related to DNS Server Profiles.
 * Provides operations for initialization, reference resolution, entity counts, and deletion logic.
 */
public class DNSServerProfile extends AbstractAPI
{
    private static final Logger LOGGER = new Logger(DNSServerProfile.class, MOTADATA_API, "DNS Server Profile");

    public static final String DNS_SERVER_PROFILE_NAME = "dns.server.profile.name";
    public static final String DNS_SERVER_PROFILE_DESCRIPTION = "dns.server.profile.description";
    public static final String DNS_SERVER_IP = "dns.server.ip";
    public static final String DNS_SERVER_RESOLVER_TIMEOUT = "dns.server.resolver.timeout";
    public static final String DNS_SERVER_RESOLVER_TYPE = "dns.server.resolver.type";
    public static final String DNS_SERVER_PORT = "dns.server.port";
    public static final String DNS_SERVER_RESOLVER_LAST_SYNC_TIME = "dns.server.resolver.last.sync.time";
    public static final String DNS_SERVER_RESOLVED_OBJECTS = "dns.server.resolved.objects";
    public static final String DNS_SERVER_RESOLVED_SOURCES = "dns.server.resolved.sources";
    public static final String DNS_SERVER_PROFILE_CONTEXT = "dns.server.profile.context";

    /**
     * Initializes the DNSProfile API with its configuration store and logger.
     */
    public DNSServerProfile()
    {
        super("dns-server-profiles", DNSServerProfileConfigStore.getStore(), LOGGER);
    }

    /**
     * Registers the DNS profile routes with the given Vert.x router.
     *
     * @param router the Vert.x router to which the routes are added
     */
    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);

            router.get("/" + endpoint + "/:id/references").handler(this::getReferences);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Hook to add resolved objects and source IPs to the response before returning references.
     *
     * @param routingContext the current routing context
     * @param response the JSON response object to enrich
     * @return future containing the enriched response
     */
    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            var item = DNSServerProfileConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID)));

            if (item.containsKey(DNSServerProfile.DNS_SERVER_RESOLVED_OBJECTS) && item.getJsonArray(DNSServerProfile.DNS_SERVER_RESOLVED_OBJECTS) != null)
            {
                response.put(APIConstants.Entity.OBJECT.getName(), ObjectConfigStore.getStore().getItems(item.getJsonArray(DNSServerProfile.DNS_SERVER_RESOLVED_OBJECTS)));
            }

            if (item.containsKey(DNSServerProfile.DNS_SERVER_RESOLVED_SOURCES) && item.getJsonArray(DNSServerProfile.DNS_SERVER_RESOLVED_SOURCES) != null)
            {
                response.put("IPs", item.getJsonArray(DNSServerProfile.DNS_SERVER_RESOLVED_SOURCES));
            }

            promise.complete(response);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    /**
     * Hook to compute and add the count of resolved objects and sources for each DNS profile.
     *
     * @param response the JSON response object to populate with counts
     * @return future containing the response with entity counts
     */
    @Override
    protected Future<JsonObject> getEntityCountPreHook(JsonObject response)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            var items = DNSServerProfileConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                try
                {
                    var item = items.getJsonObject(index);

                    response.put(CommonUtil.getString(item.getLong(ID)), item.getJsonArray(DNSServerProfile.DNS_SERVER_RESOLVED_OBJECTS, new JsonArray()).size() + item.getJsonArray(DNSServerProfile.DNS_SERVER_RESOLVED_SOURCES, new JsonArray()).size());
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }

            promise.complete(response);

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    /**
     * Handles deletion of a DNS profile.
     * Performs pre-deletion checks, invokes the delete operation, and sends audit events.
     *
     * @param routingContext the current routing context
     */
    @Override
    protected void delete(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var table = schema.getString(APIConstants.ENTITY_TABLE);

                this.beforeDelete(routingContext).compose(parameters ->
                        Future.<JsonObject>future(promise ->
                        {
                            var id = CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID));

                            var item = this.configStore.getItem(id);

                            if (item != null && !item.isEmpty())
                            {
                                var validRequest = true;

                                if (item.getString(DBConstants.FIELD_TYPE) != null && item.getString(DBConstants.FIELD_TYPE).equalsIgnoreCase(DBConstants.ENTITY_TYPE_SYSTEM))
                                {
                                    validRequest = false;

                                    var message = String.format(ErrorMessageConstants.ENTITY_DELETE_NOT_ALLOWED, schema.getString(APIConstants.ENTITY_NAME));

                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                            .put(GlobalConstants.MESSAGE, message).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL));

                                    promise.fail(message);

                                    Bootstrap.vertx().eventBus().send(EVENT_AUDIT,
                                            new JsonObject().put(USER_NAME, routingContext.user().principal().getString(User.USER_NAME)).put(ENTITY_TABLE, table)
                                                    .put(REQUEST, REQUEST_DELETE).put(MESSAGE, message).put(STATUS, Boolean.FALSE));

                                }

                                if (validRequest)
                                {
                                    Bootstrap.configDBService().delete(table,
                                            new JsonObject().put(DBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, id),
                                            routingContext.user().principal().getString(User.USER_NAME), routingContext.request().remoteAddress().host(),
                                            result ->
                                            {
                                                if (result.succeeded())
                                                {
                                                    if (!result.result().isEmpty())
                                                    {
                                                        this.configStore.deleteItem(id);

                                                        promise.complete(new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                                                .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_DELETED, schema.getString(APIConstants.ENTITY_NAME))).put(ID, id));

                                                    }

                                                    else
                                                    {
                                                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                                                .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), UNKNOWN)).put(GlobalConstants.STATUS, STATUS_FAIL));

                                                        promise.fail(String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), UNKNOWN));
                                                    }
                                                }

                                                else
                                                {
                                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                                                            .put(GlobalConstants.STATUS, STATUS_FAIL)
                                                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), result.cause().getMessage()))
                                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                            .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));

                                                    promise.fail(result.cause());

                                                }
                                            });
                                }
                            }

                            else
                            {
                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, Entity.METRIC_POLICY.getName()))));

                                promise.fail(String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, schema.getString(APIConstants.ENTITY_NAME)));
                            }
                        }).compose(entity -> this.afterDelete(entity, routingContext))
                );
            }

            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
        }
    }

    /**
     * Enum representing the type of DNS resolver used.
     * <p>
     * It can be either {@code MANUAL}, where the resolver is manually configured,
     * or {@code AUTOMATIC}, where the system handles it automatically.
     */

    public enum ResolverType
    {
        MANUAL("Manual"),
        AUTOMATIC("Auto");

        private static final Map<String, ResolverType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(ResolverType::getName, entity -> entity)));
        private final String name;

        ResolverType(String name)
        {
            this.name = name;
        }

        public static ResolverType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }
}
