/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 *  28-Jul-2025		Vismit Mandlik		MOTADATA-6673: Added dashboard usage counts in getAll widgets api
 *  02-May-2025		Chopra Deven		MOTADATA-6050: CRUD of widget in WidgetCacheStore
 */
package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.DashboardConfigStore;
import com.mindarray.store.ObjectCacheStore;
import com.mindarray.store.TemplateConfigStore;
import com.mindarray.store.WidgetConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.Dashboard.*;
import static com.mindarray.db.DBConstants.FIELD_TYPE;

public class Widget extends AbstractAPI
{
    private static final Logger LOGGER = new Logger(Widget.class, MOTADATA_API, "Widget API");

    public Widget()
    {
        super("widgets", WidgetConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);

            router.get("/" + endpoint + "/:id/references").handler(this::getReferences);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected void getAll(RoutingContext routingContext)
    {
        try
        {
            var items = WidgetConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                item.put(APIConstants.Entity.DASHBOARD.getName(), getDashboards(item.getLong(ID)));
            }

            super.send(routingContext, new JsonObject().put(APIConstants.RESPONSE_CODE, HttpStatus.SC_OK)
                    .put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED).put(GlobalConstants.RESULT, items));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected Future<Void> afterCreate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_WIDGET.name()).put(EventBusConstants.EVENT_CONTEXT, entity));

        WidgetConfigStore.getStore().assign(entity.getLong(ID));

        ObjectCacheStore.getStore().assign(entity.getLong(ID));

        return Future.succeededFuture();
    }

    //after successfully dashboard create and if dashboard is default than this dashboard id store in user preference
    @Override
    protected Future<JsonObject> afterUpdate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_WIDGET.name()).put(EventBusConstants.EVENT_CONTEXT, entity));

        WidgetConfigStore.getStore().update(entity.getLong(ID)).onComplete(voidAsyncResult -> ObjectCacheStore.getStore().update(entity.getLong(ID)));

        return Future.succeededFuture();
    }

    @Override
    protected Future<Void> afterDelete(JsonObject response, RoutingContext routingContext)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_WIDGET.name()).put(EventBusConstants.EVENT_CONTEXT, response));

        WidgetConfigStore.getStore().deleteItem(response.getLong(ID));

        ObjectCacheStore.getStore().deleteItem(response.getLong(ID));

        return super.afterDelete(response, routingContext);
    }

    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(ID));

            var dashboards = getDashboards(id);

            var items = TemplateConfigStore.getStore().getItems();

            var templates = new JsonArray();

            // in case of widget created in custom tab, we need to check for the reference for template
            for (var index = 0; index < items.size(); index++)
            {
                var widgets = items.getJsonObject(index).getJsonArray("template.widgets", new JsonArray());

                if (!widgets.isEmpty())
                {
                    for (var widget = 0; widget < widgets.size(); widget++)
                    {
                        var item = widgets.getJsonObject(widget);

                        if (item != null && !item.isEmpty() && item.containsKey(ID) && item.getLong(ID).equals(id))
                        {
                            templates.add(items.getJsonObject(index));

                            break;
                        }
                    }
                }
            }

            if (!dashboards.isEmpty())
            {
                response.put(APIConstants.Entity.DASHBOARD.getName(), dashboards);
            }

            if (!templates.isEmpty())
            {
                response.put(APIConstants.Entity.TEMPLATE.getName(), templates);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            APIUtil.sendResponse(exception, routingContext);
        }

        return Future.succeededFuture(response);
    }

    /**
     * Retrieves usage details for a widget across dashboards.
     * <p>
     * This method iterates through all dashboards and checks if the specified widget ID
     * is present in any dashboard's widget list. If found, it collects relevant dashboard
     * details such as ID, name, category, access type, and field type, and adds them to
     * the result array.
     *
     * @param widgetId the ID of the widget to search for
     * @return a JsonObject containing a "dashboards" array with usage details
     */
    private static JsonArray getDashboards(Long widgetId)
    {
        var dashboards = new JsonArray();

        try
        {
            var ids = DashboardConfigStore.getStore().getIds();

            // Check dashboard usage
            for (var index = 0; index < ids.size(); index++)
            {
                var item = DashboardConfigStore.getStore().getItem(ids.getLong(index));

                var context = item.getJsonObject(DASHBOARD_CONTEXT);

                if (context != null)
                {
                    var widgets = context.getJsonArray(DASHBOARD_WIDGETS);

                    for (var i = 0; i < widgets.size(); i++)
                    {
                        var widget = widgets.getJsonObject(i);

                        if (widget != null && !widget.isEmpty() && widget.containsKey(ID) && widget.getLong(ID).equals(widgetId))
                        {
                            // Add dashboard usage info with relevant details
                            dashboards.add(new JsonObject().put(ID, item.getLong(ID))
                                    .put(DASHBOARD_NAME, item.getString(DASHBOARD_NAME))
                                    .put(DASHBOARD_CATEGORY, item.getString(DASHBOARD_CATEGORY, EMPTY_VALUE))
                                    .put(DASHBOARD_ACCESS_TYPE, item.getString(DASHBOARD_ACCESS_TYPE, EMPTY_VALUE))
                                    .put(FIELD_TYPE, item.getString(FIELD_TYPE, EMPTY_VALUE)));

                            break;
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return dashboards;
    }
}
