/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *	18-Feb-2025		Sankalp		        MOTADATA-5203: Stack template name - In case of stack devices routing protocol (L3) will not be appended in template name
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 *  11-Jun-2025		Jenil <PERSON>	Added condition while rendering instance tab to prevent attachment of unwanted tabs in categories other than server.
 *  23-Jun-2025     Darshan Parmar      MOTADATA-6583 : Container Orchestration Category support added
 *  10-Jul-2025     Darshan Parmar      MOTADATA-6186 : ruckus smartzone template
 *  14-Jul-2025     <PERSON>riya<PERSON>h Sindhav    MOTADATA-6348 : Enable Manual Mapping and Custom Management of Monitor Templates in Motadata AIOps
 *  06-Aug-2025	    Sankalp	            MOTADATA-6677 : Different template support for database app and objects


 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.*;

import static com.mindarray.ErrorMessageConstants.INTERNAL_SERVER_EXCEPTION;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.AIOpsObject.OBJECT_CONTEXT;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.User.USER_ID;
import static com.mindarray.nms.NMSConstants.CONTROLLER_MODEL;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;


public class Template extends AbstractAPI
{
    public static final String TEMPLATE_NAME = "template.name";
    public static final String TEMPLATE_TABS = "template.tabs";
    public static final String TEMPLATE_TAB_ID = "tab.id";
    public static final String TEMPLATE_TEXT = "text";
    public static final String TEMPLATE_PARENT_ID = "template.parent.id";
    public static final String TEMPLATE_WIDGETS = "template.widgets"; // dashboard.context

    private static final Logger LOGGER = new Logger(Template.class, MOTADATA_API, "Template API");

    public Template()
    {
        super("templates", TemplateConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router, Set.of(REQUEST_GET, REQUEST_CREATE));

            router.get("/" + endpoint).handler(this::getAll);

            router.post("/" + endpoint + "/clone").handler(this::validate).handler(this::clone);

            router.get("/" + endpoint + "/resolve").handler(this::resolve);

            router.get("/" + endpoint + "/:id").handler(this::validate).handler(this::get);

            router.post("/" + endpoint).handler(this::validate).handler(this::create);

            router.put("/" + endpoint + "/:id/assign").handler(this::assign);

            router.get("/" + endpoint + "/:id/references").handler(this::getReferences);


        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /*
     *  this method used to get template tabs of applications if any application has been provisioned on SERVER.
     *  and event templates if entity qualified as event.source of any log, flow or trap.
     * */
    private JsonArray getTemplateTabs(JsonObject item)
    {
        var templateTabs = new JsonArray();

        var tabIds = new HashSet<Long>();

        if (item.getString(AIOpsObject.OBJECT_IP) != null)
        {
        var entity = EventSourceConfigStore.getStore().getItemByHost(item.getString(AIOpsObject.OBJECT_IP), false);

        if (entity != null && entity.containsKey(EventBusConstants.EVENT_TYPE) && !entity.getJsonArray(EventBusConstants.EVENT_TYPE).isEmpty())
        {
            var eventTypes = entity.getJsonArray(EventBusConstants.EVENT_TYPE);

            if (eventTypes.contains(EventBusConstants.EVENT_LOG) && (DBConstants.ENTITY_TYPE_USER.equalsIgnoreCase(entity.getString(DBConstants.FIELD_TYPE)) || (entity.getJsonArray(LogEngineConstants.EVENT_CATEGORY) != null && !entity.getJsonArray(LogEngineConstants.EVENT_CATEGORY).isEmpty())))
            {
                templateTabs.add(new JsonObject().put(TEMPLATE_TEXT, VisualizationConstants.Template.LOG_ANALYTICS.getName()).put(TEMPLATE_TAB_ID, CommonUtil.getLong(VisualizationConstants.Template.getTemplate(VisualizationConstants.Template.LOG_ANALYTICS.getName()))));
            }

            if (eventTypes.contains(EventBusConstants.EVENT_TRAP))
            {
                templateTabs.add(new JsonObject().put(TEMPLATE_TEXT, VisualizationConstants.Template.TRAP_ANALYTICS.getName()).put(TEMPLATE_TAB_ID, CommonUtil.getLong(VisualizationConstants.Template.getTemplate(VisualizationConstants.Template.TRAP_ANALYTICS.getName()))));
            }

            if (eventTypes.contains(EventBusConstants.EVENT_FLOW))
            {
                var template = TemplateConfigStore.getStore().getItem(CommonUtil.getLong(VisualizationConstants.Template.getTemplate(VisualizationConstants.Template.FLOW_ANALYTICS.getName())));

                var tab = new JsonObject().put(TEMPLATE_TEXT, VisualizationConstants.Template.FLOW_ANALYTICS.getName()).put(TEMPLATE_TAB_ID, template.getLong(ID));

                tab.put(TEMPLATE_TABS, getTabs(template));   // adding child templates

                templateTabs.add(tab);
                }
            }
        }

        if (item.containsKey(AIOpsObject.OBJECT_CATEGORY) &&
                item.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVER.getName()))
        {

            var metricTypes = new JsonArray();

            for (var metric : MetricConfigStore.getStore().getItemsByObject(item.getLong(ID)))
            {
                var metricType = metric.getString(Metric.METRIC_TYPE);

                var metricName = metric.getString(Metric.METRIC_NAME);

                // Handle metric type templates

                if (!metricTypes.contains(metricType) && NMSConstants.APPLICATION_TYPES.contains(metricType))
                {
                    if (NMSConstants.getCategory(NMSConstants.Type.valueOfName(metricType)).getName().equalsIgnoreCase(NMSConstants.Category.DATABASE.getName()))
                    {
                        addTemplateTab(metricType + " " + NMSConstants.Category.DATABASE.getName(), templateTabs, tabIds);
                    }
                    else
                    {
                        addTemplateTab(metricType, templateTabs, tabIds);
                    }
                    metricTypes.add(metricType);
                }

                // Handle metric name templates

                var tabId = VisualizationConstants.Template.getTemplate(metricName);

                if (!metricTypes.contains(metricType) && !tabIds.contains(tabId)
                        && tabId != VisualizationConstants.DEFAULT_NETWORK_TEMPLATE
                        && !metricName.equalsIgnoreCase(item.getString(AIOpsObject.OBJECT_TYPE)))
                {
                    addTemplateTab(metricName, templateTabs, tabIds);

                    metricTypes.add(metricName);
                }
            }
        }
        return templateTabs;
    }

    // --- Helper Method ---
    private void addTemplateTab(String type, JsonArray templateTabs, Set<Long> tabIds)
    {
        var tab = new JsonObject();

        var tabId = VisualizationConstants.Template.getTemplate(type);

        var appTabs = getTabs(TemplateConfigStore.getStore().getItem(tabId));

        // Remove redundant child tab if same as parent
        if (appTabs.size() == 1 && appTabs.getJsonObject(0).getLong(TEMPLATE_TAB_ID).equals(tabId))
        {
            appTabs.remove(0);
        }

        tabIds.add(tabId);

        templateTabs.add(tab.put(TEMPLATE_TEXT, type).put(TEMPLATE_TAB_ID, tabId).put(TEMPLATE_TABS, appTabs));
    }


    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        return Future.succeededFuture();
    }

    protected void clone(RoutingContext routingContext)
    {
        var context = routingContext.body().asJsonObject();

        var id = context.getLong(VisualizationConstants.TEMPLATE_ID);

        try
        {
            LOGGER.info("Initiating cloning process for template with ID" + id);

            var item = TemplateConfigStore.getStore().getItem(id);

            if(item != null)
            {
                item.remove(ID);

                item.put(TEMPLATE_NAME, context.getString(TEMPLATE_NAME));

                LOGGER.info("Successfully cloned the template with the name: Copy of " + context.getString(TEMPLATE_NAME));

                cloneWidgets(item).compose(v -> saveTemplate(item)).compose(parentTemplateId ->
                        {
                            // Clone child templates
                            var items = TemplateConfigStore.getStore().getItemsByValue(TEMPLATE_PARENT_ID, id);

                            var widgetFutures = new ArrayList<Future<Void>>();

                            for (int i = 0; i < items.size(); i++)
                            {
                                widgetFutures.add(cloneWidgets(items.getJsonObject(i).put(TEMPLATE_PARENT_ID, parentTemplateId)));
                            }

                            return Future.all(widgetFutures).compose(widgetCloneResult ->
                            {

                                Future<Long> future = Future.succeededFuture();

                                var sortedTabs = items.stream()
                                        .map(obj -> (JsonObject) obj)
                                        .sorted(Comparator.comparingLong(obj -> obj.getLong(ID)))
                                        .toList();

                                for (var template : sortedTabs)
                                {
                                    template.remove(ID);

                                    template.put(TEMPLATE_NAME, "Copy of " + template.getString(TEMPLATE_NAME));

                                    future = future.compose(v -> saveTemplate(template));
                                }

                                // Finally, return the parentTemplateId after all are done
                                return future.compose(v -> Future.succeededFuture(parentTemplateId));

                            });
                        })
                        .onSuccess(parentTemplateId ->
                        {
                            LOGGER.info("Template cloned successfully");

                            this.send(routingContext, new JsonObject()
                                    .put(RESPONSE_CODE, HttpStatus.SC_OK)
                                    .put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                                    .put(MESSAGE, "Template cloned successfully")
                                    .put(RESULT, parentTemplateId)
                            );
                        })
                        .onFailure(error ->
                        {
                            LOGGER.error(error);

                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, error.getCause().getMessage()))
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                    .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(error.getCause().getStackTrace())));
                        });
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_FAIL)
                        .put(MESSAGE, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, id))
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND));
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_FAIL)
                    .put(MESSAGE, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, id))
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND));
        }
    }

    /**
     * REST API endpoint for template inventory management.*
     * returns all the templates with parent id as default id
     *
     * @param routingContext The routing context containing the request parameters
     */
    protected void getAll(RoutingContext routingContext)
    {
        try
        {
            if (routingContext.request().params().contains(FILTER))
            {
                var requestParameters = JsonObject.mapFrom(Json.decodeValue(routingContext.request().getParam(FILTER)));

                if (requestParameters != null && requestParameters.containsKey(KEY) && requestParameters.containsKey(VALUE))
                {
                    filter(routingContext, requestParameters);
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST)
                            .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                            .put(GlobalConstants.MESSAGE, ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS));
                }
            }
            else
            {
                this.beforeGetAll(routingContext).compose(handler ->

                        Future.<JsonArray>future(promise ->
                                this.configStore.getReferenceCountsByItem().onComplete(result ->
                                {
                                    if (result.succeeded())
                                    {
                                        this.getEntityCountPreHook(result.result().put(USER_ID, routingContext.user().principal().getLong(ID))).onComplete(asyncResult ->
                                        {
                                            if (asyncResult.succeeded())
                                            {
                                                var items = this.configStore.getItemsByValue(TEMPLATE_PARENT_ID, DEFAULT_TEMPLATE_ID);

                                                var entities = new JsonArray();

                                                for (var index = 0; index < items.size(); index++)
                                                {
                                                    var item = CommonUtil.removeSensitiveFields(items.getJsonObject(index), true);

                                                    if (asyncResult.result() != null && asyncResult.result().containsKey(CommonUtil.getString(item.getLong(ID))))
                                                    {
                                                        item.put(ENTITY_PROPERTY_COUNT, asyncResult.result().getInteger(CommonUtil.getString(item.getLong(ID))));
                                                    }

                                                    entities.add(item);
                                                }

                                                promise.complete(entities);
                                            } else
                                            {
                                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                                        .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, asyncResult.cause().getMessage()))
                                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                        .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(asyncResult.cause().getStackTrace())));

                                                promise.fail(asyncResult.cause());
                                            }
                                        });
                                    } else
                                    {
                                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                                .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, result.cause().getMessage()))
                                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));

                                        promise.fail(result.cause());
                                    }
                                })).compose(entities -> this.afterGetAll(entities, routingContext)));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void assign(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var id = CommonUtil.getLong(routingContext.request().getParam(ID));

                var item = this.configStore.getItem(id);

                if (item != null)
                {
                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_CHANGE_LOCAL_NOTIFICATION,
                            item.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ASSIGN_TEMPLATE)
                                    .put(AIOpsObject.OBJECT_IP, item.getString(EventBusConstants.EVENT_SOURCE))
                                    .put(NMSConstants.OBJECTS, routingContext.body().asJsonObject().getJsonArray(APIConstants.REQUEST_PARAM_IDS)));

                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                            .put(GlobalConstants.STATUS, STATUS_SUCCEED)
                            .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ASSIGN_SUCCEEDED, schema.getString(APIConstants.ENTITY_NAME)))
                            .put(ID, id));
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST)
                            .put(GlobalConstants.STATUS, STATUS_FAIL)
                            .put(MESSAGE, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, schema.getString(APIConstants.ENTITY_NAME)))
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND));
                }
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                    .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
        }
    }

    /**
     * Helper method to clone template widgets with proper async handling.
     *
     * @param template The template being cloned
     *
     */
    private Future<Void> cloneWidgets(JsonObject template)
    {
        Promise<Void> promise = Promise.promise();

        try
        {
            var widgets = template.getJsonArray(TEMPLATE_WIDGETS,new JsonArray());

            if(!widgets.isEmpty())
            {
                var futures = new ArrayList<Future<Void>>();

                LOGGER.info("Initiating to clone " + widgets.size() + " widgets");

                for (var widgetId = 0; widgetId < widgets.size(); widgetId++)
                {
                    var item = WidgetConfigStore.getStore().getItem(widgets.getJsonObject(widgetId).getLong(ID));

                    if(item != null)
                    {
                        Promise<Void> widgetPromise = Promise.promise();

                        item.remove(ID);

                        item.put(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_USER)
                                .put(VisualizationConstants.VISUALIZATION_NAME, String.format("Copy of %s %s", template.getString(TEMPLATE_NAME) , item.getString(VisualizationConstants.VISUALIZATION_NAME)));

                        Bootstrap.configDBService().save(DBConstants.TBL_WIDGET, item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                WidgetConfigStore.getStore().addItem(asyncResult.result()).onComplete(handler ->
                                {
                                    if (handler.succeeded())
                                    {
                                        item.put(ID, asyncResult.result());

                                        widgetPromise.complete();
                                    }
                                    else
                                    {
                                        LOGGER.error(asyncResult.cause());

                                        widgetPromise.fail(handler.cause());
                                    }
                                });
                            }
                            else
                            {
                                LOGGER.error(asyncResult.cause());

                                widgetPromise.fail(asyncResult.cause());
                            }
                        });

                        futures.add(widgetPromise.future());
                    }
                    else
                    {
                        LOGGER.warn(String.format("widget not found for %d" , widgets.getJsonObject(widgetId).getLong(ID)));
                    }
                }

                Future.all(futures).onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        LOGGER.info("All widget clones completed successfully, saving template");

                        promise.complete(null);
                    }
                    else
                    {
                        LOGGER.warn("Some widget clones failed: " + result.cause().getMessage());

                        promise.fail("Failed to clone some widgets: " + result.cause().getMessage());
                    }
                });

                return promise.future();
            }
            else
            {
                promise.complete();

                return Future.succeededFuture();
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage()));

            return promise.future();
        }
    }

    /**
     * Helper method to save the cloned template to the database.
     *
     * @param template The template to save
     *
     */
    private Future<Long> saveTemplate(JsonObject template)
    {

    template.put(DBConstants.FIELD_TYPE,DBConstants.ENTITY_TYPE_USER);

    var promise = Promise.<Long>promise();

    Bootstrap.configDBService().save(DBConstants.TBL_TEMPLATE, template,
            GlobalConstants.DEFAULT_USER, GlobalConstants.SYSTEM_REMOTE_ADDRESS,
            result ->
            {
                if (result.succeeded())
                {
                    var newTemplateId = result.result();

                    LOGGER.info("Successfully saved cloned template with ID: " + newTemplateId);

                    // Add to TemplateConfigStore
                    TemplateConfigStore.getStore().addItem(newTemplateId).onComplete(asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            promise.complete(newTemplateId);
                        }
                        else
                        {
                            LOGGER.warn("Failed to add cloned template to store: " + asyncResult.cause().getMessage());

                            promise.fail(asyncResult.cause());
                        }
                    });
                }
                else
                {
                    LOGGER.warn("Failed to save cloned template to database: " + result.cause().getMessage());

                    promise.fail(result.cause());
                }
            });

    return promise.future();
}

    protected void resolve(RoutingContext routingContext)
    {
        try
        {
            var template = new JsonObject();

            var context = JsonObject.mapFrom(Json.decodeValue(routingContext.request().getParam(FILTER)));

            var item = ObjectConfigStore.getStore().getItem(context.getLong(ENTITY_ID));

            var objectType = item.getString(AIOpsObject.OBJECT_TYPE);

            if (context.containsKey(NMSConstants.APPLICATION))
            {
                template = this.configStore.getItem(VisualizationConstants.Template.getTemplate(context.getString(NMSConstants.APPLICATION)));
            }
            else if (context.containsKey(NMSConstants.Type.ACCESS_POINT.getName()))
            {
                if (objectType.equalsIgnoreCase(NMSConstants.Type.CISCO_WIRELESS.getName()))
                {
                    template = this.configStore.getItem(VisualizationConstants.Template.getTemplate(VisualizationConstants.Template.CISCO_WIRELESS_ACCESS_POINT.getName()));
                }
                else if (objectType.equalsIgnoreCase(NMSConstants.Type.ARUBA_WIRELESS.getName()))
                {
                    template = this.configStore.getItem(VisualizationConstants.Template.getTemplate(VisualizationConstants.Template.ARUBA_WIRELESS_ACCESS_POINT.getName()));
                }
                else
                {
                    if (item.containsKey(OBJECT_CONTEXT) && item.getJsonObject(OBJECT_CONTEXT).getString(CONTROLLER_MODEL).equalsIgnoreCase("SmartZone"))
                    {
                        template = this.configStore.getItem(VisualizationConstants.Template.getTemplate(VisualizationConstants.Template.RUCKUS_WIRELESS_SMARTZONE_ACCESS_POINT.getName()));
                    }
                    else
                    {
                        template = this.configStore.getItem(VisualizationConstants.Template.getTemplate(VisualizationConstants.Template.RUCKUS_WIRELESS_ACCESS_POINT.getName()));
                    }
                }
            }
            else if (context.containsKey(NMSConstants.Type.VIRTUAL_MACHINE.getName()))
            {
                if (objectType.equalsIgnoreCase(NMSConstants.Type.VMWARE_ESXI.getName()))
                {
                    template = this.configStore.getItem(VisualizationConstants.Template.getTemplate(VisualizationConstants.Template.VMWARE_ESXI_VM.getName()));
                }
                else if (objectType.equalsIgnoreCase(NMSConstants.Type.CITRIX_XEN.getName()))
                {
                    template = this.configStore.getItem(VisualizationConstants.Template.getTemplate(VisualizationConstants.Template.CITRIX_XEN_VM.getName()));
                }
                else if (objectType.equalsIgnoreCase(NMSConstants.Type.NUTANIX.getName()))
                {
                    template = this.configStore.getItem(VisualizationConstants.Template.getTemplate(VisualizationConstants.Template.NUTANIX_VM.getName()));
                }
                else
                {
                    template = this.configStore.getItem(VisualizationConstants.Template.getTemplate(VisualizationConstants.Template.HYPER_V_VM.getName()));
                }
            }
            else
            {
                template = this.configStore.getItem(item.getLong(VisualizationConstants.TEMPLATE_ID));
            }

            send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT,
                    new JsonObject().put(VisualizationConstants.TEMPLATE_ID,template.getLong(ID))
                    .put(TEMPLATE_TABS,getTemplateTabs(item))));

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                    .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
        }

    }

    private JsonArray getTabs(JsonObject template)
    {
        var tabs = new JsonArray();

        tabs.add(new JsonObject().put(TEMPLATE_TAB_ID, template.getLong(ID)).put(TEMPLATE_TEXT, "Overview"));

        var sortedTabs = this.configStore.getItemsByValue(TEMPLATE_PARENT_ID, Objects.equals(template.getLong(TEMPLATE_PARENT_ID), DEFAULT_TEMPLATE_ID) ? template.getLong(ID):template.getLong(TEMPLATE_PARENT_ID)).stream()
                .map(obj -> (JsonObject) obj)
                .sorted(Comparator.comparingLong(obj -> obj.getLong(ID)))
                .toList();

        for (int i = 0; i < sortedTabs.size(); i++)
        {
            var tab = sortedTabs.get(i);

            tabs.add( new JsonObject().put(TEMPLATE_TAB_ID, tab.getLong(ID)).put(TEMPLATE_TEXT, tab.getString(TEMPLATE_TEXT)!=null ? tab.getString(TEMPLATE_TEXT) :tab.getString(TEMPLATE_NAME)).put(DBConstants.FIELD_TYPE,tab.getString(DBConstants.FIELD_TYPE)));

        }

        return tabs;
    }

    @Override
    protected Future<Void> afterGet(JsonObject template, RoutingContext routingContext)
    {
        try
        {
            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, template.put(TEMPLATE_TABS,getTabs(template)).put(ENTITIES,ObjectConfigStore.getStore().getItemsByValue(VisualizationConstants.TEMPLATE_ID,template.getLong(ID)))));
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }

        return Future.succeededFuture();
    }

    @Override
    protected void getReferences(RoutingContext routingContext)
    {
        try
        {
            this.configStore.getReferenceEntities(CommonUtil.getLong(routingContext.request().getParam(ID))).onComplete(result ->
            {
                if (result.succeeded())
                {
                    this.getReferencesPreHook(routingContext, result.result()).onComplete(asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, asyncResult.result()));
                        }
                        else
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, asyncResult.cause().getMessage()))
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                    .put(ERROR, CommonUtil.formatStackTrace(asyncResult.cause().getStackTrace())));
                        }
                    });
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                            .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, result.cause().getMessage()))
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                            .put(ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));
                }
            });
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }


    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        try
        {
            if (response != null && !response.isEmpty())
            {
                var entities = response.getJsonArray(Entity.OBJECT.getName());

                for (var index = 0; index < entities.size(); index++)
                {
                    var object = entities.getJsonObject(index);

                    NMSConstants.enrich(object);

                    CommonUtil.removeSensitiveFields(object,true);

                    object.mergeIn(object);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture(response);
    }
}