/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 * Change Logs:
 *  Date			Author			         Notes
 *  28-Feb-2025     Smit Prajapati           MOTADATA-4956: Rule Based Tagging
 *  4-Mar-2025      Bharat                   MOTADATA-4740: Two factor authentication 2FA
 *  2-Apr-2025      Bharat                   MOTADATA-5637: Domain Mapping in Flow
 *  20-Feb-2025     Pruthviraj               MOTADATA-5285: NetRoute and NetRoute policy init added
 *  22-Apr-2025     Bharat                   MOTADATA-5822: Metric Explorer Enhancements
 *  24-Jun-2025     <PERSON><PERSON>A-6528 : added Explorer API entry & removed Metric Explorer entry
 *  23-Jul-2025	    <PERSON><PERSON><PERSON>A-6236: DNSServer init added.
 * */

package com.mindarray.api;

import com.mindarray.util.Logger;
import io.vertx.ext.web.Router;

import static com.mindarray.GlobalConstants.MOTADATA_API;

class APIRouter
{
    private static final Logger LOGGER = new Logger(APIRouter.class, MOTADATA_API, "API Router");

    private APIRouter()
    {
    }

    public static void init(Router settingsAPIRouter, Router miscRouter, Router visualizationRouter, Router systemRouter, Router queryRouter)
    {
        try
        {
            new User().init(settingsAPIRouter);

            new MyAccount().init(settingsAPIRouter);

            new License().init(settingsAPIRouter);

            new UserRole().init(settingsAPIRouter);

            new Group().init(settingsAPIRouter);

            new PasswordPolicy().init(settingsAPIRouter);

            new Discovery().init(settingsAPIRouter);

            new CredentialProfile().init(settingsAPIRouter);

            new LDAPServer().init(settingsAPIRouter);

            new SingleSignOn().init(settingsAPIRouter);

            new RemoteEventProcessor().init(settingsAPIRouter);

            new Agent().init(settingsAPIRouter);

            new SNMPDeviceCatalog().init(settingsAPIRouter);

            new SNMPOIDGroup().init(settingsAPIRouter);

            new AIOpsObject().init(settingsAPIRouter);

            new Metric().init(settingsAPIRouter);

            new ApplicationMapper().init(settingsAPIRouter);

            new ProtocolMapper().init(settingsAPIRouter);

            new MailServerConfiguration().init(settingsAPIRouter);

            new Integration().init(settingsAPIRouter);

            new SystemProcess().init(settingsAPIRouter);

            new SystemService().init(settingsAPIRouter);

            new BusinessHour().init(settingsAPIRouter);

            new MetricPlugin().init(settingsAPIRouter);

            new SNMPTrapProfile().init(settingsAPIRouter);

            new SNMPTrapForwarder().init(settingsAPIRouter);

            new SNMPTrapListenerConfiguration().init(settingsAPIRouter);

            new SMSGatewayConfiguration().init(settingsAPIRouter);

            new CustomMonitoringField().init(settingsAPIRouter);

            new SystemFile().init(settingsAPIRouter);

            new Scheduler().init(settingsAPIRouter);

            new RunbookPlugin().init(settingsAPIRouter);

            new Rebranding().init(settingsAPIRouter);

            new Misc().init(miscRouter);

            new TopologyPlugin().init(settingsAPIRouter);

            new MACScanner().init(settingsAPIRouter);

            new ProxyServer().init(settingsAPIRouter);

            new DependencyMapper().init(settingsAPIRouter);

            new FlowSettings().init(settingsAPIRouter);

            new LogCollector().init(settingsAPIRouter);

            new FlowSamplingRate().init(settingsAPIRouter);

            new Tag().init(settingsAPIRouter);

            new FlowIPGroup().init(settingsAPIRouter);

            new LogParser().init(settingsAPIRouter);

            new LogParserPlugin().init(settingsAPIRouter);

            new EventSource().init(settingsAPIRouter);

            new MetricPolicy().init(settingsAPIRouter);

            new EventPolicy().init(settingsAPIRouter);

            new Dashboard().init(visualizationRouter);

            new Widget().init(visualizationRouter);

            new Explorer().init(visualizationRouter);

            new Template().init(visualizationRouter);

            new Report().init(visualizationRouter);

            new DataRetentionPolicy().init(settingsAPIRouter);

            new ConfigTemplate().init(settingsAPIRouter);

            new StorageProfile().init(settingsAPIRouter);

            new Configuration().init(settingsAPIRouter);

            new BackupProfile().init(settingsAPIRouter);

            new MotadataApp().init(systemRouter);

            new PersonalAccessToken().init(settingsAPIRouter);

            new Query().init(queryRouter);

            new LogForwarder().init(settingsAPIRouter);

            new FlowASMapper().init(settingsAPIRouter);

            new FlowDomainMapper().init(settingsAPIRouter);

            new FlowGeolocationMapper().init(settingsAPIRouter);

            new FlowIPMapper().init(settingsAPIRouter);

            new IntegrationProfile().init(settingsAPIRouter);

            new ComplianceRule().init(settingsAPIRouter);

            new ComplianceBenchmark().init(settingsAPIRouter);

            new CompliancePolicy().init(settingsAPIRouter);

            new TwoFactorAuthentication().init(settingsAPIRouter);

            new TagRule().init(settingsAPIRouter);

            new NetRoute().init(settingsAPIRouter);

            new NetRoutePolicy().init(settingsAPIRouter);

            new DNSServerProfile().init(settingsAPIRouter);

            new SLOProfile().init(settingsAPIRouter);

            new SLOCycle().init(settingsAPIRouter);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
