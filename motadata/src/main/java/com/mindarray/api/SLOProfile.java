package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.SLOProfileConfigStore;
import com.mindarray.store.TagConfigStore;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;

public class SLOProfile extends AbstractAPI
{
    private static final Logger LOGGER = new Logger(SLOProfile.class, GlobalConstants.MOTADATA_API, "SLO Profile API");

    public SLOProfile()
    {
        super("slo-profiles", SLOProfileConfigStore.getStore(), LOGGER);
    }

    public static final String SLO_PROFILE_NAME = "slo.profile.name";
    public static final String SLO_PROFILE_STATE = "slo.profile.state"; // Yes/No
    public static final String SLO_CYCLE_ID = "slo.cycle.id"; // FK to SLOCycleConfigStore
    public static final String SLO_PROFILE_START_TIME = "slo.profile.start.time"; // Epoch seconds of the starting of the day (ex. for 8-8-2025 it will be **********)
    public static final String SLO_PROFILE_CONTEXT = "slo.profile.context";
    public static final String SLO_PROFILE_TYPE = "slo.profile.type"; // Availability/Performance
    public static final String SLO_PROFILE_TAGS = "slo.profile.tags"; // Tags for the SLO profile
    public static final String SLO_PROFILE_BUSINESS_SERVICE_NAME = "slo.profile.business.service.name"; // Business service name for the SLO profile
    public static final String SLO_NOTIFICATION_CONTEXT = "slo.notification.context";

    // keys inside slo.profile.context
    public static final String SLO_FREQUENCY = "slo.frequency"; // Daily/Weekly/Monthly/Quarterly
    public static final String SLO_SEVERITY = "slo.severity"; // Critical/Down
    public static final String SLO_INSTANCE = "slo.instance"; // monitor, interface, vm, ap, link, container, process, service
    public static final String SLO_TARGET = "slo.target";
    public static final String SLO_WARNING = "slo.warning";

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            var requestBody = routingContext.body().asJsonObject();

            requestBody
                    .put(GlobalConstants.ID, SLOProfileConfigStore.getStore().newId())
                    .put(SLO_PROFILE_STATE, GlobalConstants.YES);

            if (requestBody.containsKey(SLO_PROFILE_TAGS) && requestBody.getJsonArray(SLO_PROFILE_TAGS) != null && !requestBody.getJsonArray(SLO_PROFILE_TAGS).isEmpty())
            {
                requestBody.put(SLO_PROFILE_TAGS, TagConfigStore.getStore().addItems(requestBody.getJsonArray(SLO_PROFILE_TAGS), Tag.TagType.SLO.getName(), DBConstants.ENTITY_TYPE_USER));
            }

            promise.complete(requestBody);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        var requestBody = routingContext.body().asJsonObject();

        if (requestBody.containsKey(SLO_PROFILE_TAGS) && requestBody.getJsonArray(SLO_PROFILE_TAGS) != null && !requestBody.getJsonArray(SLO_PROFILE_TAGS).isEmpty())
        {
            requestBody.put(SLO_PROFILE_TAGS, TagConfigStore.getStore().addItems(requestBody.getJsonArray(SLO_PROFILE_TAGS), Tag.TagType.SLO.getName(), DBConstants.ENTITY_TYPE_USER));
        }

        return Future.succeededFuture(requestBody);
    }

    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_SLO_PROFILE).put(ID, entity.getLong(ID)));

        return super.afterDelete(entity, routingContext);
    }

    @Override
    protected Future<Void> afterGetAll(JsonArray items, RoutingContext routingContext)
    {
        try
        {
            var entities = new JsonArray();

            for (var i = 0; i < items.size(); i++)
            {
                var item = items.getJsonObject(i);

                if (item.containsKey(SLO_PROFILE_TAGS) && item.getJsonArray(SLO_PROFILE_TAGS) != null && !item.getJsonArray(SLO_PROFILE_TAGS).isEmpty())
                {
                    item.put(SLO_PROFILE_TAGS, TagConfigStore.getStore().getItems(item.getJsonArray(SLO_PROFILE_TAGS), Tag.TagType.SLO, DBConstants.ENTITY_TYPE_USER));
                }

                entities.add(item);
            }

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, entities));
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }

        return Future.succeededFuture();
    }

    @Override
    protected Future<Void> afterGet(JsonObject item, RoutingContext routingContext)
    {
        try
        {
            if (item.containsKey(SLO_PROFILE_TAGS) && item.getJsonArray(SLO_PROFILE_TAGS) != null && !item.getJsonArray(SLO_PROFILE_TAGS).isEmpty())
            {
                item.put(SLO_PROFILE_TAGS, TagConfigStore.getStore().getItems(item.getJsonArray(SLO_PROFILE_TAGS), Tag.TagType.SLO, DBConstants.ENTITY_TYPE_USER));
            }

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, item));
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }

        return Future.succeededFuture();
    }

    @Override
    protected void getAll(RoutingContext routingContext)
    {
        try
        {
            if (routingContext.request().params().contains(FILTER))
            {
                var requestParameters = JsonObject.mapFrom(Json.decodeValue(routingContext.request().getParam(FILTER)));

                if (requestParameters.containsKey("active") && requestParameters.getString("active").equalsIgnoreCase(YES))
                {
                    this.send(routingContext, new JsonObject()
                            .put(APIConstants.RESPONSE_CODE, HttpStatus.SC_OK)
                            .put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                            .put(GlobalConstants.RESULT, SLOProfileConfigStore.getStore().getActiveSLOProfiles()));
                }
                else
                {
                    super.getAll(routingContext);
                }
            }
            else
            {
                super.getAll(routingContext);
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }
}
