/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
Change Logs
 *   Date          Author              Notes
 *  2025-03-18     <PERSON><PERSON>        Added .latency as data category for Float.
 *  2025-04-01     <PERSON><PERSON>        Added latency as data category for Float.
 *  24-Feb-2025	   Pruthviraj Jadeja   NetRoute related new plugin id added
 *  05-Jun-2025	   Vismit              Motadata-6451 Added replace method to replace reserved separators with empty value
 *  20-Jun-2025	   Sankalp		       MOTADATA-5513 : Removed started.time.seconds from default data type string
 *  29-Jul-2025    Umang               MOTADATA-6661 : Convert every bytes.per.sec into bits.per.sec for metrics
 */

package com.mindarray.datastore;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.RemoteEventProcessorConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.lang3.math.NumberUtils;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.*;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE;
import static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.visualization.VisualizationConstants.VisualizationGrouping.EVENT_SOURCE;

public class DatastoreConstants
{
    //datastore const ..

    public static final Set<String> floatingColumns = new HashSet<>();
    public static final String METRIC_COLUMNS = "metric-columns";
    public static final String EVENT_COLUMNS = "event-columns";
    public static final String INDEXABLE_COLUMNS = "indexable-columns";
    public static final String EVENT_CATEGORIES = "event-categories";
    public static final String FLOATING_COLUMNS = "floating-columns";
    public static final String MAPPER = "mapper";
    public static final String MAPPER_PLUGIN_IDS = "mapper.plugin.ids";
    public static final String MAPPER_DATA_CATEGORIES = "mapper.data.categories";
    public static final String MAPPER_INSTANCE = "mapper.instance";
    public static final String MAPPER_CORRELATED = "mapper.correlated";
    public static final String MAPPER_SHADOW_COUNTER = "mapper.shadow.counter";
    public static final String MAPPER_EVENT_CATEGORY = "mapper.event.category";
    public static final String MAPPER_DURATION = "mapper.duration";
    public static final String MAPPER_STATUS = "mapper.status";
    public static final String MAPPER_GROUP = "mapper.group";
    public static final String DATASTORE_CATEGORY = "datastore.category";
    public static final String DATASTORE_FORMAT = "datastore.format";
    public static final String SHADOW_COUNTERS = "shadow-counters";
    public static final String DATASTORE_TYPE = "datastore.type";
    public static final String OPERATION_TYPE = "operation.type";
    public static final String BATCHES = "batches";
    public static final String BATCH_FILE = "batch.file";
    public static final String BACKUP_SIZE_BYTES = "backup.size.bytes";
    public static final String DATASTORE_WRITER_MAX_BUFFER_BYTES = "datastore.writer.max.buffer.bytes";
    public static final Set<String> EVENT_BATCH_PASSOVER_FIELDS = Set.of(GlobalConstants.PLUGIN_ID, EventBusConstants.EVENT_TIMESTAMP, DATASTORE_TYPE, EventBusConstants.EVENT_SOURCE, EventBusConstants.EVENT_VOLUME_BYTES, EVENT_COPY_REQUIRED);
    public static final String DATASTORE_UUID = "datastore.uuid";
    public static final String DATASTORE_PASSOVER_STEP_1_QUERY = "passover.step1.query";
    public static final int DATASTORE_REQUEST_BATCH_SIZE = MotadataConfigUtil.getDatastoreRequestBatchSize(); // Used to send number of monitors in a single histogram request
    public static final String DATASTORE_TYPES = "datastore.types";

    private DatastoreConstants()
    {
    }

    public static void runRetention(String eventType, long seconds, Logger logger)
    {
        try
        {
            var path = CURRENT_DIR + PATH_SEPARATOR + EventBusConstants.EVENT_DIR + PATH_SEPARATOR + EventBusConstants.replace(eventType);

            var eventFiles = new File(path + GlobalConstants.PATH_SEPARATOR + "bookmarks");

            if (eventFiles.exists())
            {
                var content = Files.readString(eventFiles.toPath(), StandardCharsets.UTF_8);

                if (!content.isEmpty())
                {
                    var events = new JsonObject(content);

                    logger.info(String.format("%s file found in path : %s ", events.size(), eventFiles.getPath()));

                    if (!events.isEmpty())
                    {
                        for (var entry : events.getMap().entrySet())
                        {
                            var timestamp = CommonUtil.getLong(entry.getKey());

                            if (new File(path + GlobalConstants.PATH_SEPARATOR + timestamp + ".dat").exists() && timestamp < seconds)
                            {
                                Bootstrap.vertx().eventBus().send(EVENT_DATASTORE_RETENTION_TRIGGER + DOT_SEPARATOR + eventType, timestamp);
                            }
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            logger.error(exception);
        }
    }

    public static byte getDataCategory(boolean metric, String key, String value)
    {
        var category = DataCategory.STRING.getName();

        if (metric && (key.endsWith(".id") || key.endsWith(".uptime") || key.endsWith(".version") || key.endsWith(".state") ||
                key.endsWith(".status") || key.endsWith(".creation.time.seconds") || key.endsWith(".uptime.sec") ||
                key.endsWith(".uptime.seconds") || key.endsWith(".started.time")))
        {

            return category;
        }
        else if (!key.endsWith(NMSConstants.INSTANCE_NAME) && value != null && !value.startsWith("0x") && NumberUtils.isCreatable(value))
        {
            if (key.endsWith(".percent") || key.endsWith(".cost") ||
                    key.endsWith(".amount") ||
                    key.endsWith(".system.load.avg") ||
                    key.contains(".avg.") || key.contains("latency") ||
                    floatingColumns.contains(key))
            {
                category = DataCategory.FLOAT.getName();
            }
            else if (CommonUtil.isNumeric(value))
            {
                category = DataCategory.NUMERIC.getName();
            }
        }

        return category;
    }

    // write datastore event
    public static void write(JsonObject event, String eventCategory, Set<String> mappers, StringBuilder builder)
    {
        try
        {
            builder.setLength(0);

            var source = event.getString(EventBusConstants.EVENT_SOURCE);

            var correlatedBatch = event.getString(NMSConstants.CORRELATION_METRICS, NO);

            event.remove(NMSConstants.CORRELATION_METRICS);

            if (!event.containsKey(LogEngineConstants.EVENT_CATEGORY))
            {
                event.put(LogEngineConstants.EVENT_CATEGORY, eventCategory);
            }

            var iterator = event.iterator();

            var buffer = Buffer.buffer("UTF-8");

            appendBytes(buffer, event.getLong(EventBusConstants.EVENT_TIMESTAMP), event.getInteger(PLUGIN_ID), event.getInteger(DATASTORE_TYPE), source != null ? source : EMPTY_VALUE, eventCategory, mappers, builder);

            while (iterator.hasNext())
            {
                var entry = iterator.next();

                if (!EVENT_BATCH_PASSOVER_FIELDS.contains(entry.getKey()) && entry.getValue() != null)
                {
                    updateEventFieldBatch(entry.getKey(), replace(CommonUtil.getString(entry.getValue())), event.getInteger(GlobalConstants.PLUGIN_ID), buffer, mappers, builder, eventCategory, correlatedBatch);
                }
            }

            Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_DATASTORE_WRITE + "." + DatastoreCategory.EVENT.getName(), buffer.getBytes());

        }
        catch (Exception exception)
        {
            exception.getStackTrace();
        }
    }

    private static void appendBytes(Buffer bytes, long timestamp, int pluginId, int dataStoreType, String source, String eventCategory, Set<String> mappers, StringBuilder builder)
    {
        bytes.setLongLE(0, timestamp);

        var plugin = pluginId + DASH_SEPARATOR + (eventCategory.trim().replace(" ", ".").toLowerCase());

        var valueBytes = plugin.getBytes(StandardCharsets.UTF_8);

        bytes.appendIntLE(valueBytes.length);

        bytes.appendBytes(valueBytes);

        bytes.appendByte(DatastoreFormat.HORIZONTAL.getName().byteValue());

        bytes.appendByte(CommonUtil.getByteValue(dataStoreType));

        valueBytes = source.getBytes(StandardCharsets.UTF_8);

        bytes.appendShortLE(CommonUtil.getShort(valueBytes.length));

        bytes.appendBytes(valueBytes);

        builder.append(DataCategory.STRING.getName()).append(COLUMN_SEPARATOR).append(PluginId.GENERIC_LOG_EVENT.getName()).append(COLUMN_SEPARATOR).append(EVENT_SOURCE.getName()).append(COLUMN_SEPARATOR).append(NO).append(COLUMN_SEPARATOR).append(VisualizationConstants.VisualizationDataSource.EVENT_HISTORY.getName());

        if (!mappers.contains(builder.toString()))
        {
            mappers.add(builder.toString());

            Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE,
                    new JsonObject()
                            .put(CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_EVENT_COLUMN.name())
                            .put(MAPPER, builder.toString()));
        }
    }

    private static void updateEventFieldBatch(String column, String value, int pluginId, Buffer buffer, Set<String> mappers, StringBuilder builder, String eventCategory, String correlatedMetric)
    {
        builder.setLength(0);

        byte category;

        if (column.equalsIgnoreCase(GlobalConstants.MESSAGE) || column.equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_SOURCE.getName()) || column.equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_CATEGORY.getName())
                || column.equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_SOURCE_TYPE.getName()) || column.equalsIgnoreCase(LogEngineConstants.EVENT_SEVERITY))
        {
            pluginId = PluginId.GENERIC_LOG_EVENT.getName();

            category = DataCategory.STRING.getName();

            eventCategory = "event.history";
        }
        else
        {
            category = getDataCategory(false, column, value);
        }

        if (category == DataCategory.FLOAT.getName())
        {
            buffer.appendByte(DataCategory.NUMERIC.getName());

            category = DataCategory.NUMERIC.getName();
        }
        else
        {
            buffer.appendByte(category);
        }

        var bytes = column.getBytes(StandardCharsets.UTF_8);

        buffer.appendIntLE(bytes.length);

        buffer.appendBytes(bytes);

        if (category == DataCategory.NUMERIC.getName())//if it has decimal number than convert it into integer
        {
            buffer.appendLongLE(Math.round(CommonUtil.getDouble(value)));
        }
        else
        {
            var valueBytes = value.getBytes(StandardCharsets.UTF_8);

            buffer.appendIntLE(valueBytes.length);

            buffer.appendBytes(valueBytes);
        }


        builder.append(category).append(COLUMN_SEPARATOR).append(pluginId).append(COLUMN_SEPARATOR).append(column).append(COLUMN_SEPARATOR).append(correlatedMetric).append(COLUMN_SEPARATOR).append(eventCategory);

        if (!mappers.contains(builder.toString()))
        {
            mappers.add(builder.toString());

            Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE,
                    new JsonObject()
                            .put(CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_EVENT_COLUMN.name())
                            .put(MAPPER, builder.toString()));
        }
    }

    //add default floating columns
    public static void loadConfigs()
    {
        var file = new File(CURRENT_DIR + PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + PATH_SEPARATOR + FLOATING_COLUMNS);

        if (file.exists())
        {
            var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(file.getPath());

            if (buffer != null && buffer.getBytes().length > 0)
            {
                floatingColumns.addAll(new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes()))).fieldNames());
            }
        }
    }

    public static void notify(JsonObject context, int operationType, JsonArray ids)
    {
        var buffer = Buffer.buffer();

        // introduce switch case if multiple operation types are there in future
        if (operationType == OperationType.MODIFY_BACKUP_PROFILE.ordinal())
        {
            buffer.appendByte(OperationType.MODIFY_BACKUP_PROFILE.getName()).appendBytes(context.encode().getBytes());

            for (var id : ids)
            {
                var item = RemoteEventProcessorConfigStore.getStore().getItem(CommonUtil.getLong(id));

                if (item != null && item.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(BootstrapType.DATASTORE.name()))
                {
                    Bootstrap.vertx().eventBus().send(EVENT_PUBLICATION_DATASTORE_WRITE, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false)
                            .put(EventBusConstants.EVENT_TOPIC, DATASTORE_CONNECTION_ALIVE_TOPIC)
                            .put(REMOTE_EVENT_PROCESSOR_TOPIC, DATASTORE_OPERATION_TOPIC)
                            .put(REMOTE_EVENT_PROCESSOR_UUID, item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID))
                            .put(EVENT_CONTEXT, buffer.getBytes()));
                }
            }
        }
    }

    /**
     * Replaces all reserved separators in the given value with a space character.
     * This method is used to sanitize event data before inserting into the database
     * to prevent conflicts with reserved separator characters.
     *
     * @param value The string value to sanitize
     * @return The sanitized string with all reserved separators replaced by spaces
     */
    public static String replace(String value)
    {
        if (value == null) return null;

        return value.replace("@#$", EMPTY_VALUE).replace("###", EMPTY_VALUE).replace("§", EMPTY_VALUE).replace("$$", EMPTY_VALUE).replace("$", EMPTY_VALUE).replace("#@#", EMPTY_VALUE);
    }

    public enum MetricWriterOrdinal
    {
        DATA_CATEGORY,

        OBJECT_ID,

        COLUMN,

        INSTANCE,

        VALUE,

        STATUS
    }

    public enum EventWriterOrdinal
    {
        DATA_CATEGORY,

        SOURCE,

        COLUMN,

        VALUE,

        GROUP
    }

    public enum PluginId
    {
        CORRELATED_METRIC(490000),

        FLOW_EVENT(500000),

        TRAP_EVENT(500001),

        AUDIT_EVENT(500002),

        POLICY_EVENT(500004),

        POLICY_FLOW(500005),

        POLICY_RESULT(500007),

        NOTIFICATION_EVENT(500008),

        UNKNOWN_LOG_EVENT(500009),

        POLICY_TRAP(500010),

        LOG_EVENT_STAT(500011),

        GENERIC_LOG_EVENT(499999),

        LOG_EVENT(600000),

        METRIC_POLICY_DURATION_METRIC(499998),

        OBJECT_STATUS_DURATION_METRIC(0),

        HEALTH_METRIC(500014),

        CACHE_DATASTORE_EVENT(500015),

        PENDING_FILES_DATASTORE_EVENT(500016),

        QUERY_STATS_DATASTORE_EVENT(500017),

        FLOW_EVENT_STAT(500018),

        STATIC_METRIC(500019),

        JVM_HEALTH_METRIC(500020),

        JVM_GC_HEALTH_METRIC(500021),

        RUNBOOK_WORKLOG(500022),

        COMPLIANCE(500023),

        NETROUTE_STATUS_DURATION_METRIC(500024),

        NETROUTE_METRIC(500025),

        NETROUTE_EVENT(500026),

        POLICY_NETROUTE(500027),

        NETROUTE_POLICY_DURATION_METRIC(500028),

        SLO(500029),

        SLO_INSTANCE(500030),

        SLO_FLAP(500031),

        SLO_INSTANCE_FLAP(500032);

        private static final Map<Integer, PluginId> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(PluginId::getName, e -> e)));
        private final int name;

        PluginId(int name)
        {
            this.name = name;
        }

        public static PluginId valueOfName(int name)
        {
            return VALUES.get(name);
        }

        public int getName()
        {
            return name;
        }
    }

    public enum DatastoreFormat
    {
        VERTICAL(0),
        HORIZONTAL(1);

        private static final Map<Integer, DatastoreFormat> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(DatastoreFormat::getName, e -> e)));
        private final int name;

        DatastoreFormat(Integer name)
        {
            this.name = name;
        }

        public static DatastoreFormat valueOfName(Integer name)
        {
            return VALUES.get(name);
        }

        public Integer getName()
        {
            return name;
        }
    }

    public enum DatastoreCategory
    {
        METRIC(0),
        EVENT(1),
        AVAILABILITY(2);

        private static final Map<Integer, DatastoreCategory> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(DatastoreCategory::getName, e -> e)));
        private final int name;

        DatastoreCategory(Integer name)
        {
            this.name = name;
        }

        public static DatastoreCategory valueOfName(Integer name)
        {
            return VALUES.get(name);
        }

        public Integer getName()
        {
            return name;
        }
    }

    public enum DatastoreType
    {
        INDEX,

        PERFORMANCE_METRIC,

        OBJECT_STATUS_METRIC,

        TRAP_ACKNOWLEDGEMENT,

        METRIC_POLICY_ACKNOWLEDGEMENT,

        EVENT_POLICY_ACKNOWLEDGEMENT,

        AUDIT,

        NOTIFICATION,

        METRIC_POLICY_FLAP_HISTORY,

        OBJECT_STATUS_FLAP_HISTORY,

        TRAP_FLAP_HISTORY,

        LOG,

        FLOW,

        TRAP,

        METRIC_POLICY,

        EVENT_POLICY,

        CORRELATED_METRIC,

        RUNBOOK_WORKLOG,

        POLICY_RESULT,

        CORRELATION_WORKLOG,

        HEALTH_METRIC,

        STATIC_METRIC,

        COMPLIANCE,

        TRACE_METRIC,

        TRACE_EVENT,

        NETROUTE_METRIC,

        OBJECT_STATUS_FLAP_METRIC,

        NETROUTE_STATUS_METRIC,

        SLO_METRIC,

        SLO_FLAP_METRIC
    }

    public enum OperationType
    {
        DATA_WRITE((byte) 0),

        DATA_UPDATE((byte) 1),

        DATA_READ((byte) 2),

        QUERY_ABORT((byte) 3),

        INDEX_UPDATE((byte) 5),

        DATASTORE_INIT((byte) 6),

        DATA_FLUSH((byte) 9),

        WIDGET_CREATE((byte) 10),

        TRAP_ACKNOWLEDGE((byte) 11),

        METRIC_POLICY_ACKNOWLEDGE((byte) 12),

        EVENT_POLICY_ACKNOWLEDGE((byte) 13),

        STORE_RETENTION((byte) 14),

        STORE_BACKUP((byte) 15),

        WIDGET_DELETE((byte) 17),

        CONNECTION_KEEP_ALIVE((byte) 18),

        HEALTH_STATS((byte) 19),

        MODIFY_LOG_LEVEL((byte) 20),

        PROFILER((byte) 21),

        ACKNOWLEDGE((byte) 22),

        MODIFY_BACKUP_PROFILE((byte) 23),

        SHADOW_COUNTER((byte) 24),

        LIVE_DATA_FLUSH((byte) 25),

        START_SLO_CYCLE((byte) 26);

        private static final Map<Byte, OperationType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(OperationType::getName, e -> e)));
        private final Byte name;

        OperationType(Byte name)
        {
            this.name = name;
        }

        public static OperationType valueOfName(Byte name)
        {
            return VALUES.get(name);
        }

        public Byte getName()
        {
            return name;
        }

    }

    public enum DataCategory
    {
        STRING((byte) 0),

        NUMERIC((byte) 1),

        FLOAT((byte) 2);

        private static final Map<Byte, DataCategory> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(DataCategory::getName, e -> e)));
        private final byte name;

        DataCategory(byte name)
        {
            this.name = name;
        }

        public static DataCategory valueOfName(byte name)
        {
            return VALUES.get(name);
        }

        public byte getName()
        {
            return name;
        }
    }

    public enum ConditionGroup
    {
        AND("and"),

        OR("or"),

        IN("in"),

        EQUAL("="),

        WHERE("where");

        private static final Map<String, ConditionGroup> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(ConditionGroup::getName, e -> e)));
        private final String name;

        ConditionGroup(String name)
        {
            this.name = name;
        }

        public static ConditionGroup valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    public enum DataType
    {
        INT32((byte) 48),

        INT64((byte) 112),

        FLOAT32((byte) 160),

        STRING((byte) 176);

        private static final Map<Byte, DataType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(DataType::getName, e -> e)));
        private final byte name;

        DataType(byte name)
        {
            this.name = name;
        }

        public static DataType valueOfName(byte name)
        {
            return VALUES.get(name);
        }

        public byte getName()
        {
            return name;
        }

        public short getSize()
        {
            return CommonUtil.getShort(getName());
        }
    }

    public enum AggregationType
    {
        NO_AGGREGATION("none"),

        AVG("avg"),

        MIN("min"),

        MAX("max"),

        SUM("sum"),

        COUNT("count"),

        SPARKLINE("sparkline"),

        LAST("last");

        private static final Map<String, AggregationType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(AggregationType::getName, e -> e)));
        private final String name;

        AggregationType(String name)
        {
            this.name = name;
        }

        public static AggregationType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }
}
