/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 *  17-APR-2025     Chopra Deven        MOTADATA-5827: added "object.groups" column added in case of report
 *  15-May-2025     <PERSON><PERSON>        Added Duration in drilldown for Availability.
 *  23-May-2025     Chopra Deven        MOTADATA-6187: added "instance.ip" and "discovered.time" column added in case of a report.
 *  07-Aug-2025     Aagam Salot         MOTADATA-6919: Interface Availability support for server
 *  05-Aug-2025     Aagam Salot         MOTADATA-6851: Added reason in object state maintenance
 */

package com.mindarray.cache;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.Group;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.*;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.policy.PolicyEngineConstants.DURATION;
import static com.mindarray.policy.PolicyEngineConstants.INSTANCE_TYPE;

/**
 * Implementation of the {@link AvailabilityCacheService} interface that provides methods for retrieving
 * cached data related to availability status, heatmaps, and drill-down information.
 * <p>
 * This class reads cached data from various stores, processes the data as needed, and returns it
 * to the caller. It uses Vert.x's executeBlocking method to perform operations without blocking
 * the event loop.
 * <p>
 * The availability data is organized by status, object, instance, and other identifiers to allow
 * for efficient retrieval and visualization of specific data.
 *
 * @see AvailabilityCacheService
 * @see CacheServiceProvider
 */
public class AvailabilityCacheServiceImpl implements AvailabilityCacheService
{
    /**
     * Logger instance for this class.
     */
    private static final Logger LOGGER = new Logger(AvailabilityCacheServiceImpl.class, MOTADATA_CACHE, "Availability Cache Service");

    /**
     * The Vert.x instance used for executing blocking operations.
     */
    private final Vertx vertx;

    /**
     * Constructs a new AvailabilityCacheServiceImpl instance.
     *
     * @param vertx   The Vert.x instance
     * @param handler The handler to be called when the service is created
     */
    AvailabilityCacheServiceImpl(Vertx vertx, Handler<AsyncResult
            <AvailabilityCacheService>> handler)
    {
        this.vertx = vertx;

        handler.handle(Future.succeededFuture(this));
    }

    /**
     * {@inheritDoc}
     * <p>
     * This implementation executes a blocking operation to build the availability status result
     * by retrieving data from the ObjectStatusCacheStore. It counts the number of objects or instances
     * with each status specified in the context.
     */
    @Override
    public AvailabilityCacheService getAvailabilityByStatus(JsonObject context, Handler<AsyncResult<JsonObject>> handler)
    {
        vertx.<JsonObject>executeBlocking(future ->
                {
                    var result = buildStatusResult(context, context.getJsonArray(ENTITIES));

                    if (!result.isEmpty())
                    {
                        handler.handle(Future.succeededFuture(result));
                    }
                    else
                    {
                        handler.handle(Future.failedFuture(ErrorMessageConstants.INVALID_DATA_SOURCE));
                    }

                    future.complete(result);
                }, false,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });
        return this;
    }

    /**
     * {@inheritDoc}
     * <p>
     * This implementation executes a blocking operation to build the availability heatmap result
     * by retrieving data from the ObjectStatusCacheStore. It organizes the data by object, instance,
     * and status to create a heatmap visualization.
     */
    @Override
    public AvailabilityCacheService getAvailabilityHeatmap(JsonObject context, Handler<AsyncResult<JsonObject>> handler)
    {
        vertx.<JsonObject>executeBlocking(future ->
                {
                    var result = buildAvailabilityHeatmap(context, context.getJsonArray(ENTITIES));

                    if (!result.isEmpty())
                    {
                        handler.handle(Future.succeededFuture(result));
                    }
                    else
                    {
                        handler.handle(Future.failedFuture(ErrorMessageConstants.INVALID_DATA_SOURCE));
                    }

                }, false,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });

        return this;
    }

    /**
     * {@inheritDoc}
     * <p>
     * This implementation executes a blocking operation to retrieve active status information
     * for objects or instances. It queries the event bus for object status duration data
     * and combines it with status information from the ObjectStatusCacheStore.
     */
    @Override
    public AvailabilityCacheService getActiveStatus(JsonObject context, Handler<AsyncResult<JsonObject>> handler)
    {
        vertx.<JsonObject>executeBlocking(future -> getObjectActiveStatuses(context, context.getJsonArray(ENTITIES)).onComplete(result ->
                        handler.handle(Future.succeededFuture(result.result()))), false,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });

        return this;
    }

    /**
     * {@inheritDoc}
     * <p>
     * This implementation executes a blocking operation to retrieve application status information.
     * It retrieves application data from the MetricConfigStore and status information from the
     * ObjectStatusCacheStore, then combines them to provide a comprehensive view of application status.
     */
    @Override
    public AvailabilityCacheService getApplicationStatus(JsonObject context, Handler<AsyncResult<JsonObject>> handler)
    {
        vertx.<JsonObject>executeBlocking(future -> buildApplicationStatusResult(context.getJsonArray(ENTITIES)).onComplete(result ->
                        handler.handle(Future.succeededFuture(result.result()))), false,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });

        return this;
    }

    /**
     * {@inheritDoc}
     * <p>
     * This implementation executes a blocking operation to retrieve drill-down information.
     * It retrieves detailed information about objects or instances from various stores,
     * including ObjectConfigStore, ObjectStatusCacheStore, and MetricPolicyCacheStore,
     * and formats the data for display in a grid visualization.
     */
    @Override
    public AvailabilityCacheService getDrillDownResult(JsonObject context, Handler<AsyncResult<JsonObject>> handler)
    {
        vertx.<JsonObject>executeBlocking(future -> buildDrillDownResult(context, context.getJsonArray(ENTITIES)).onComplete(result ->
                        handler.handle(Future.succeededFuture(result.result()))), false,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });

        return this;
    }

    /**
     * Builds the result for drill-down visualization.
     * <p>
     * This method retrieves detailed information about objects or instances based on the context
     * and formats the data for display in a grid visualization. It supports both object-level
     * and instance-level drill-down.
     * <p>
     * Drill down support for Gauge, Type - Grid, Fetch Data from Object as well as Instance and will return JsonObject as per Grid Requirement.
     *
     * @param context  The context containing the parameters for the query
     * @param entities The array of entity IDs for which to retrieve drill-down information
     * @return A future that will be completed with the result
     */
    private Future<JsonObject> buildDrillDownResult(JsonObject context, JsonArray entities)
    {
        var result = new JsonObject();

        var promise = Promise.<JsonObject>promise();

        try
        {
            Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_OBJECT_STATUS_DURATION_QUERY, context, reply ->
            {
                try
                {
                    if (reply.result() != null && !reply.result().body().isEmpty())
                    {
                        var durations = reply.result().body();

                        result.put(SEVERITY, new JsonArray()).put(APIConstants.Entity.OBJECT.getName().toLowerCase(), new JsonArray()).put(AIOpsObject.OBJECT_IP, new JsonArray()).put(AIOpsObject.OBJECT_TYPE, new JsonArray()).put(AIOpsObject.OBJECT_GROUPS, new JsonArray()).put(AIOpsObject.OBJECT_TAGS, new JsonArray()).put(DURATION, new JsonArray());

                        var instance = false;

                        var builder = new StringBuilder();

                        if (context.containsKey(VisualizationConstants.INSTANCE_TYPE) && !context.getString(VisualizationConstants.INSTANCE_TYPE).isEmpty())
                        {
                            instance = true;

                            result.put(INSTANCE, new JsonArray());
                        }

                        for (var idx = 0; idx < entities.size(); idx++)
                        {
                            var object = ObjectConfigStore.getStore().getItem(entities.getLong(idx));

                            if (instance)
                            {
                                var instanceItems = ObjectStatusCacheStore.getStore().getInstanceItems(entities.getLong(idx), false);

                                if (instanceItems != null && !instanceItems.isEmpty())
                                {
                                    for (var instanceItem : instanceItems.entrySet())
                                    {
                                        var entry = instanceItem.getKey().split(INSTANCE_SEPARATOR);

                                        if (CommonUtil.debugEnabled())
                                        {
                                            LOGGER.debug(String.format("instance : %s , status : %s", instanceItem.getKey(), instanceItem.getValue()));
                                        }

                            /*
                                As key contains instance type so splitting and checking it matches same selected instance with selected status and
                                for Instance Level check status from context, if it matches with the status of the Object the set result data.
                            */
                                        if (entry[0].equalsIgnoreCase(context.getString(VisualizationConstants.INSTANCE_TYPE)) && context.getJsonArray(STATUS).contains(CommonUtil.getString(instanceItem.getValue()).toLowerCase()))
                                        {
                                            result.getJsonArray(DURATION).add(durations.getJsonObject(object.getString(ID)).getInteger(instanceItem.getKey(), 0));

                                            result.getJsonArray(INSTANCE).add(entry[1]);

                                            result.getJsonArray(SEVERITY).add(MetricPolicyCacheStore.getStore().getInstanceSeverity(object.getLong(ID) + SEPARATOR + entry[1]) != null ? MetricPolicyCacheStore.getStore().getInstanceSeverity(object.getLong(ID) + SEPARATOR + entry[1]) : Severity.UNKNOWN.name());

                                            setColumns(result, builder, object);
                                        }
                                    }
                                }
                            }
                            else
                            {
                                // For Object Level check status from context, if it matches with the status of the Object the set result data.
                                if (ObjectStatusCacheStore.getStore().getItem(object.getLong(ID)) != null && (context.containsKey(STATUS) && context.getJsonArray(STATUS).contains(ObjectStatusCacheStore.getStore().getItem(object.getLong(ID)).toLowerCase())))
                                {
                                    result.getJsonArray(DURATION).add(durations.getJsonObject(object.getString(ID)).getInteger(EMPTY_VALUE, 0));

                                    result.getJsonArray(SEVERITY).add(MetricPolicyCacheStore.getStore().getSeverity(object.getLong(ID)) != null ? MetricPolicyCacheStore.getStore().getSeverity(object.getLong(ID)) : Severity.UNKNOWN.name());

                                    setColumns(result, builder, object);
                                }
                            }
                        }
                    }

                    if (result.isEmpty() || result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).isEmpty())
                    {
                        promise.fail(ErrorMessageConstants.INVALID_NO_DATA_FOUND);
                    }
                    else
                    {
                        promise.complete(result);
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return promise.future();
    }

    /**
     * Sets object column values in the result.
     * <p>
     * This method adds object-specific information to the result, including object ID, type, IP,
     * tags, and groups. It formats the data as needed for display in the visualization.
     *
     * @param result  The result object to which to add the column values
     * @param builder A StringBuilder used for concatenating values
     * @param object  The object for which to set column values
     */
    private void setColumns(JsonObject result, StringBuilder builder, JsonObject object)
    {
        result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).add(object.getInteger(AIOpsObject.OBJECT_ID));

        result.getJsonArray(AIOpsObject.OBJECT_TYPE).add(object.getString(AIOpsObject.OBJECT_TYPE, EMPTY_VALUE));

        result.getJsonArray(AIOpsObject.OBJECT_IP).add(object.getString(AIOpsObject.OBJECT_IP, EMPTY_VALUE));

        if (object.containsKey(AIOpsObject.OBJECT_TAGS) && object.getJsonArray(AIOpsObject.OBJECT_TAGS) != null && !object.getJsonArray(AIOpsObject.OBJECT_TAGS).isEmpty())
        {
            for (var tag : object.getJsonArray(AIOpsObject.OBJECT_TAGS))
            {
                if (!builder.isEmpty())
                {
                    builder.append(COMMA_SEPARATOR); // Append Value separator if not the first string
                }

                builder.append(TagConfigStore.getStore().getTag(CommonUtil.getLong(tag)));
            }
        }
        else
        {
            builder.append(EMPTY_VALUE);
        }

        result.getJsonArray(AIOpsObject.OBJECT_TAGS).add(builder.toString());

        builder.setLength(0);

        if (object.containsKey(AIOpsObject.OBJECT_GROUPS) && !object.getJsonArray(AIOpsObject.OBJECT_GROUPS).isEmpty())
        {
            for (var group : object.getJsonArray(AIOpsObject.OBJECT_GROUPS))
            {
                if (!builder.isEmpty())
                {
                    builder.append(COMMA_SEPARATOR); // Append comma separator if not the first string
                }

                builder.append(group);
            }
        }
        else
        {
            builder.append(EMPTY_VALUE);
        }

        result.getJsonArray(AIOpsObject.OBJECT_GROUPS).add(builder.toString());

        builder.setLength(0);
    }

    private Future<JsonObject> buildApplicationStatusResult(JsonArray entities)
    {
        var result = new JsonObject();

        result.put(STATUS, new JsonArray()).put(INSTANCE, new JsonArray()).put(VisualizationConstants.TYPE, new JsonArray());

        var promise = Promise.<JsonObject>promise();

        try
        {
            for (var entity : entities)
            {
                var apps = MetricConfigStore.getStore().getAppsByObjectId(CommonUtil.getLong(entity));

                var items = ObjectStatusCacheStore.getStore().getInstanceItems(CommonUtil.getLong(entity), false);

                if (apps != null && items != null && !items.isEmpty())
                {
                    for (var entry : items.entrySet())
                    {
                        if (apps.containsKey(entry.getKey().split(INSTANCE_SEPARATOR)[1]))
                        {
                            result.getJsonArray(STATUS).add(entry.getValue());

                            result.getJsonArray(INSTANCE).add(entry.getKey().split(INSTANCE_SEPARATOR)[1].trim());

                            result.getJsonArray(VisualizationConstants.TYPE).add(apps.remove(entry.getKey().split(INSTANCE_SEPARATOR)[1]));
                        }

                        if (apps.isEmpty())
                        {
                            //as we got all application so need to iterate items
                            break;
                        }
                    }
                }
            }

            if (result.isEmpty() || result.getJsonArray(STATUS).isEmpty())
            {
                promise.fail(ErrorMessageConstants.INVALID_NO_DATA_FOUND);
            }
            else
            {
                promise.complete(result);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return promise.future();
    }

    private JsonObject buildStatusResult(JsonObject context, JsonArray items)
    {
        var result = new JsonObject();

        if (context.containsKey(VisualizationConstants.INSTANCE_TYPE) && !context.getString(VisualizationConstants.INSTANCE_TYPE).isEmpty())
        {
            for (var index = 0; index < items.size(); index++)
            {
                var instanceItems = ObjectStatusCacheStore.getStore().getInstanceItems(items.getLong(index), false);

                if (instanceItems != null && !instanceItems.isEmpty())
                {
                    if (context.containsKey(VisualizationConstants.VisualizationGrouping.TAG.getName()) && !context.getJsonObject(VisualizationConstants.VisualizationGrouping.TAG.getName()).isEmpty())
                    {
                        for (var entry : context.getJsonObject(VisualizationConstants.VisualizationGrouping.TAG.getName()))
                        {
                            for (var pluginId : context.getJsonArray("plugin.ids"))
                            {
                                for (var entity : (JsonArray) entry.getValue()) //Entities From the Qualified Entity...
                                {
                                    if (instanceItems.containsKey(context.getString(INSTANCE_TYPE) + INSTANCE_SEPARATOR + CommonUtil.getString(entity).split(GROUP_SEPARATOR)[1] + INSTANCE_SEPARATOR + pluginId))
                                    {
                                        var status = instanceItems.get(context.getString(INSTANCE_TYPE) + INSTANCE_SEPARATOR + CommonUtil.getString(entity).split(GROUP_SEPARATOR)[1] + INSTANCE_SEPARATOR + pluginId).toLowerCase();

                                        if (context.containsKey(STATUS) && context.getJsonArray(STATUS).contains(status))
                                        {
                                            if (result.containsKey(status))
                                            {
                                                result.put(status, result.getInteger(status) + 1);
                                            }
                                            else
                                            {
                                                result.put(status, 1);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        for (var entry : instanceItems.entrySet())
                        {
                            var status = CommonUtil.getString(entry.getValue()).toLowerCase();

                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("instance : %s , status : %s", entry.getKey(), entry.getValue()));
                            }

                            //as key contains instance type so splitting and checking it matches same selected instance with selected status if matches incrementing count
                            if (entry.getKey().split(INSTANCE_SEPARATOR)[0].equalsIgnoreCase(context.getString(VisualizationConstants.INSTANCE_TYPE)) && context.getJsonArray(STATUS).contains(status))
                            {
                                if (result.containsKey(status))
                                {
                                    result.put(status, result.getInteger(status) + 1);
                                }
                                else
                                {
                                    result.put(status, 1);
                                }
                            }
                        }
                    }
                }
            }
        }
        else
        {
            for (var index = 0; index < items.size(); index++)
            {
                if (ObjectStatusCacheStore.getStore().getItem(items.getLong(index)) != null)
                {
                    var status = ObjectStatusCacheStore.getStore().getItem(items.getLong(index)).toLowerCase();

                    if (context.getJsonArray(STATUS).contains(status))
                    {
                        if (result.containsKey(status))
                        {
                            result.put(status, result.getInteger(status) + 1);
                        }
                        else
                        {
                            result.put(status, 1);
                        }
                    }
                }
            }
        }

        context.getJsonArray(STATUS).stream().filter(status -> !result.containsKey(CommonUtil.getString(status))).forEach(status -> result.put(CommonUtil.getString(status), 0));

        return result;
    }

    private JsonObject buildAvailabilityHeatmap(JsonObject context, JsonArray objects)
    {
        var result = new JsonObject();

        try
        {
            if (context.containsKey(INSTANCE_TYPE) && context.getJsonArray(INSTANCE_TYPE).getString(0).equalsIgnoreCase(EMPTY_VALUE))
            {
                if (context.containsKey(VisualizationConstants.VISUALIZATION_RESULT_BY) && context.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).getString(0).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.TAG.getName()))
                {
                    result.put(VisualizationConstants.VisualizationGrouping.TAG.getName(), new JsonArray()).put(APIConstants.Entity.OBJECT.getName().toLowerCase(), new JsonArray()).put(STATUS, new JsonArray());

                    for (var entry : context.getJsonObject(VisualizationConstants.VisualizationGrouping.TAG.getName()).getMap().entrySet()) //Entities From the Qualified Entity...
                    {
                        var tag = entry.getKey();

                        for (var object : (JsonArray) entry.getValue()) //Entities From the Qualified Entity...
                        {
                            var item = ObjectStatusCacheStore.getStore().getItem(ObjectConfigStore.getStore().getIdByObjectId(CommonUtil.getInteger(object)));

                            if (item != null && !context.getString(VisualizationConstants.GROUP_BY).equalsIgnoreCase(VisualizationConstants.GROUP_APP) && context.getJsonArray(STATUS).contains(item.toLowerCase()))
                            {
                                updateAvailabilityStatusByEntity(result, Status.valueOf(item.toUpperCase()), VisualizationConstants.VisualizationGrouping.TAG.getName(), tag, CommonUtil.getInteger(object), EMPTY_VALUE);
                            }
                        }
                    }
                }
                else
                {
                    result.put(APIConstants.Entity.OBJECT.getName().toLowerCase(), new JsonArray()).put(STATUS, new JsonArray());

                    for (var object : objects) //Entities From the Qualified Entity...
                    {
                        var item = ObjectStatusCacheStore.getStore().getItem(CommonUtil.getLong(object));

                        if (item != null && !context.getString(VisualizationConstants.GROUP_BY).equalsIgnoreCase(VisualizationConstants.GROUP_APP) && context.getJsonArray(STATUS).contains(item.toLowerCase()))
                        {
                            updateAvailabilityStatus(result, Status.valueOf(item.toUpperCase()), CommonUtil.getLong(object), 0, 0, EMPTY_VALUE, EMPTY_VALUE);
                        }
                    }
                }

            }
            else
            {
                if (context.containsKey(VisualizationConstants.VISUALIZATION_RESULT_BY) && context.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).getString(0).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.TAG.getName()))
                {
                    result.put(VisualizationConstants.VisualizationGrouping.TAG.getName(), new JsonArray()).put(APIConstants.Entity.OBJECT.getName().toLowerCase(), new JsonArray()).put(STATUS, new JsonArray()).put(INSTANCE, new JsonArray());

                    setTagResult(context, result, VisualizationConstants.VisualizationGrouping.TAG.getName());
                }
                else
                {
                    result.put(APIConstants.Entity.OBJECT.getName().toLowerCase(), new JsonArray()).put(STATUS, new JsonArray()).put(INSTANCE, new JsonArray()).put(INSTANCE_IP, new JsonArray());

                    if (context.containsKey(VisualizationConstants.VisualizationGrouping.TAG.getName()) && !context.getJsonObject(VisualizationConstants.VisualizationGrouping.TAG.getName()).isEmpty())
                    {
                        setTagResult(context, result, EMPTY_VALUE);
                    }
                    else if (!context.containsKey(VisualizationConstants.VisualizationGrouping.TAG.getName()))
                    {
                        for (var object : objects) //Entities From the Qualified Entity...
                        {
                            JsonObject apps = null;

                            var items = ObjectStatusCacheStore.getStore().getInstanceItems(CommonUtil.getLong(object), false);

                            if (items != null)
                            {
                                if (context.getString(VisualizationConstants.GROUP_BY).equalsIgnoreCase(VisualizationConstants.GROUP_APP))
                                {
                                    apps = MetricConfigStore.getStore().getAppsByObjectId(CommonUtil.getLong(object));
                                }

                                for (var entry : items.entrySet())
                                {
                                    var status = CommonUtil.getString(entry.getValue());

                                    var tokens = entry.getKey().split(INSTANCE_SEPARATOR);

                                    var type = getInstanceTypeByMetric(context, tokens, tokens[0]);

                                    if (apps != null && apps.containsKey(tokens[1]) && context.getJsonArray(STATUS).contains(status.toLowerCase()))
                                    {
                                        updateAvailabilityStatus(result, Status.valueOf(status.toUpperCase()), CommonUtil.getLong(object), 0, 0, apps.getString(tokens[1]), tokens[0]);
                                    }
                                    else if (!context.getString(VisualizationConstants.GROUP_BY).equalsIgnoreCase(VisualizationConstants.GROUP_APP) && !context.getString(VisualizationConstants.GROUP_BY).equalsIgnoreCase(VisualizationConstants.GROUP_PROCESS) && context.getJsonArray(VisualizationConstants.INSTANCE_TYPE).contains(tokens[0]) && context.getJsonArray(STATUS).contains(status.toLowerCase()))
                                    {
                                        if (context.getJsonArray(VisualizationConstants.PLUGINS).contains(tokens[2] + DASH_SEPARATOR + type))
                                        {
                                            //as key contains instance type so splitting and checking it matches same selected instance with selected status if matches put that in map...
                                            updateAvailabilityStatus(result, Status.valueOf(status.toUpperCase()), CommonUtil.getLong(object), 0, 0, tokens[1], tokens[0]);
                                        }
                                    }
                                    else if (context.getString(VisualizationConstants.GROUP_BY).equalsIgnoreCase(VisualizationConstants.GROUP_PROCESS) && context.getJsonArray(VisualizationConstants.INSTANCE_TYPE).contains(tokens[0]) && context.getJsonArray(STATUS).contains(status.toLowerCase()))
                                    {
                                        updateAvailabilityStatus(result, Status.valueOf(status.toUpperCase()), CommonUtil.getLong(object), 0, 0, tokens[1], tokens[0]);
                                    }
                                }
                            }
                        }
                    }

                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }

    private void setTagResult(JsonObject context, JsonObject result, String resultBy)
    {
        var instancePlugins = context.getJsonArray(VisualizationConstants.PLUGINS);

        for (var entry : context.getJsonObject(VisualizationConstants.VisualizationGrouping.TAG.getName()).getMap().entrySet()) //Entities From the Qualified Entity...
        {
            var tag = entry.getKey();

            for (var entity : (JsonArray) entry.getValue()) //Entities From the Qualified Entity...
            {
                JsonObject apps = null;

                var tokens = CommonUtil.getString(entity).split(GROUP_SEPARATOR);

                var items = ObjectStatusCacheStore.getStore().getInstanceItems(ObjectConfigStore.getStore().getIdByObjectId(CommonUtil.getInteger(tokens[0])), false);

                for (var instancePlugin : instancePlugins)
                {
                    var values = CommonUtil.getString(instancePlugin).split(DASH_SEPARATOR);

                    var type = getInstanceMetricByType(context, values[1]);

                    if (items != null && items.containsKey(type + INSTANCE_SEPARATOR + tokens[1] + INSTANCE_SEPARATOR + values[0]))
                    {
                        if (context.getString(VisualizationConstants.GROUP_BY).equalsIgnoreCase(VisualizationConstants.GROUP_APP))
                        {
                            apps = MetricConfigStore.getStore().getAppsByObjectId(ObjectConfigStore.getStore().getIdByObjectId(CommonUtil.getInteger(tokens[0])));
                        }

                        var status = CommonUtil.getString(items.get(type + INSTANCE_SEPARATOR + tokens[1] + INSTANCE_SEPARATOR + values[0]));

                        if (apps != null && apps.containsKey(tokens[1]) && context.getJsonArray(STATUS).contains(status.toLowerCase()))
                        {
                            updateAvailabilityStatusByEntity(result, Status.valueOf(status.toUpperCase()), resultBy, tag, CommonUtil.getInteger(tokens[0]), apps.getString(tokens[1]));
                        }
                        else if (!context.getString(VisualizationConstants.GROUP_BY).equalsIgnoreCase(VisualizationConstants.GROUP_APP) && context.getJsonArray(STATUS).contains(status.toLowerCase()))
                        {
                            //as key contains instance type so splitting and checking it matches same selected instance with selected status if matches put that in map...
                            updateAvailabilityStatusByEntity(result, Status.valueOf(status.toUpperCase()), resultBy, tag, CommonUtil.getInteger(tokens[0]), tokens[1]);
                        }
                    }
                }
            }
        }
    }

    /**
     * Gets the instance type based on the metric.
     * <p>
     * This method determines the appropriate instance type based on the metric and context.
     * It handles special cases for VM and interface metrics.
     *
     * @param context The context containing the parameters for the query
     * @param tokens  The tokens from the instance key
     * @param type    The initial instance type
     * @return The determined instance type
     */
    private String getInstanceTypeByMetric(JsonObject context, String[] tokens, String type)
    {
        if (context.getString(VisualizationConstants.GROUP_BY).equalsIgnoreCase(VisualizationConstants.GROUP_VM) && tokens[0].equalsIgnoreCase(NMSConstants.ESXI_VM))
        {
            type = "vmware.esxi.vm";
        }
        else if (context.getString(VisualizationConstants.GROUP_BY).equalsIgnoreCase(NMSConstants.INTERFACE) && tokens[0].equalsIgnoreCase(NMSConstants.INTERFACE))
        {
            type = "network.interface";
        }
        else if (context.getString(VisualizationConstants.GROUP_BY).equalsIgnoreCase(NMSConstants.SYSTEM_NETWORK_INTERFACE) && tokens[0].equalsIgnoreCase(NMSConstants.SYSTEM_NETWORK_INTERFACE))
        {
            type = "linux.network.interface";
        }

        return type;
    }

    /**
     * Gets the metric type based on the instance type.
     * <p>
     * This method determines the appropriate metric type based on the instance type and context.
     * It handles special cases for VM, interface, application, and process metrics.
     *
     * @param context The context containing the parameters for the query
     * @param type    The initial metric type
     * @return The determined metric type
     */
    private String getInstanceMetricByType(JsonObject context, String type)
    {
        if (context.getString(VisualizationConstants.GROUP_BY).equalsIgnoreCase(VisualizationConstants.GROUP_VM))
        {
            if (type.equalsIgnoreCase("vmware.esxi.vm"))
            {
                type = NMSConstants.ESXI_VM;
            }
        }

        else if (context.getString(VisualizationConstants.GROUP_BY).equalsIgnoreCase(NMSConstants.INTERFACE))
        {
            if (type.equalsIgnoreCase("network.interface"))
            {
                type = NMSConstants.INTERFACE;
            }
        }

        else if (context.getString(VisualizationConstants.GROUP_BY).equalsIgnoreCase(VisualizationConstants.GROUP_APP) || context.getString(VisualizationConstants.GROUP_BY).equalsIgnoreCase(VisualizationConstants.GROUP_PROCESS))
        {
            if (type.equalsIgnoreCase("linux.process") || type.equalsIgnoreCase("windows.process"))
            {
                type = NMSConstants.SYSTEM_PROCESS;
            }
            else if (type.equalsIgnoreCase("windows.service"))
            {
                type = NMSConstants.SYSTEM_SERVICE;
            }
        }
        return type;
    }

    private void updateAvailabilityStatus(JsonObject result, GlobalConstants.Status status, long object, long timestamp, int duration, String instance, String instanceType)
    {
        result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).add(ObjectConfigStore.getStore().getObjectIdById(object));

        result.getJsonArray(STATUS).add(status);

        if (instance != null && !instance.isEmpty())
        {
            result.getJsonArray(INSTANCE).add(instance);

            if (NMSConstants.INSTANCE_IP_METRICS.containsKey(instanceType))
            {
                result.getJsonArray(INSTANCE_IP).add(ObjectCacheStore.getStore().getInstanceIP(object, instance));
            }
            else
            {
                result.getJsonArray(INSTANCE_IP).add(EMPTY_VALUE);
            }
        }

        if (timestamp > 0)
        {
            result.getJsonArray(TIME_STAMP).add(timestamp * 1000);

            result.getJsonArray(DURATION).add(duration);
        }
    }

    private void updateAvailabilityStatusByEntity(JsonObject result, GlobalConstants.Status status, String entityType, String entity, int object, String instance)
    {
        result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).add(object);

        /*
        entityType can be {example = Tag} and if Empty Value do not include that in Map.
         */
        if (!entityType.equalsIgnoreCase(EMPTY_VALUE))
        {
            result.getJsonArray(entityType).add(entity);
        }

        result.getJsonArray(STATUS).add(status);

        if (instance != null && !instance.isEmpty())
        {
            result.getJsonArray(INSTANCE).add(instance);
        }
    }

    private void updateObjectStatus(JsonObject result, GlobalConstants.Status status, long object, long timestamp, int duration, String instance, JsonObject context, StringBuilder builder)
    {
        result.getJsonArray(APIConstants.Entity.OBJECT.getName().toLowerCase()).add(ObjectConfigStore.getStore().getObjectIdById(object));

        result.getJsonArray(STATUS).add(status);

        if (instance != null && !instance.isEmpty())
        {
            result.getJsonArray(context.getString(VisualizationConstants.GROUP_BY) != null && !context.getString(VisualizationConstants.GROUP_BY).isEmpty() ? context.getString(VisualizationConstants.GROUP_BY) : context.getJsonArray(INSTANCE_TYPE).getString(0)).add(instance);
        }

        if (timestamp > 0)
        {
            result.getJsonArray(TIME_STAMP).add(timestamp * 1000);

            result.getJsonArray(DURATION).add(duration);
        }

        if (builder != null)
        {
            var item = ObjectConfigStore.getStore().getItem(object, false);

            if (item.containsKey(AIOpsObject.OBJECT_GROUPS))
            {
                var groups = item.getJsonArray(AIOpsObject.OBJECT_GROUPS);

                if (!groups.isEmpty())
                {
                    for (var group : groups)
                    {
                        if (!builder.isEmpty())
                        {
                            builder.append(COMMA_SEPARATOR); // Append comma separator if not the first string
                        }

                        // In Report, we require to have group name instead of group ID
                        builder.append(GroupConfigStore.getStore().getItem(CommonUtil.getLong(group), false).getString(Group.FIELD_GROUP_NAME));
                    }
                }
                else
                {
                    builder.append(EMPTY_VALUE);
                }

                result.getJsonArray(AIOpsObject.OBJECT_GROUPS).add(builder.toString());

                builder.setLength(0);
            }
        }

        // add the "instance.ip" column in case of a report.
        if (result.containsKey(INSTANCE_IP))
        {
            result.getJsonArray(INSTANCE_IP).add(ObjectCacheStore.getStore().getInstanceIP(object, instance));
        }

        // add the "reason" column in case of a report.
        if (result.containsKey(REASON))
        {
            result.getJsonArray(REASON).add(ObjectStatusCacheStore.getStore().getItemByReason(object,EMPTY_VALUE));
        }

        // add the "object.creation.time" column in case of a report.
        if (result.containsKey(AIOpsObject.OBJECT_CREATION_TIME))
        {
            result.getJsonArray(AIOpsObject.OBJECT_CREATION_TIME).add(ObjectConfigStore.getStore().getItem(object).getValue(AIOpsObject.OBJECT_CREATION_TIME_SECONDS, 0));
        }

    }

    private Future<JsonObject> getObjectActiveStatuses(JsonObject context, JsonArray objects)
    {
        var result = new JsonObject();

        var promise = Promise.<JsonObject>promise();

        try
        {
            Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_OBJECT_STATUS_DURATION_QUERY, context, reply ->
            {
                try
                {
                    if (reply.result() != null && !reply.result().body().isEmpty())
                    {
                        var containerTypeReport = context.containsKey(VisualizationConstants.CONTAINER_TYPE) && context.getString(VisualizationConstants.CONTAINER_TYPE) != null && context.getString(VisualizationConstants.CONTAINER_TYPE).equalsIgnoreCase(VisualizationConstants.CONTAINER_TYPE_REPORT);

                        // Extract instance type value from context for reuse
                        var instanceType = context.containsKey(INSTANCE_TYPE) ? context.getJsonArray(INSTANCE_TYPE).getString(0) : null;

                        var durations = reply.result().body();

                        StringBuilder builder = null;

                        if (containerTypeReport)
                        {
                            builder = new StringBuilder();

                            result.put(AIOpsObject.OBJECT_GROUPS, new JsonArray());

                            result.put(REASON, new JsonArray());

                            // if the context contains an instance type, and it is not empty and the instance type is present in the configured IP metrics list then add instance ip column in the result else add DISCOVERED TIME column.
                            if (instanceType != null && !instanceType.isEmpty() && NMSConstants.INSTANCE_IP_METRICS.containsKey(instanceType))
                            {
                                /*
                                 *  If the object is an instance:
                                 *  - We need to add INSTANCE_IP as a column in the result
                                 *  - We do NOT include OBJECT_CREATION_TIME since it's not applicable to instances
                                 */
                                result.put(INSTANCE_IP, new JsonArray());
                            }
                            else if (EMPTY_VALUE.equalsIgnoreCase(instanceType))
                            {
                                /*
                                 *  If the object is a monitor (not an instance):
                                 *  - We include OBJECT_CREATION_TIME as it's relevant to monitor creation
                                 *  - We do NOT include INSTANCE_IP as monitors don't have instance IP
                                 */
                                result.put(AIOpsObject.OBJECT_CREATION_TIME, new JsonArray());
                            }

                            context.remove(VisualizationConstants.CONTAINER_TYPE);
                        }

                        if (context.containsKey(INSTANCE_TYPE) && context.getJsonArray(INSTANCE_TYPE).getString(0).equalsIgnoreCase(EMPTY_VALUE))
                        {
                            result.put(APIConstants.Entity.OBJECT.getName().toLowerCase(), new JsonArray()).put(STATUS, new JsonArray()).put(TIME_STAMP, new JsonArray()).put(DURATION, new JsonArray());

                            for (var object : objects) //Entities From the Qualified Entity...
                            {
                                var item = ObjectStatusCacheStore.getStore().getItem(CommonUtil.getLong(object));

                                if (item != null && !context.getString(VisualizationConstants.GROUP_BY).equalsIgnoreCase(VisualizationConstants.GROUP_APP) && context.getJsonArray(STATUS).contains(item.toLowerCase()))
                                {
                                    updateObjectStatus(result, Status.valueOf(item.toUpperCase()), CommonUtil.getLong(object), ObjectStatusCacheStore.getStore().getTimestamp(CommonUtil.getLong(object)), durations.getJsonObject(CommonUtil.getString(object)).getInteger(EMPTY_VALUE, 0), EMPTY_VALUE, context, builder);
                                }
                            }

                        }

                        else
                        {
                            result.put(APIConstants.Entity.OBJECT.getName().toLowerCase(), new JsonArray()).put(STATUS, new JsonArray()).put(context.getString(VisualizationConstants.GROUP_BY) != null && !context.getString(VisualizationConstants.GROUP_BY).isEmpty() ? context.getString(VisualizationConstants.GROUP_BY) : context.getJsonArray(INSTANCE_TYPE).getString(0), new JsonArray()).put(TIME_STAMP, new JsonArray()).put(DURATION, new JsonArray());

                            for (var object : objects) //Entities From the Qualified Entity...
                            {
                                JsonObject apps = null;

                                var items = ObjectStatusCacheStore.getStore().getInstanceItems(CommonUtil.getLong(object), false);

                                if (items != null)
                                {
                                    if (context.getString(VisualizationConstants.GROUP_BY).equalsIgnoreCase(VisualizationConstants.GROUP_APP))
                                    {
                                        apps = MetricConfigStore.getStore().getAppsByObjectId(CommonUtil.getLong(object));
                                    }

                                    for (var entry : items.entrySet())
                                    {
                                        var status = CommonUtil.getString(entry.getValue());

                                        var timestamp = ObjectStatusCacheStore.getStore().getTimestamp(CommonUtil.getLong(object), entry.getKey());

                                        if (apps != null && apps.containsKey(entry.getKey().split(INSTANCE_SEPARATOR)[1]) && context.getJsonArray(STATUS).contains(status.toLowerCase()))
                                        {
                                            updateObjectStatus(result, Status.valueOf(status.toUpperCase()), CommonUtil.getLong(object), timestamp, durations.getJsonObject(CommonUtil.getString(object)).getInteger(entry.getKey(), 0), apps.getString(entry.getKey().split(INSTANCE_SEPARATOR)[1]), context, builder);
                                        }
                                        else if (!context.getString(VisualizationConstants.GROUP_BY).equalsIgnoreCase(VisualizationConstants.GROUP_APP) && context.getJsonArray(VisualizationConstants.INSTANCE_TYPE).contains(entry.getKey().split(INSTANCE_SEPARATOR)[0]) && context.getJsonArray(STATUS).contains(status.toLowerCase()))
                                        {
                                            updateObjectStatus(result, Status.valueOf(status.toUpperCase()), CommonUtil.getLong(object), timestamp, durations.getJsonObject(CommonUtil.getString(object)).getInteger(entry.getKey(), 0), entry.getKey().split(INSTANCE_SEPARATOR)[1], context, builder);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

                if (result.isEmpty() || !result.containsKey(STATUS) || result.getJsonArray(STATUS).isEmpty())
                {
                    promise.fail(ErrorMessageConstants.INVALID_NO_DATA_FOUND);
                }
                else
                {
                    promise.complete(result);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return promise.future();
    }
}
