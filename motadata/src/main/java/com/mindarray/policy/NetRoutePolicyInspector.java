/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			        Notes
 *  20-Feb-2025		Pruthviraj Jadeja		Initial commit
 *  June-5-2025     <PERSON><PERSON>            Added Support for widget/Alert for Netroute.
 *  05-Aug-2025     Viram                   MOTADATA-6858 Removed redundant code to have atomic operations
 */

package com.mindarray.policy;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.*;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.netroute.NetRouteConstants;
import com.mindarray.notification.MicrosoftTeamsNotification;
import com.mindarray.notification.Notification;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.text.StringSubstitutor;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.InfoMessageConstants.NETROUTE_POLICY_HOP_BY_HOP_DEFAULT_MESSAGE;
import static com.mindarray.InfoMessageConstants.POLICY_NETROUTE_DEFAULT_EMAIL_SUBJECT;
import static com.mindarray.api.APIConstants.ENTITY_ID;
import static com.mindarray.api.MetricPolicy.POLICY_CONDITION;
import static com.mindarray.api.MetricPolicy.POLICY_THRESHOLD;
import static com.mindarray.api.NetRoute.NETROUTE_ID;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.datastore.DatastoreConstants.PluginId.POLICY_NETROUTE;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.netroute.NetRouteConstants.*;
import static com.mindarray.notification.Notification.*;
import static com.mindarray.policy.PolicyEngineConstants.*;

/**
 * NetRoutePolicyInspector is responsible for evaluating network route metrics against defined policies.
 * <p>
 * This class:
 * 1. Loads and manages network route policies from the configuration store
 * 2. Assigns policies to network routes and tags
 * 3. Inspects incoming network route metrics against applicable policies
 * 4. Handles both source-to-destination and hop-by-hop network route types
 * 5. Triggers actions when policy conditions are met
 * 6. Manages policy flaps (state changes) and renotifications
 * <p>
 * The inspector runs as a Vert.x verticle and communicates with other components
 * through the event bus.
 */
public class NetRoutePolicyInspector extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(NetRoutePolicyInspector.class, GlobalConstants.MOTADATA_POLICY, "NetRoute Policy Inspector");
    private static final int INTERVAL_SECONDS = MotadataConfigUtil.getPolicyCleanupTimerSeconds();
    private static final int SETUP_TIMER_SECONDS = MotadataConfigUtil.getPolicySetupTimerSeconds();
    private static final String LOCAL_HOST = MotadataConfigUtil.getHost();
    private final Map<Long, JsonObject> policies = new HashMap<>();
    private final Map<String, JsonObject> policyTimerContexts = new HashMap<>();
    private final Map<String, JsonObject> triggeredPolicies = new HashMap<>();
    private final Map<Long, Set<Long>> suppressedPolicies = new HashMap<>();
    private final Map<Long, Map<String, List<Long>>> policiesByRoute = new HashMap<>();
    private final Map<Long, Map<String, List<Long>>> policiesByTag = new HashMap<>();
    private final StringBuilder builder = new StringBuilder(0);
    private final JsonArray records = new JsonArray();                              // for hop to hop , policy result
    private EventEngine eventEngine;
    private boolean policyTimerEnabled = false;
    private Set<String> mappers;

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        mappers = new HashSet<>();

        try
        {
            var items = NetRoutePolicyConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                var policy = items.getJsonObject(index);

                if ((!policy.containsKey(POLICY_ARCHIVED) || policy.getString(POLICY_ARCHIVED).equalsIgnoreCase(NO)))
                {
                    var policyContext = policy.getJsonObject(POLICY_CONTEXT);

                    if (policyContext.containsKey(ENTITY_TYPE) && policyContext.getString(ENTITY_TYPE).equalsIgnoreCase(Tag.TAG))
                    {
                        policy.put(POLICY_CONTEXT, policyContext.put(ENTITIES, TagConfigStore.getStore().getIdsByItems(policyContext.getJsonArray(ENTITIES))));
                    }

                    policies.put(policy.getLong(ID), policy);
                }
            }

            assign();

            // sending netroute metric to update column mapper
            for (var metric : new String[]{NETROUTE_LATENCY, NETROUTE_MIN_LATENCY, NETROUTE_MAX_LATENCY, NETROUTE_PACKET_LOST_PERCENT})
            {
                builder.append(DatastoreConstants.DataCategory.FLOAT.getName()).append(COLUMN_SEPARATOR).append(DatastoreConstants.PluginId.NETROUTE_METRIC.getName()).append(COLUMN_SEPARATOR).append(metric).append(COLUMN_SEPARATOR).append(NO);

                vertx.eventBus().publish(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_METRIC_COLUMN.name())
                        .put(DatastoreConstants.MAPPER, builder.toString()));

                builder.setLength(0);
            }

            // available for single netroute type , Source to destination
            vertx.eventBus().<JsonObject>localConsumer(EVENT_NETROUTE_POLICY_CLEAR, message ->
            {
                try
                {
                    var event = message.body();

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("policy clear request received : %s ", event.encode()));
                    }

                    if (message.body() != null && !event.isEmpty() && policies.containsKey(event.getLong(POLICY_ID)))
                    {
                        var key = event.getLong(ENTITY_ID) + SEPARATOR + event.getString(METRIC) + SEPARATOR + event.getString(POLICY_EVALUATION_TYPE);

                        if (triggeredPolicies.containsKey(key))
                        {
                            LOGGER.trace(String.format("clearing policy with key : %s ", key));

                            processPolicyFlap(new JsonObject().mergeIn(triggeredPolicies.get(key)).put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()).put(SEVERITY, Severity.CLEAR.name()).put(VALUE, "Manual Clear"));
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

            // available for both netroute type
            vertx.eventBus().<JsonObject>localConsumer(EVENT_NETROUTE_POLICY_SUPPRESS, message ->
            {
                try
                {
                    var event = message.body();

                    message.reply(message.body() != null && suppressedPolicies.containsKey(event.getLong(ENTITY_ID)) && suppressedPolicies.get(event.getLong(ENTITY_ID)).contains(event.getLong(POLICY_ID)));
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

            var timer = new AtomicInteger(SETUP_TIMER_SECONDS);

            vertx.setPeriodic(INTERVAL_SECONDS * 1000L, id ->
            {
                try
                {
                    if (policyTimerEnabled)
                    {
                        timer.addAndGet(-10);

                        if (timer.get() <= 0)
                        {
                            assign();

                            policyTimerEnabled = false;

                            timer.set(SETUP_TIMER_SECONDS);
                        }
                    }

                    policyTimerContexts.forEach((key, value) ->
                    {
                        var policyActions = value.getJsonObject(POLICY_ACTIONS);

                        if (policyActions != null && !policyActions.isEmpty())
                        {
                            var renotification = policyActions.getJsonObject(PolicyTriggerActionType.RENOTIFICATION.getName());

                            if (renotification != null && !renotification.isEmpty())
                            {
                                for (var severity : Severity.values())
                                {
                                    if (renotification.containsKey(severity.name()))
                                    {
                                        renotification.getJsonObject(severity.name()).put(PolicyEngineConstants.TIMER_SECONDS, renotification.getJsonObject(severity.name()).getInteger(PolicyEngineConstants.TIMER_SECONDS) - INTERVAL_SECONDS);
                                    }
                                }
                            }
                        }
                    });
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

            vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
            {
                try
                {
                    var event = message.body();

                    switch (EventBusConstants.ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)))
                    {
                        case ADD_POLICY, UPDATE_POLICY ->
                        {
                            var item = NetRoutePolicyConfigStore.getStore().getItem(event.getLong(ID));

                            if (item != null)
                            {
                                var policyContext = item.getJsonObject(POLICY_CONTEXT);

                                if (policyContext.containsKey(ENTITY_TYPE) && policyContext.getString(ENTITY_TYPE).equalsIgnoreCase(Tag.TAG))
                                {
                                    item.put(POLICY_CONTEXT, policyContext.put(ENTITIES, TagConfigStore.getStore().getIdsByItems(policyContext.getJsonArray(ENTITIES))));
                                }

                                policies.put(event.getLong(ID), item);

                                policyTimerContexts.values().removeIf(value -> value != null && !value.isEmpty() && value.containsKey(ID) && value.getLong(ID).equals(event.getLong(ID)));

                                assign();
                            }
                        }

                        case ADD_NETROUTE -> policyTimerEnabled = true;

                        case DELETE_POLICY ->
                        {
                            policies.remove(event.getLong(ID));

                            policiesByRoute.values().forEach(item ->
                            {
                                for (var policies : item.values())
                                {
                                    policies.remove(event.getLong(ID));
                                }
                            });

                            policiesByTag.values().forEach(item ->
                            {
                                for (var tagPolicies : item.values())
                                {
                                    tagPolicies.remove(event.getLong(ID));
                                }
                            });

                            suppressedPolicies.values().removeIf(item -> item.contains(event.getLong(ID)));

                            triggeredPolicies.values().removeIf(item -> item != null && !item.isEmpty() && item.getLong(ID).equals(event.getLong(ID)));

                            policyTimerContexts.values().removeIf(item -> item != null && !item.isEmpty() && item.containsKey(ID) && item.getLong(ID).equals(event.getLong(ID)));

                            NetRoutePolicyCacheStore.getStore().cleanup(event.getLong(ID));

                            sort();
                        }

                        case DELETE_NETROUTE ->
                        {
                            policiesByRoute.remove(event.getLong(ID));

                            suppressedPolicies.remove(event.getLong(ID));

                            triggeredPolicies.entrySet().removeIf(entry -> CommonUtil.getLong(entry.getKey().split(SEPARATOR_WITH_ESCAPE)[0].trim()) == event.getLong(ID));

                            policyTimerContexts.entrySet().removeIf(entry -> CommonUtil.getLong(entry.getKey().split(SEPARATOR_WITH_ESCAPE)[0].trim()) == event.getLong(ID));

                            NetRoutePolicyCacheStore.getStore().deleteItem(event.getLong(ID));
                        }

                        case SUPPRESS_POLICY ->
                        {
                            if (event.containsKey(ENTITY_ID) && policies.containsKey(event.getLong(POLICY_ID)))
                            {
                                var id = event.getLong(ENTITY_ID);

                                if (!suppressedPolicies.containsKey(id))
                                {
                                    suppressedPolicies.put(id, new HashSet<>());
                                }

                                suppressedPolicies.get(id).add(event.getLong(POLICY_ID));
                            }
                        }

                        case UNSUPPRESS_POLICY ->
                        {
                            if (event.containsKey(ENTITY_ID) && suppressedPolicies.containsKey(event.getLong(ENTITY_ID)))
                            {
                                var id = event.getLong(ENTITY_ID);

                                suppressedPolicies.get(id).remove(event.getLong(POLICY_ID));

                                if (suppressedPolicies.get(id).isEmpty())
                                {
                                    suppressedPolicies.remove(id);
                                }
                            }
                        }

                        default ->
                        {
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

            }).exceptionHandler(LOGGER::error);

            eventEngine = new EventEngine().setEventType(config().getString(EventBusConstants.EVENT_TYPE))
                    .setPersistEventOffset(true).setLogger(LOGGER).setEventHandler(this::inspect).start(vertx, promise);

            promise.complete();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }
    }

    private void assign()
    {
        try
        {
            policiesByRoute.clear();

            policiesByTag.clear();

            policies.forEach((key, value) ->
            {
                try
                {
                    var policyContext = value.getJsonObject(POLICY_CONTEXT);

                    var metric = value.getString(POLICY_TYPE) + SEPARATOR + policyContext.getString(METRIC) + SEPARATOR + policyContext.getString(POLICY_EVALUATION_TYPE);

                    JsonArray entities;

                    var policyByPath = true;

                    if (policyContext.getJsonArray(ENTITIES) != null && !policyContext.getJsonArray(ENTITIES).isEmpty())
                    {
                        entities = policyContext.getJsonArray(ENTITIES);

                        if (policyContext.getString(ENTITY_TYPE).equalsIgnoreCase(Tag.TAG))
                        {
                            policyByPath = false;
                        }
                    }
                    else
                    {
                        entities = NetRouteConfigStore.getStore().getIds();
                    }

                    assign(value.getLong(ID), metric, entities, policyByPath ? policiesByRoute : policiesByTag);
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

            sort();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void assign(long policyId, String metricKey, JsonArray entities, Map<Long, Map<String, List<Long>>> policiesByEntity)
    {
        try
        {
            for (var i = 0; i < entities.size(); i++)
            {
                var object = entities.getLong(i);

                if (!policiesByEntity.containsKey(object))
                {
                    policiesByEntity.put(object, new HashMap<>());

                    policiesByEntity.get(object).put(metricKey, new ArrayList<>());
                }

                else if (!policiesByEntity.get(object).containsKey(metricKey))
                {
                    policiesByEntity.get(object).put(metricKey, new ArrayList<>());
                }

                if (!policiesByEntity.get(object).get(metricKey).contains(policyId))
                {
                    policiesByEntity.get(object).get(metricKey).add(policyId);
                }
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void sort()
    {
        try
        {
            for (var values : policiesByRoute.values())
            {
                for (var entry : values.entrySet())
                {
                    entry.getValue().sort(Comparator.comparing(item -> this.policies.get(CommonUtil.getLong(item)).getLong(POLICY_CREATION_TIME)).reversed());
                }
            }

            for (var values : policiesByTag.values())
            {
                for (var entry : values.entrySet())
                {
                    entry.getValue().sort(Comparator.comparing(item -> this.policies.get(CommonUtil.getLong(item)).getLong(POLICY_CREATION_TIME)).reversed());
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Inspects incoming network route metrics against applicable policies.
     * This method processes network route metrics, finds applicable policies,
     * evaluates metrics against policy conditions, and triggers actions when conditions are met.
     *
     * @param event The event containing network route metrics and metadata
     */
    private void inspect(JsonObject event)
    {
        try
        {
            var item = NetRouteConfigStore.getStore().getItem(event.getLong(NETROUTE_ID), false);

            var id = item.getLong(ID);

            var entries = event.getJsonObject(RESULT);

            var context = new JsonObject().put(NETROUTE_ID, id).put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()));

            if (CommonUtil.debugEnabled())
            {
                LOGGER.debug(String.format("Inspecting netroute : %s ", item.getString(NetRoute.NETROUTE_NAME)));
            }


            var qualifiedPolicies = qualify(id, item.getJsonArray(NetRoute.NETROUTE_TAGS));

            if (!qualifiedPolicies.isEmpty())
            {
                for (var entry : entries.getMap().entrySet())
                {
                    if (qualifiedPolicies.containsKey(entry.getKey()))
                    {
                        var results = entries.getJsonArray(entry.getKey());

                        qualifiedPolicies.get(entry.getKey()).forEach((key, value) ->
                        {
                            if (!value.isEmpty())
                            {
                                var metric = key.split(SEPARATOR_WITH_ESCAPE)[1];

                                var policyId = value.stream().findFirst().get();

                                var policy = policies.get(policyId);

                                if (policy.getString(POLICY_STATE).equalsIgnoreCase(YES) && (!suppressedPolicies.containsKey(id) || (suppressedPolicies.containsKey(id) && !suppressedPolicies.get(id).contains(policyId))))
                                {
                                    LOGGER.info(String.format("Policy : %s qualified for netroute : %s for metric : %s ", policy.getString(POLICY_NAME), item.getString(NetRoute.NETROUTE_NAME), metric));

                                    var entity = new JsonObject();

                                    for (var index = 0; index < results.size(); index++)
                                    {
                                        var result = results.getJsonObject(index);

                                        if (result.containsKey(metric))
                                        {
                                            var policyContext = policy.getJsonObject(POLICY_CONTEXT);

                                            context.put(GlobalConstants.METRIC, metric).put(ID, policyId).put(POLICY_NAME, policy.getString(POLICY_NAME)).put(ENTITY_ID, event.getLong(NETROUTE_ID)).put(POLICY_EVALUATION_TYPE, policyContext.getString(POLICY_EVALUATION_TYPE)).put(POLICY_TYPE, policy.getString(POLICY_TYPE));

                                            result.getMap().computeIfAbsent(SEVERITIES, val -> new JsonArray());

                                            inspect(policyContext, result, metric, context, entity);
                                        }
                                    }

                                    if (!entity.isEmpty())
                                    {
                                        processPolicyFlap(entity);

                                        if (entity.getString(POLICY_EVALUATION_TYPE).equalsIgnoreCase(NetRouteType.HOP_BY_HOP.getName()))
                                        {
                                            send(event, entity);
                                        }
                                    }

                                    records.clear();
                                }
                            }
                        });
                    }
                }
            }
            else
            {
                LOGGER.info(String.format("No policy qualified for netroute : %s ", item.getString(NetRoute.NETROUTE_NAME)));
            }

            vertx.eventBus().send(EVENT_NETROUTE_POLICY_RESPONSE, event);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void send(JsonObject event, JsonObject context)
    {
        if (!records.isEmpty())
        {
            var policy = policies.get(context.getLong(ID));

            var message = String.format(NETROUTE_POLICY_HOP_BY_HOP_DEFAULT_MESSAGE, context.getString(METRIC), policy.getString(POLICY_SEVERITY), PolicyEngineConstants.toOperatorLiteral(context.getString(POLICY_CONDITION)), context.getString(POLICY_THRESHOLD));

            var id = CommonUtil.newEventId();

            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_NETROUTE_POLICY_SEVERITY_DURATION_CALCULATE, context);

            DatastoreConstants.write(new JsonObject().put(SEVERITY, policy.getString(POLICY_SEVERITY, EMPTY_VALUE))
                    .put(ID, id).put(MESSAGE, message).put(POLICY_TYPE, policy.getString(POLICY_TYPE))
                    .put(POLICY_ID, policy.getLong(ID)).put(EVENT_FIELD, context.getString(METRIC))
                    .put(GlobalConstants.PLUGIN_ID, POLICY_NETROUTE.getName()).put(NETROUTE_ID, context.getLong(NETROUTE_ID))
                    .put(EventBusConstants.EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()))
                    .put(DatastoreConstants.DATASTORE_TYPE, DatastoreConstants.DatastoreType.EVENT_POLICY.ordinal())
                    .put(EVENT_SOURCE, LOCAL_HOST), VisualizationConstants.VisualizationDataSource.POLICY.getName(), mappers, builder);

            DatastoreConstants.write(new JsonObject()
                    .put(POLICY_TRIGGER_ID, id).put(SEVERITY, policy.getString(POLICY_SEVERITY, EMPTY_VALUE)).put(POLICY_TRIGGER_EVALUATION_WINDOW, EMPTY_VALUE)
                    .put(POLICY_TRIGGER_VALUE, new JsonObject().put(RESULT, records.encode()))
                    .put(POLICY_TRIGGER_POLICY_ID, policy.getLong(ID)).put(GlobalConstants.PLUGIN_ID, DatastoreConstants.PluginId.POLICY_RESULT.getName())
                    .put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds()))
                    .put(DatastoreConstants.DATASTORE_TYPE, DatastoreConstants.DatastoreType.POLICY_RESULT.ordinal())
                    .put(EVENT_SOURCE, LOCAL_HOST), VisualizationConstants.VisualizationDataSource.POLICY_RESULT.getName(), mappers, builder);
        }
    }

    /**
     * Processes policy state changes (flaps) and triggers appropriate actions.
     * This method handles the core logic of policy state transitions, including:
     * - Recording policy trigger events
     * - Managing triggered policies
     * - Delegating to action handlers
     *
     * @param context The context containing policy state information
     */
    private void processPolicyFlap(JsonObject context)
    {
        var policy = policies.get(context.getLong(ID));

        var entityId = context.getLong(ENTITY_ID);

        var severity = context.getString(SEVERITY);

        context.put(POLICY_KEY, entityId + SEPARATOR + context.getString(METRIC) + SEPARATOR + context.getString(POLICY_EVALUATION_TYPE));

        if (context.getString(POLICY_EVALUATION_TYPE).equalsIgnoreCase(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName()))
        {
            var policyKey = context.getString(POLICY_KEY);

            var trigger = false;

            var clearSeverity = severity.equalsIgnoreCase(Severity.CLEAR.name());

            if (triggeredPolicies.containsKey(policyKey))
            {
                var triggeredContext = triggeredPolicies.get(policyKey);

                if (!triggeredContext.getString(SEVERITY).equalsIgnoreCase(severity))
                {
                    trigger = true;
                }

                context = triggeredContext.mergeIn(context);
            }
            else
            {
                if (!clearSeverity)
                {
                    trigger = true;
                }
            }

            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_NETROUTE_POLICY_SEVERITY_DURATION_CALCULATE, context);

            NetRoutePolicyCacheStore.getStore().updateItem(context.getString(POLICY_KEY).hashCode(), context);

            triggeredPolicies.put(policyKey, context);

            triggerAction(policy, context, severity, policyKey, entityId, trigger);
        }
        else
        {
            context.mergeIn(policy.getJsonObject(POLICY_CONTEXT).getJsonObject(POLICY_SEVERITY).getJsonObject(severity));

            triggerAction(policy, context, policy.getString(POLICY_SEVERITY), null, entityId, true);

            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_POLICY_NOTIFICATION, context.put(PolicyEngineConstants.POLICY_ID, context.getLong(ID))
                    .put(NetRoute.NETROUTE_NAME, NetRouteConfigStore.getStore().getItem(entityId, false).getString(NetRoute.NETROUTE_NAME)));
        }
    }

    private void triggerAction(JsonObject policy, JsonObject context, String severity, String policyKey, long entityId, boolean trigger)
    {
        Set<String> renotificationEmailRecipients = null;

        Set<String> renotificationSMSRecipients = null;

        Map<String, Set<String>> renotificationChannelRecipients = null;

        var policyActions = policy.getJsonObject(POLICY_ACTIONS, null);

        if (policyActions != null && !policyActions.isEmpty() && context.getString(POLICY_EVALUATION_TYPE).equalsIgnoreCase(NetRouteType.SOURCE_TO_DESTINATION.getName()))
        {
            var renotification = policyActions.getJsonObject(PolicyTriggerActionType.RENOTIFICATION.getName(), null);

            if (renotification != null && !renotification.isEmpty() && renotification.containsKey(severity) && !renotification.getJsonObject(severity).isEmpty())
            {
                renotificationEmailRecipients = new HashSet<>();

                renotificationSMSRecipients = new HashSet<>();

                renotificationChannelRecipients = new HashMap<>();

                updateRenotificationTimer(severity, policyKey, policyTimerContexts, policy, renotificationEmailRecipients, renotificationSMSRecipients, renotificationChannelRecipients);
            }
        }

        var message = policy.getString(POLICY_MESSAGE);

        var actions = policy.getJsonObject(POLICY_ACTIONS);

        var item = NetRouteConfigStore.getStore().getItem(context.getLong(NETROUTE_ID), false);

        if (trigger)
        {
            // notification
            if (actions != null && actions.containsKey(PolicyTriggerActionType.NOTIFICATION.getName()) && !actions.getJsonObject(PolicyTriggerActionType.NOTIFICATION.getName()).isEmpty())
            {
                try
                {
                    var notificationContext = actions.getJsonObject(PolicyTriggerActionType.NOTIFICATION.getName());

                    // email & sms

                    var emailContext = notificationContext.getJsonObject(Notification.NotificationType.EMAIL.getName());

                    if (emailContext != null && !emailContext.isEmpty() && emailContext.containsKey(severity) && !emailContext.getJsonArray(severity).isEmpty())
                    {
                        var emailRecipients = new HashSet<String>();

                        var smsRecipients = new HashSet<String>();

                        setRecipients(emailRecipients, smsRecipients, notificationContext.getJsonObject(Notification.NotificationType.EMAIL.getName()).getJsonArray(severity));

                        if (!emailRecipients.isEmpty())
                        {
                            var targets = new JsonArray(new ArrayList(emailRecipients));

                            var subject = replaceNetRoutePolicyPlaceholders(policy, context, POLICY_NETROUTE_DEFAULT_EMAIL_SUBJECT, message, item, LOGGER);

                            context.put(RunbookPlugin.RUNBOOK_PLUGIN_NOTIFICATION_EMAIL_RECIPIENTS, targets);

                            context.put(RunbookPlugin.RUNBOOK_PLUGIN_NOTIFICATION_EMAIL_SUBJECT, subject);

                            JsonObject emailBody;

                            String template;

                            if (context.getString(POLICY_EVALUATION_TYPE).equalsIgnoreCase(NetRouteType.SOURCE_TO_DESTINATION.getName()))
                            {
                                context.put(POLICY_MESSAGE, replaceNetRoutePolicyPlaceholders(policy, context, NETROUTE_SOURCE_TO_DESTINATION_POLICY_EMAIL_NOTIFICATION_DEFAULT_MESSAGE, message, item, LOGGER));

                                template = Notification.EMAIL_NOTIFICATION_NETROUTE_POLICY_SOURCE_TO_DESTINATION_HTML_TEMPLATE;

                                emailBody = new JsonObject().mergeIn(item).mergeIn(context).put(TIME_STAMP, DateTimeUtil.timestamp(context.getLong(EVENT_TIMESTAMP) * 1000L)).put(NetRoute.NETROUTE_SOURCE, ObjectConfigStore.getStore().getItemByAgentId(item.getLong(NetRoute.NETROUTE_SOURCE)).getString(AIOpsObject.OBJECT_HOST)).put(POLICY_NAME, policy.getString(POLICY_NAME)).put(SEVERITY, context.getString(SEVERITY).toLowerCase());
                            }
                            else
                            {
                                context.put(POLICY_MESSAGE, replaceNetRoutePolicyPlaceholders(policy, context, NETROUTE_HOP_BY_HOP_POLICY_EMAIL_NOTIFICATION_DEFAULT_MESSAGE, message, item, LOGGER));

                                template = Notification.EMAIL_NOTIFICATION_NETROUTE_POLICY_HOP_BY_HOP_HTML_TEMPLATE;

                                emailBody = new JsonObject().mergeIn(item)
                                        .mergeIn(context)
                                        .put(TIME_STAMP, DateTimeUtil.timestamp(context.getLong(EVENT_TIMESTAMP) * 1000L))
                                        .put(NetRoute.NETROUTE_SOURCE, ObjectConfigStore.getStore().getItemByAgentId(item.getLong(NetRoute.NETROUTE_SOURCE)).getString(AIOpsObject.OBJECT_HOST))
                                        .put(POLICY_NAME, policy.getString(POLICY_NAME))
                                        .put("records", new JsonArray(records.getList()))
                                        .put(SEVERITY, context.getString(SEVERITY).toLowerCase());
                            }

                            Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_NOTIFICATION, new JsonObject()
                                    .put(EventBusConstants.EVENT_REPLY, YES).put(NOTIFICATION_TYPE, Notification.NotificationType.EMAIL.getName())
                                    .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                                    .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add(context.getString(SEVERITY).toLowerCase() + ".png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                                    .put(Notification.EMAIL_NOTIFICATION_SUBJECT, subject)
                                    .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, targets)
                                    .put(TEMPLATE_NAME, template)
                                    .put(Notification.EMAIL_NOTIFICATION_CONTENT, emailBody), reply ->
                            {
                                try
                                {
                                    if (reply.succeeded())
                                    {
                                        LOGGER.info(String.format("email triggered for policy : %s with recipients : %s ", policy.getString(POLICY_NAME), targets.encode()));

                                        writeTriggeredActionEvent(targets, context, entityId, policy.getLong(ID), reply.result().body().put(RUNBOOK_WORKLOG_TYPE, PolicyTriggerActionType.NOTIFICATION.getName()), LOGGER, mappers, builder);
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            });
                        }

                        if (!smsRecipients.isEmpty())
                        {
                            var targets = new JsonArray(new ArrayList(smsRecipients));

                            if (context.getString(POLICY_EVALUATION_TYPE).equalsIgnoreCase(NetRouteType.SOURCE_TO_DESTINATION.getName()))
                            {
                                context.put(POLICY_MESSAGE, replaceNetRoutePolicyPlaceholders(policy, context, NETROUTE_SOURCE_TO_DESTINATION_POLICY_EMAIL_NOTIFICATION_DEFAULT_MESSAGE, message, item, LOGGER));
                            }
                            else
                            {
                                context.put(POLICY_MESSAGE, replaceNetRoutePolicyPlaceholders(policy, context, NETROUTE_HOP_BY_HOP_POLICY_EMAIL_NOTIFICATION_DEFAULT_MESSAGE, message, item, LOGGER));
                            }

                            Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_REPLY, YES).put(NOTIFICATION_TYPE, Notification.NotificationType.SMS.getName()).put(Notification.SMS_NOTIFICATION_RECIPIENTS, targets)
                                    .put(Notification.SMS_NOTIFICATION_MESSAGE, context.getString(POLICY_MESSAGE)), reply ->
                            {
                                try
                                {
                                    if (reply.succeeded())
                                    {
                                        LOGGER.info(String.format("sms triggered for policy : %s with recipients : %s ", policy.getString(POLICY_NAME), targets.encode()));

                                        writeTriggeredActionEvent(targets, context, entityId, policy.getLong(ID), reply.result().body().put(RUNBOOK_WORKLOG_TYPE, PolicyTriggerActionType.NOTIFICATION.getName()), LOGGER, mappers, builder);
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            });
                        }
                    }

                    // sound
                    var soundContext = notificationContext.getJsonObject(Notification.NotificationType.SOUND.getName());

                    if (soundContext != null && !soundContext.isEmpty() && soundContext.containsKey(severity))
                    {
                        Notification.playSound(new JsonObject().mergeIn(context));
                    }

                    var channelContext = notificationContext.getJsonObject(CHANNELS);

                    if (channelContext != null && !channelContext.isEmpty() && channelContext.containsKey(severity) && !channelContext.getJsonArray(severity).isEmpty())
                    {
                        if (context.getString(POLICY_EVALUATION_TYPE).equalsIgnoreCase(NetRouteType.SOURCE_TO_DESTINATION.getName()))
                        {
                            context.put(POLICY_MESSAGE, replaceNetRoutePolicyPlaceholders(policy, context, NETROUTE_SOURCE_TO_DESTINATION_POLICY_EMAIL_NOTIFICATION_DEFAULT_MESSAGE, message, item, LOGGER));
                        }
                        else
                        {
                            context.put(POLICY_MESSAGE, replaceNetRoutePolicyPlaceholders(policy, context, NETROUTE_HOP_BY_HOP_POLICY_EMAIL_NOTIFICATION_DEFAULT_MESSAGE, message, item, LOGGER));
                        }

                        var recipients = actions.getJsonObject(PolicyTriggerActionType.NOTIFICATION.getName()).getJsonObject(CHANNELS).getJsonArray(context.getString(SEVERITY));

                        var channelRecipients = new HashMap<String, Set<String>>();

                        for (var index = 0; index < recipients.size(); index++)
                        {
                            var recipient = recipients.getJsonObject(index);

                            var integration = IntegrationConfigStore.getStore().getItem(recipient.getLong(IntegrationProfile.INTEGRATION));

                            if (integration != null)
                            {
                                channelRecipients.computeIfAbsent(integration.getString(Integration.INTEGRATION_TYPE), key -> new HashSet<>()).add(recipient.getString(ID));
                            }
                        }

                        var runbook = new HashSet<String>();

                        if (actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()) != null && !actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).isEmpty()
                                && actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).containsKey(severity) && !actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).getJsonArray(severity).isEmpty())
                        {
                            var entries = actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).getJsonArray(severity);

                            for (var index = 0; index < entries.size(); index++)
                            {
                                if (RunbookPluginConfigStore.getStore().existItem(entries.getJsonObject(index).getLong(ID)))
                                {
                                    runbook.add(RunbookPluginConfigStore.getStore().getItem(entries.getJsonObject(index).getLong(ID)).getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME));
                                }
                            }
                        }

                        channelRecipients.forEach((channelType, targets) ->
                        {
                            if (!targets.isEmpty())
                            {
                                Notification.send(new JsonObject().mergeIn(context).put(NOTIFICATION_TYPE, channelType).put(CHANNELS, new JsonArray(new ArrayList<>(targets)))
                                        .put(Notification.CHANNEL_NOTIFICATION_CONTENT, new StringSubstitutor(new JsonObject().mergeIn(NetRouteConfigStore.getStore().getItem(context.getLong(NETROUTE_ID))).mergeIn(context).put(TIME_STAMP, DateTimeUtil.timestamp(context.getLong(EVENT_TIMESTAMP) * 1000L))
                                                .put(POLICY_NAME, policy.getString(POLICY_NAME)).put(SEVERITY, context.getString(SEVERITY).toLowerCase()).put("policy.action", String.join(", ", runbook))
                                                .getMap()).replace(Notification.getChannelNotificationTemplate(policy.getString(POLICY_TYPE), channelType, false))));
                            }
                        });
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }
        }

        // renotification
        if (actions != null && context.getString(POLICY_EVALUATION_TYPE).equalsIgnoreCase(NetRouteConstants.NetRouteType.SOURCE_TO_DESTINATION.getName()) && renotificationEmailRecipients != null && renotificationSMSRecipients != null && (!renotificationEmailRecipients.isEmpty() || !renotificationSMSRecipients.isEmpty()))
        {
            var key = context.getLong(ENTITY_ID) + SEPARATOR + context.getLong(ID) + SEPARATOR + (context.getString(GlobalConstants.METRIC));

            var duration = NetRoutePolicyFlapDurationCacheStore.getStore().getFlapTick(key) > 0 ? DateTimeUtil.getTimeStampFromEpochTimestamp(NetRoutePolicyFlapDurationCacheStore.getStore().getFlapTick(key), true) : EMPTY_VALUE;

            var acknowledged = NetRoutePolicyFlapDurationCacheStore.getStore().getAcknowledgment(key) != null ? NetRoutePolicyFlapDurationCacheStore.getStore().getAcknowledgment(key) : NO;

            if (actions.containsKey(PolicyTriggerActionType.RENOTIFICATION.getName()) &&
                    (actions.getJsonObject(PolicyTriggerActionType.RENOTIFICATION.getName()).getString(RENOTIFY_ACKNOWLEDGED).equalsIgnoreCase(NO)
                            || (actions.getJsonObject(PolicyTriggerActionType.RENOTIFICATION.getName()).getString(RENOTIFY_ACKNOWLEDGED).equalsIgnoreCase(YES) && acknowledged.equalsIgnoreCase(NO))))
            {
                if (!renotificationEmailRecipients.isEmpty())
                {
                    var emailRecipients = new JsonArray(new ArrayList(renotificationEmailRecipients));

                    context.put(POLICY_MESSAGE, replaceNetRoutePolicyPlaceholders(policy, context, NETROUTE_SOURCE_TO_DESTINATION_POLICY_EMAIL_NOTIFICATION_DEFAULT_MESSAGE, message, item, LOGGER));

                    Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_REPLY, YES).put(NOTIFICATION_TYPE, Notification.NotificationType.EMAIL.getName())
                            .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                            .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add(severity.toLowerCase() + ".png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                            .put(Notification.EMAIL_NOTIFICATION_SUBJECT, replaceNetRoutePolicyPlaceholders(policy, context, POLICY_NETROUTE_DEFAULT_EMAIL_SUBJECT, message, item, LOGGER))
                            .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, emailRecipients)
                            .put(Notification.TEMPLATE_NAME, EMAIL_RENOTIFICATION_NETROUTE_POLICY_HTML_TEMPLATE)
                            .put(Notification.EMAIL_NOTIFICATION_CONTENT, new JsonObject().mergeIn(item).mergeIn(context).put("active.since", duration).put(NetRoute.NETROUTE_SOURCE, ObjectConfigStore.getStore().getItemByAgentId(item.getLong(NetRoute.NETROUTE_SOURCE)).getString(AIOpsObject.OBJECT_HOST)).put(TIME_STAMP, DateTimeUtil.timestamp(context.getLong(EVENT_TIMESTAMP) * 1000L)).put(POLICY_NAME, policy.getString(POLICY_NAME)).put(SEVERITY, context.getString(SEVERITY).toLowerCase())), reply ->
                    {
                        try
                        {
                            if (reply.succeeded())
                            {
                                LOGGER.info(String.format("re-notification email triggered for policy : %s with recipients : %s ", policy.getString(POLICY_NAME), emailRecipients.encode()));

                                writeTriggeredActionEvent(emailRecipients, context, entityId, policy.getLong(ID), reply.result().body().put(RUNBOOK_WORKLOG_TYPE, PolicyTriggerActionType.NOTIFICATION.getName()), LOGGER, mappers, builder);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    });
                }

                if (!renotificationSMSRecipients.isEmpty())
                {
                    var smsRecipients = new JsonArray(new ArrayList(renotificationSMSRecipients));

                    context.put(POLICY_MESSAGE, replaceNetRoutePolicyPlaceholders(policy, context, NETROUTE_SOURCE_TO_DESTINATION_POLICY_EMAIL_NOTIFICATION_DEFAULT_MESSAGE, message, item, LOGGER));

                    Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_REPLY, YES).put(NOTIFICATION_TYPE, Notification.NotificationType.SMS.getName()).put(Notification.SMS_NOTIFICATION_RECIPIENTS, smsRecipients)
                            .put(Notification.SMS_NOTIFICATION_MESSAGE, context.getString(POLICY_MESSAGE)), reply ->
                    {
                        try
                        {
                            if (reply.succeeded())
                            {
                                LOGGER.info(String.format("re-notification sms triggered for policy : %s with recipients : %s ", policy.getString(POLICY_NAME), smsRecipients.encode()));

                                writeTriggeredActionEvent(smsRecipients, context, entityId, policy.getLong(ID), reply.result().body().put(RUNBOOK_WORKLOG_TYPE, PolicyTriggerActionType.NOTIFICATION.getName()), LOGGER, mappers, builder);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    });
                }

                if (!renotificationChannelRecipients.isEmpty())
                {
                    var runbook = new HashSet<String>();

                    var runbookContext = actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName());

                    if (runbookContext != null && !runbookContext.isEmpty() && runbookContext.containsKey(severity) && !runbookContext.getJsonArray(severity).isEmpty())
                    {
                        var entries = runbookContext.getJsonArray(severity);

                        for (var index = 0; index < entries.size(); index++)
                        {
                            if (RunbookPluginConfigStore.getStore().existItem(entries.getJsonObject(index).getLong(ID)))
                            {
                                runbook.add(RunbookPluginConfigStore.getStore().getItem(entries.getJsonObject(index).getLong(ID)).getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME));
                            }
                        }
                    }

                    renotificationChannelRecipients.forEach((channelType, targets) ->
                    {
                        if (!targets.isEmpty())
                        {
                            Notification.send(new JsonObject().mergeIn(context).put(NOTIFICATION_TYPE, channelType).put(CHANNELS, new JsonArray(new ArrayList<>(targets)))
                                    .put(Notification.CHANNEL_NOTIFICATION_CONTENT, new StringSubstitutor(new JsonObject().mergeIn(NetRouteConfigStore.getStore().getItem(context.getLong(NETROUTE_ID))).mergeIn(context).put(TIME_STAMP, DateTimeUtil.timestamp(context.getLong(EVENT_TIMESTAMP) * 1000L)).put(POLICY_NAME, policy.getString(POLICY_NAME)).put(SEVERITY, context.getString(SEVERITY).toLowerCase()).put("active.since", duration).put("policy.action", String.join(", ", runbook)).put("severity.color", MicrosoftTeamsNotification.MicrosoftTeamsColorCode.valueOf(context.getString(SEVERITY)).getColorCode()).getMap()).replace(Notification.getChannelNotificationTemplate(policy.getString(POLICY_TYPE), channelType, true))));
                        }
                    });
                }
            }
        }
    }

    private void writeTriggeredActionEvent(JsonArray recipients, JsonObject context, long entityId, long policyId, JsonObject result, Logger logger, Set<String> mappers, StringBuilder builder)
    {
        var value = EMPTY_VALUE;

        try
        {

            if (PolicyTriggerActionType.valueOfName(result.getString(RUNBOOK_WORKLOG_TYPE)) == PolicyTriggerActionType.RUNBOOK)
            {
                var response = result.containsKey(EventBusConstants.EVENT_REPLY_CONTEXTS) && !result.getJsonArray(EventBusConstants.EVENT_REPLY_CONTEXTS).isEmpty() ? result.getJsonArray(EventBusConstants.EVENT_REPLY_CONTEXTS).getJsonObject(0) : null;

                if (response != null && !response.isEmpty())
                {
                    if (response.containsKey(RESULT))
                    {
                        value = new JsonObject().put(RESULT, CommonUtil.getString(response.getValue(RESULT))).encode();
                    }

                    result.put(STATUS, response.getValue(STATUS));
                }

                result.put(RUNBOOK_WORKLOG_TYPE, PolicyTriggerActionType.RUNBOOK.ordinal());
            }

            else
            {

                value = CommonUtil.getString(result.getJsonArray("recipients"));

                if (value == null)
                {
                    value = CommonUtil.getString(recipients);
                }

                result.put(RUNBOOK_WORKLOG_TYPE, PolicyTriggerActionType.NOTIFICATION.ordinal());
            }

            var error = result.getString(ERROR);

            DatastoreConstants.write(new JsonObject().put(RUNBOOK_WORKLOG_STATUS, result.getValue(STATUS, STATUS_FAIL))
                            .put(RUNBOOK_WORKLOG_RESULT, value.length() > RUNBOOK_WORKLOG_RESULT_MAX_BYTES ? value.substring(0, RUNBOOK_WORKLOG_RESULT_MAX_BYTES) : value)
                            .put(RUNBOOK_WORKLOG_ID, result.containsKey(ID) ? result.getLong(ID) : 0)
                            .put(RUNBOOK_WORKLOG_TYPE, result.getValue(RUNBOOK_WORKLOG_TYPE))
                            .put(POLICY_ID, policyId).put(AIOpsObject.OBJECT_ID, 0).put(NETROUTE_ID, entityId)
                            .put(GlobalConstants.PLUGIN_ID, DatastoreConstants.PluginId.RUNBOOK_WORKLOG.getName())
                            .put(EVENT_TIMESTAMP, context.getLong(EVENT_TIMESTAMP))
                            .put(DatastoreConstants.DATASTORE_TYPE, DatastoreConstants.DatastoreType.RUNBOOK_WORKLOG.ordinal())
                            .put(USER_NAME, DEFAULT_USER).put(RUNBOOK_WORKLOG_ERROR, error != null ? (error.length() > RUNBOOK_WORKLOG_RESULT_MAX_BYTES ? error.substring(0, RUNBOOK_WORKLOG_RESULT_MAX_BYTES) : error) : EMPTY_VALUE)
                            .put(EVENT_SOURCE, LOCAL_HOST)
                    , VisualizationConstants.VisualizationDataSource.RUNBOOK_WORKLOG.getName(), mappers, builder);
        }

        catch (Exception exception)
        {
            logger.error(exception);
        }
    }

    private void inspect(JsonObject policyContext, JsonObject result, String metric, JsonObject context, JsonObject item)
    {
        var availabilityPolicy = metric.equalsIgnoreCase(STATUS);

        if (!availabilityPolicy)
        {
            result.put(metric, CommonUtil.getFloat(result.getValue(metric)));
        }

        var response = evaluateCondition(result.getValue(metric), policyContext.getJsonObject(POLICY_SEVERITY), availabilityPolicy);

        if (response.isEmpty() && !policyContext.getString(POLICY_EVALUATION_TYPE).equalsIgnoreCase(NetRouteType.HOP_BY_HOP.getName()))
        {
            response = new JsonObject().put(SEVERITY, Severity.CLEAR.name());
        }

        if (!response.isEmpty())
        {
            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace(String.format("Policy : %s triggered for metric : %s with value : %s and severity : %s ", context.getString(POLICY_NAME), metric, result.getString(metric), response.getString(SEVERITY)));
            }

            result.getJsonArray(SEVERITIES).add(Severity.valueOf(response.getString(SEVERITY)));

            records.add(new JsonObject().put(SEVERITY, response.getString(SEVERITY)).put(METRIC, metric).put(VALUE, result.getValue(metric)).put(NetRoute.NETROUTE_SOURCE, result.getString(NetRoute.NETROUTE_SOURCE)).put(NetRoute.NETROUTE_DESTINATION_IP, result.getString(NetRoute.NETROUTE_DESTINATION_IP)));

            item.mergeIn(response).mergeIn(context).put(VALUE, result.getValue(metric));
        }
    }

    /**
     * Evaluates metric values against policy conditions to determine if a policy should be triggered.
     * Handles different policy types (availability, performance) and severity levels.
     *
     * @param value              The metric value to evaluate
     * @param conditions         The policy conditions to evaluate against
     * @param availabilityPolicy Flag indicating if this is an availability policy
     * @return A JsonObject containing the evaluation result, including severity and threshold if conditions are met
     */
    private JsonObject evaluateCondition(Object value, JsonObject conditions, boolean availabilityPolicy)
    {
        var result = new JsonObject();

        //for availability policy
        if (availabilityPolicy)
        {
            if (PolicyEngineConstants.evaluateCondition(Boolean.TRUE, Operator.EQUAL.getName(), STATUS_DOWN, value))
            {
                result.put(SEVERITY, Severity.DOWN.name());

                result.put(POLICY_THRESHOLD, STATUS_DOWN);
            }
            else if (PolicyEngineConstants.evaluateCondition(Boolean.TRUE, Operator.EQUAL.getName(), STATUS_UP, value))
            {
                result.put(SEVERITY, Severity.CLEAR.name());

                result.put(POLICY_THRESHOLD, STATUS_UP);
            }
        }
        else
        {
            if (conditions != null)
            {
                for (var index = 0; index < POLICY_SEVERITIES.size(); index++)
                {
                    var severity = POLICY_SEVERITIES.getString(index);

                    var condition = conditions.getJsonObject(severity, null);

                    if (condition != null && PolicyEngineConstants.evaluateCondition(Boolean.TRUE, condition.getString(POLICY_CONDITION), condition.getValue(POLICY_THRESHOLD), value))
                    {
                        result.put(SEVERITY, Severity.valueOf(severity));

                        result.put(POLICY_THRESHOLD, CommonUtil.getString(condition.getValue(POLICY_THRESHOLD)));

                        break;
                    }
                }
            }
        }

        return result;
    }

    private Map<String, Map<String, Set<Long>>> qualify(long id, JsonArray tags)
    {
        var qualifiedPolicies = new HashMap<String, Map<String, Set<Long>>>();

        var keys = new HashMap<String, Set<String>>();

        policiesByRoute.getOrDefault(id, Collections.emptyMap()).forEach((key, policyIds) ->
        {
            if (!policyIds.isEmpty())
            {
                var type = key.split(SEPARATOR_WITH_ESCAPE)[2];

                qualifiedPolicies.computeIfAbsent(type, value -> new HashMap<>())
                        .computeIfAbsent(key, value -> new LinkedHashSet<>()).addAll(policyIds);
            }
        });

        if (tags != null && !tags.isEmpty())
        {
            for (var index = 0; index < tags.size(); index++)
            {
                var tag = tags.getLong(index);

                policiesByTag.getOrDefault(tag, Collections.emptyMap()).forEach((key, policyIds) ->
                {
                    if (!policyIds.isEmpty())
                    {
                        var type = key.split(SEPARATOR_WITH_ESCAPE)[2];

                        if (qualifiedPolicies.containsKey(type) && qualifiedPolicies.get(type).containsKey(key))
                        {
                            keys.computeIfAbsent(type, value -> new HashSet<>()).add(key);
                        }

                        qualifiedPolicies.computeIfAbsent(type, value -> new HashMap<>())
                                .computeIfAbsent(key, value -> new LinkedHashSet<>()).addAll(policyIds);
                    }
                });
            }
        }

        if (!keys.isEmpty())
        {
            keys.forEach((type, metrics) ->
            {
                var items = qualifiedPolicies.get(type);

                metrics.forEach(metric ->
                {
                    var values = items.get(metric);

                    if (values.size() > 1)
                    {
                        items.put(metric, values.stream().sorted(Comparator.comparing(item -> policies.get(CommonUtil.getLong(item)).getLong(POLICY_CREATION_TIME)).reversed())
                                .collect(Collectors.toCollection(LinkedHashSet::new)));
                    }
                });
            });
        }

        return qualifiedPolicies;
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        eventEngine.stop(vertx, promise);
    }
}
