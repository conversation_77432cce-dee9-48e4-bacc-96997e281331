/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.APIConstants;
import com.mindarray.api.Group;
import com.mindarray.api.Tag;
import com.mindarray.db.DBConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.Collections;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.visualization.VisualizationConstants.*;

public class WidgetConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(WidgetConfigStore.class, GlobalConstants.MOTADATA_STORE, "Widget Config Store");

    private static final WidgetConfigStore STORE = new WidgetConfigStore();

    private WidgetConfigStore()
    {
        super(DBConstants.TBL_WIDGET, LOGGER, false);
    }

    /**
     * Map of group ID to set of widget IDs.
     * This reverse mapping allows quick lookup of all widgets associated with a group.
     */
    private final Map<Long, Set<Long>> itemsByGroup = new ConcurrentHashMap<>();

    /**
     * Map of tag ID to set of widget IDs.
     * This reverse mapping allows quick lookup of all widgets associated with a tag.
     */
    private final Map<Long, Set<Long>> itemsByTag = new ConcurrentHashMap<>();

    public static WidgetConfigStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        try
        {
            Bootstrap.configDBService().getAll(DBConstants.TBL_WIDGET, result ->
            {
                if (result.succeeded())
                {
                    items.clear();

                    itemsByGroup.clear();

                    itemsByTag.clear();

                    if (result.result() != null && !result.result().isEmpty())
                    {
                        for (var index = 0; index < result.result().size(); index++)
                        {
                            var item = result.result().getJsonObject(index);

                            items.put(item.getLong(GlobalConstants.ID), item);

                            if (CommonUtil.debugEnabled())
                            {
                                LOGGER.debug(String.format("item added %s into the store", CommonUtil.removeSensitiveFields(item, false).encodePrettily()));
                            }

                            assign(item.getLong(ID));
                        }
                    }

                    promise.complete();

                    LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));
                }
                else
                {
                    LOGGER.fatal(String.format("failed to init store %s , reason : %s...", this.getClass().getSimpleName(), result.cause()));

                    promise.fail(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception.getMessage());
        }

        return promise.future();
    }

    /**
     * Adds or updates a widget in the cache for entities
     * <p>
     * This method identifies all the associated object/group/tag entities for the widget
     * and maintains bidirectional references in appropriate maps. It processes all data points
     * in the widget's data sources and extracts entity references.
     *
     * @param widgetId The ID of the widget to add or update
     */
    public void assign(long widgetId)
    {
        try
        {
            // Get the widget from the config store
            var item = items.get(widgetId);

            if (item == null)
            {
                LOGGER.warn("Widget " + widgetId + " not found in config store");

                return;
            }

            // Check if the widget is valid for caching entities
            if (!valid(item))
            {
                return;
            }

            // Get the data sources from the widget
            var dataSources = item.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES);

            // Process each data source
            for (var j = 0; j < dataSources.size(); j++)
            {
                var dataPoints = dataSources.getJsonObject(j).getJsonArray(DATA_POINTS);

                if (dataPoints == null)
                {
                    continue;
                }

                // Process each data point
                for (var index = 0; index < dataPoints.size(); index++)
                {
                    assign(dataPoints.getJsonObject(index), widgetId);
                }
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Checks if a widget is eligible for caching of entities
     * <p>
     * This method determines whether a widget should be cached based on its type
     * and structure. Only dashboard widgets with data sources of specific types
     * are eligible for caching.
     *
     * @param widget The widget to check
     * @return true if the widget is eligible for caching, false otherwise
     */
    private boolean valid(JsonObject widget)
    {
        if (widget == null)
        {
            return false;
        }

        // Check if it's a dashboard widget
        if (!widget.getString(VisualizationConstants.CONTAINER_TYPE, EMPTY_VALUE).equalsIgnoreCase("dashboard"))
        {
            return false;
        }

        // Check if it has data sources
        var dataSources = widget.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES);

        if (dataSources == null || dataSources.isEmpty())
        {
            return false;
        }

        // Check if the data source type is in the list of cacheable types
        var dataSource = dataSources.getJsonObject(0);

        if (dataSource == null)
        {
            return false;
        }

        return CACHEABLE_VISUALIZATION_TYPES.contains(dataSource.getString(TYPE, EMPTY_VALUE));
    }

    /**
     * Processes a data point and extracts entity references.
     * <p>
     * This method extracts entity references from a data point and adds them to the
     * entities set. It also maintains bidirectional references for groups and tags.
     *
     * @param dataPoint The data point to process
     * @param widgetId  The ID of the widget containing the data point
     */
    private void assign(JsonObject dataPoint, long widgetId)
    {
        // Check if the data point has valid entity information
        if (dataPoint == null ||
                dataPoint.getString(ENTITY_TYPE) == null ||
                dataPoint.getJsonArray(ENTITIES) == null ||
                dataPoint.getJsonArray(ENTITIES).isEmpty())
        {
            return;
        }

        var entityType = dataPoint.getString(ENTITY_TYPE);

        var entityIds = dataPoint.getJsonArray(ENTITIES);

        if (entityType.equalsIgnoreCase(APIConstants.Entity.GROUP.getName()))
        {
            // Add widget to each group's widget set
            for (var i = 0; i < entityIds.size(); i++)
            {
                try
                {
                    itemsByGroup.computeIfAbsent(entityIds.getLong(i), value -> Collections.newSetFromMap(new ConcurrentHashMap<>())).add(widgetId);

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace("Adding entities for group: " + GroupConfigStore.getStore().getItem(entityIds.getLong(i)).getString(Group.FIELD_GROUP_NAME)+" widget:: "+ widgetId);
                    }
                }
                catch (Exception e)
                {
                    LOGGER.error(e);
                }
            }
        }
        else if (entityType.equalsIgnoreCase(Tag.TAG))
        {
            // Add widget to each tag's widget set
            for (var i = 0; i < entityIds.size(); i++)
            {
                var tagId = TagConfigStore.getStore().getTag(entityIds.getString(i));

                // Check if tagId is valid (not the dummy ID)
                if (tagId != DUMMY_ID)
                {
                    itemsByTag.computeIfAbsent(tagId, value -> Collections.newSetFromMap(new ConcurrentHashMap<>())).add(widgetId);

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace("Adding entities for tag: " + entityIds.getString(i)+" widget:: "+ widgetId);
                    }
                }
            }
        }
    }

    /**
     * Updates a widget in the cache.
     * <p>
     * This method updates a widget in the cache by first deleting it and then re-adding it.
     * This ensures that all references are properly updated.
     *
     * @param widgetId The ID of the widget to update
     */
    public Future<Void> update(long widgetId)
    {
        // Remove the widget from the cache
        deleteItem(widgetId);

        // Re-add the widget to the cache
        return addItem(widgetId);
    }

    /**
     * Removes all cached references of a widget.
     * <p>
     * This method removes a widget from the cache, including all its entity references
     * and all references to it from groups and tags.
     *
     * @param id The ID of the widget to delete
     */
    @Override
    public void deleteItem(long id)
    {
        super.deleteItem(id);

        // Remove widget from all group references
        itemsByGroup.forEach((key, value) -> value.remove(id));

        // Remove widget from all tag references
        itemsByTag.forEach((key, value) -> value.remove(id));
    }

    public Set<Long> getItemsByGroups(JsonArray groups)
    {
        var items = new HashSet<Long>();

        if (groups != null)
        {
            for (var i=0 ; i < groups.size() ; i++)
            {
                var groupId = CommonUtil.getLong(groups.getValue(i));

                if (itemsByGroup.containsKey(groupId))
                {
                    items.addAll(itemsByGroup.get(groupId));
                }
            }
        }

        return items;
    }

    public Set<Long> getItemsByTags(JsonArray tags)
    {
        var items = new HashSet<Long>();

        if (tags != null)
        {
            for (var i=0 ; i < tags.size() ; i++)
            {
                var groupId = CommonUtil.getLong(tags.getValue(i));

                if (itemsByGroup.containsKey(groupId))
                {
                    items.addAll(itemsByGroup.get(groupId));
                }
            }
        }

        return items;
    }

    /**
     * Updates all widgets that depend on the specified entity IDs.
     * <p>
     * This method updates all widgets that are associated with any of the specified entities.
     * It retrieves the widgets from the provided map and updates each one.
     *
     * @param ids   The array of entity IDs whose widgets should be updated
     */
    private void updateItems(Set<Long> ids)
    {
        if (ids == null || ids.isEmpty())
        {
            return;
        }

        for (var id : ids)
        {
            // Re-add the widget to update its entities
            assign(id);
        }
    }

    /**
     * Updates all widgets that depend on the specified group IDs.
     * <p>
     * This method updates all widgets that are associated with any of the specified groups.
     * It retrieves the widgets from the widgetsByGroup map and updates each one.
     *
     * @param groups The array of group IDs whose widgets should be updated
     */
    public void updateItemsByGroups(JsonArray groups)
    {
        updateItems(getItemsByGroups(groups));
    }

    /**
     * Updates all widgets that depend on the specified tag IDs.
     * <p>
     * This method updates all widgets that are associated with any of the specified tags.
     * It retrieves the widgets from the widgetsByTag map and updates each one.
     *
     * @param tags The array of tag IDs whose widgets should be updated
     */
    public void updateItemsByTags(JsonArray tags)
    {
        updateItems(getItemsByTags(tags));
    }
}

