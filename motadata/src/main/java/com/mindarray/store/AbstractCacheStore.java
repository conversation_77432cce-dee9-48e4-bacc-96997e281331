/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/* Change Logs:
 *   Date           Author              Notes
 *   18-Mar-2025    Smit                MOTADATA-5431: Module Level Logging: added LoggerCacheStore
 * 	20-Feb-2025		Pruthviraj		    NetRoute ,NetRoute policy , NetRoute policy flap duration cache store added
 * 	22-Apr-2025		Chopra Deven		MOTADATA-5825: GroupCacheStore added
 */

package com.mindarray.store;

import io.vertx.core.Future;

/**
 * Abstract class representing a generic cache store, providing the foundation
 * for creating and managing specific types of cache stores. This class also
 * defines an enumeration of cache store types.
 * <p>
 * The {@code getCacheStore(CacheStore store)} method provides a mechanism
 * for retrieving the appropriate implementation of a cache store based on the
 * specified {@link CacheStore} type. Each cache store type corresponds to a
 * specific implementation, which is returned as an {@link AbstractCacheStore} instance.
 * <p>
 * Subclasses of {@code AbstractCacheStore} are expected to provide their own
 * specific logic for handling and managing the cache.
 * <p>
 * Features provided by this class include:
 * - Retrieval of specific cache store instances.
 * - Initialization of the cache store with default behavior.
 */
public abstract class AbstractCacheStore
{
    public static AbstractCacheStore getCacheStore(AbstractCacheStore.CacheStore store)
    {
        return switch (store)
        {
            case SNMP_TEMPLATE_OID_GROUP -> SNMPTemplateOIDGroupCacheStore.getStore();

            case DNS -> DNSCacheStore.getStore();

            case OBJECT_MANAGER -> ObjectManagerCacheStore.getStore();

            case AGENT -> AgentCacheStore.getStore();

            case REMOTE_EVENT_PROCESSOR -> RemoteEventProcessorCacheStore.getStore();

            case TOPOLOGY -> TopologyCacheStore.getStore();

            case SNMP_TRAP_VENDOR -> SNMPTrapVendorCacheStore.getStore();

            case LOG_PATTERN -> LogPatternCacheStore.getStore();

            case EVENT_POLICY -> EventPolicyCacheStore.getStore();

            case PLUGIN_ID -> PluginIdCacheStore.getStore();

            case EVENT_ORDINAL -> EventOrdinalCacheStore.getStore();

            case LICENSE -> LicenseCacheStore.getStore();

            case CONFIGURATION -> ConfigurationCacheStore.getStore();

            case OBJECT_STATUS -> ObjectStatusCacheStore.getStore();

            case OBJECT -> ObjectCacheStore.getStore();

            case TAG -> TagCacheStore.getStore();

            case TRAP -> TrapCacheStore.getStore();

            case INTEGRATION -> IntegrationCacheStore.getStore();

            case METRIC_POLICY_FLAP_DURATION -> MetricPolicyFlapDurationCacheStore.getStore();

            case COMPLIANCE_POLICY -> CompliancePolicyCacheStore.getStore();

            case LOGGER -> LoggerCacheStore.getStore();

            case NETROUTE -> NetRouteCacheStore.getStore();

            case NETROUTE_POLICY -> NetRoutePolicyCacheStore.getStore();

            case NETROUTE_POLICY_FLAP_DURATION -> NetRoutePolicyFlapDurationCacheStore.getStore();
        };
    }

    public Future<Void> initStore()
    {
        return Future.succeededFuture();
    }

    public enum CacheStore
    {
        SNMP_TEMPLATE_OID_GROUP,
        DNS,
        OBJECT_MANAGER,
        PLUGIN_ID,
        TOPOLOGY,
        AGENT,
        REMOTE_EVENT_PROCESSOR,
        SNMP_TRAP_VENDOR,
        LOG_PATTERN,
        EVENT_ORDINAL,
        EVENT_POLICY,
        LICENSE,
        CONFIGURATION,
        OBJECT_STATUS,
        TAG,
        TRAP,
        INTEGRATION,
        METRIC_POLICY_FLAP_DURATION,
        OBJECT,
        COMPLIANCE_POLICY,
        LOGGER,
        NETROUTE,
        NETROUTE_POLICY,
        NETROUTE_POLICY_FLAP_DURATION
    }

}
