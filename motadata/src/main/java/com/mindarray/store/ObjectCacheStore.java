/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/* Change Logs:
 *   Date           Author              Notes
 * 	 12-May-2025	Chopra Deven		MOTADATA-6050: added mapping of object ids by widgets
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.APIConstants;
import com.mindarray.api.Tag;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.Collections;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.nms.NMSConstants.INSTANCE_IP_METRICS;
import static com.mindarray.visualization.VisualizationConstants.*;

public class ObjectCacheStore extends AbstractCacheStore
{
    private static final Logger LOGGER = new Logger(ObjectCacheStore.class, GlobalConstants.MOTADATA_STORE, "Object Cache Store");

    private static final ObjectCacheStore STORE = new ObjectCacheStore();

    private final Map<String, String> items = new ConcurrentHashMap<>(); // key: object.id + # + instance.name -> value: instance.ip

    /**
     * Map of widget ID to set of associated entity IDs.
     * This is the primary cache that allows quick lookup of all entities associated with a widget.
     */
    private final Map<Long, Set<String>> itemsByWidget = new ConcurrentHashMap<>();

    private ObjectCacheStore() {}

    public static ObjectCacheStore getStore()
    {
        return STORE;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        try
        {
            if (GlobalConstants.BootstrapType.COLLECTOR == Bootstrap.bootstrapType())
            {
                promise.fail("remote event processor can not init store... it must be master or default boot sequence...");
            }
            else
            {
                // Get all widgets from the config store
                var items = WidgetConfigStore.getStore().getIds();

                // Process each widget
                for (var i = 0; i < items.size(); i++)
                {
                    assign(items.getLong(i));
                }

                LOGGER.info(String.format("store %s initialized... ", this.getClass().getSimpleName()) + " | " + itemsByWidget.size() + " widgets cached...");

                promise.complete();
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }
    /**
     * Adds or updates a widget in the cache for entities
     * <p>
     * This method identifies all the associated object/group/tag entities for the widget
     * and maintains bidirectional references in appropriate maps. It processes all data points
     * in the widget's data sources and extracts entity references.
     *
     * @param widgetId The ID of the widget to add or update
     */
    public void assign(long widgetId)
    {
        try
        {
            // Get the widget from the config store
            var item = WidgetConfigStore.getStore().getItem(widgetId);

            if (item == null)
            {
                LOGGER.warn("Widget " + widgetId + " not found in config store");

                return;
            }

            // Check if the widget is valid for caching entities
            if (!valid(item))
            {
                return;
            }

            // Get the data sources from the widget
            var dataSources = item.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES);

            var entities = new HashSet<String>();

            // Process each data source
            for (var j = 0; j < dataSources.size(); j++)
            {
                var dataPoints = dataSources.getJsonObject(j).getJsonArray(DATA_POINTS);

                if (dataPoints == null)
                {
                    continue;
                }

                // Process each data point
                for (var index = 0; index < dataPoints.size(); index++)
                {
                    update(dataPoints.getJsonObject(index), entities);
                }
            }

            // Store the entities for this widget if not empty
            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace("Adding entities to widget: " + WidgetConfigStore.getStore().getItem(widgetId).getString(VISUALIZATION_NAME)+" entities:: "+ entities);
            }

            if (!entities.isEmpty())
            {
                itemsByWidget.computeIfAbsent(item.getLong(GlobalConstants.ID), k -> Collections.newSetFromMap(new ConcurrentHashMap<>())).addAll(entities);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Checks if a widget is eligible for caching of entities
     * <p>
     * This method determines whether a widget should be cached based on its type
     * and structure. Only dashboard widgets with data sources of specific types
     * are eligible for caching.
     *
     * @param widget The widget to check
     * @return true if the widget is eligible for caching, false otherwise
     */
    private boolean valid(JsonObject widget)
    {
        if (widget == null)
        {
            return false;
        }

        // Check if it's a dashboard widget
        if (!widget.getString(VisualizationConstants.CONTAINER_TYPE, EMPTY_VALUE).equalsIgnoreCase("dashboard"))
        {
            return false;
        }

        // Check if it has data sources
        var dataSources = widget.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES);

        if (dataSources == null || dataSources.isEmpty())
        {
            return false;
        }

        // Check if the data source type is in the list of cacheable types
        var dataSource = dataSources.getJsonObject(0);

        if (dataSource == null)
        {
            return false;
        }

        return CACHEABLE_VISUALIZATION_TYPES.contains(dataSource.getString(TYPE, EMPTY_VALUE));
    }

    /**
     * Updates a widget in the cache.
     * <p>
     * This method updates a widget in the cache by first deleting it and then re-adding it.
     * This ensures that all references are properly updated.
     *
     * @param widgetId The ID of the widget to update
     */
    public void update(long widgetId)
    {
        // Remove the widget from the cache
        deleteItem(widgetId);

        // Re-add the widget to the cache
        assign(widgetId);
    }

    /**
     * Removes all cached references of a widget.
     * <p>
     * This method removes a widget from the cache, including all its entity references
     * and all references to it from groups and tags.
     *
     * @param widgetId The ID of the widget to delete
     */
    public void deleteItem(long widgetId)
    {
        // Remove widget's entity references
        itemsByWidget.remove(widgetId);
    }

    /**
     * Processes a data point and extracts entity references.
     * <p>
     * This method extracts entity references from a data point and adds them to the
     * entities set. It also maintains bidirectional references for groups and tags.
     *
     * @param dataPoint The data point to process
     * @param entities  The set to add entity references to
     */
    private void update(JsonObject dataPoint, Set<String> entities)
    {
        try
        {
            // Check if the data point has valid entity information
            if (dataPoint == null ||
                    dataPoint.getString(ENTITY_TYPE) == null ||
                    dataPoint.getJsonArray(ENTITIES) == null ||
                    dataPoint.getJsonArray(ENTITIES).isEmpty())
            {
                return;
            }

            var entityType = dataPoint.getString(ENTITY_TYPE);

            var entityIds = dataPoint.getJsonArray(ENTITIES);

            // Process based on entity type
            if (entityType.equalsIgnoreCase(APIConstants.Entity.OBJECT.getName()))
            {
                // Add object IDs directly
                entities.addAll(ObjectConfigStore.getStore()
                        .getObjectIdsByIds(entityIds)
                        .stream()
                        .map(CommonUtil::getString)
                        .collect(Collectors.toSet()));
            }
            else if (entityType.equalsIgnoreCase(APIConstants.Entity.GROUP.getName()))
            {
                // Add group's monitors to entities
                entities.addAll(ObjectConfigStore.getStore().getItemsByGroupIds(entityIds));
            }
            else if (entityType.equalsIgnoreCase(Tag.TAG))
            {
                // Process tag entities based on whether it's an instance tag or not
                if (dataPoint.getString(DATA_POINT) != null)
                {
                    entities.addAll(ObjectConfigStore.getStore().getItemsByTags(entityIds, dataPoint.getString(DATA_POINT).contains(INSTANCE_SEPARATOR)));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.warn("Error processing data point: " + dataPoint.encode());

            LOGGER.error(exception);
        }
    }

    /**
     * Updates all widgets that depend on the specified entity IDs.
     * <p>
     * This method updates all widgets that are associated with any of the specified entities.
     * It retrieves the widgets from the provided map and updates each one.
     *
     * @param ids   The array of entity IDs whose widgets should be updated
     */
    private void updateItems(Set<Long> ids)
    {
        if (ids == null || ids.isEmpty())
        {
            return;
        }

        for (var id : ids)
        {
            // Remove existing entities for this widget
            itemsByWidget.remove(id);

            // Re-add the widget to update its entities
            assign(id);
        }
    }

    /**
     * Updates all widgets that depend on the specified group IDs.
     * <p>
     * This method updates all widgets that are associated with any of the specified groups.
     * It retrieves the widgets from the widgetsByGroup map and updates each one.
     *
     * @param groups The array of group IDs whose widgets should be updated
     */
    public void updateItemsByGroups(JsonArray groups)
    {
        updateItems(WidgetConfigStore.getStore().getItemsByGroups(groups));
    }

    /**
     * Updates all widgets that depend on the specified tag IDs.
     * <p>
     * This method updates all widgets that are associated with any of the specified tags.
     * It retrieves the widgets from the widgetsByTag map and updates each one.
     *
     * @param tags The array of tag IDs whose widgets should be updated
     */
    public void updateItemsByTags(JsonArray tags)
    {
        updateItems(WidgetConfigStore.getStore().getItemsByTags(tags));
    }

    /**
     * Gets all entities associated with a widget.
     * <p>
     * This method returns the set of entity IDs associated with the specified widget.
     * If the widget is not found in the cache, an empty set is returned.
     *
     * @param widgetId The ID of the widget
     * @return The set of entity IDs associated with the widget
     */
    public Set<String> getItemsByWidgets(long widgetId)
    {
        // Return an empty set if the widget is not found
        return itemsByWidget.getOrDefault(widgetId, Collections.emptySet()); // Return an unmodifiable view for safety
    }


    /*
     *  id: Object Id (Long)
     *  item: polling data
     *  instance: instance type(example: esxi.vm)
     *  instanceValue: instanceName (example: vm1)
     *
     * */
    public void update(long id, Map<String, Object> item, String instanceType, String instanceName)
    {
        try
        {
            var metricName = INSTANCE_IP_METRICS.getOrDefault(instanceType, EMPTY_VALUE);

            if (!metricName.isEmpty() && item.containsKey(metricName))//will be checking whether instance has particular counter or not to add in cache
            {
                items.put(id + HASH_SEPARATOR + instanceName, CommonUtil.getString(item.get(metricName)));
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public String getInstanceIP(long id, String instance)
    {
        return items.getOrDefault(id + HASH_SEPARATOR + instance, EMPTY_VALUE);
    }

    public void delete(long id)
    {
        try
        {
            items.entrySet().removeIf(entry -> entry.getKey().split(HASH_SEPARATOR)[0].equalsIgnoreCase(CommonUtil.getString(id)));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
