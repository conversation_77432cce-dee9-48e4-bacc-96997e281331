/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
	25-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
	26-Feb-2025		Pruthviraj		    MOTADATA-4904 : Licensing added for netroute
	24-Mar-2025     Chandresh           MOTADATA-5426: Docker discovery and polling support added
	05-Aug-2025     Viram               MOTADATA-6858 Removed redundant code to have atomic operations and added method to remove duplicate code
*/

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Metric;
import com.mindarray.nms.NMSConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.LicenseUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.nms.NMSConstants.*;
import static com.mindarray.nms.NMSConstants.CONTAINERS;
import static com.mindarray.nms.NMSConstants.CONTAINER_PLUGINS;
import static com.mindarray.nms.NMSConstants.METRIC_INSTANCES;
import static com.mindarray.nms.NMSConstants.WAN_LINKS;

/**
 * The LicenseCacheStore class is responsible for managing license-related cache data.
 * It tracks and calculates license consumption for various object types and maintains
 * the provisioned counts for each type.
 * <p>
 * This class is implemented as a singleton and extends the {@link AbstractCacheStore} class.
 * It is initialized based on the bootstrap type and integrates with other configuration stores
 * to calculate the license usage.
 */
public class LicenseCacheStore extends AbstractCacheStore
{
    private static final LicenseCacheStore STORE = new LicenseCacheStore();
    private static final Logger LOGGER = new Logger(LicenseCacheStore.class, GlobalConstants.MOTADATA_STORE, "License Cache Store");
    private final AtomicInteger provisionedObjects = new AtomicInteger(0);
    private final AtomicInteger provisionedConfigObjects = new AtomicInteger(0);
    private final AtomicInteger provisionedAccessPoints = new AtomicInteger(0);

    private LicenseCacheStore()
    {
    }

    public static LicenseCacheStore getStore()
    {
        return STORE;
    }

    /**
     * Initializes the store and sets up required configurations and metrics.
     * This method evaluates the current bootstrap type, fetches provisioned metrics,
     * updates the provisioned objects, access points, and configuration objects.
     * <p>
     * If the store initialization is successful, the returned future will be completed successfully.
     * In case of an exception or invalid bootstrap sequence, the future will fail with the respective error.
     *
     * @return a Future representing the result of the store initialization.
     * The Future will complete successfully if the store is initialized properly,
     * or fail with an exception or error message in case of issues.
     */
    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.APP)
        {
            try
            {
                provisionedObjects.set((ObjectConfigStore.getStore().getProvisionedItems() +
                        NetRouteConfigStore.getStore().getProvisionedItems() +
                        MetricConfigStore.getStore().getProvisionedInstances(NMSConstants.VMS) +
                        MetricConfigStore.getStore().getProvisionedInstances(NMSConstants.CONTAINERS) +
                        MetricConfigStore.getStore().getProvisionedInstances(NMSConstants.WAN_LINKS) +
                        MetricConfigStore.getStore().getProvisionedInstances(NMSConstants.APPS)) -
                        AgentConfigStore.getStore().getReservedMetricAgents());

                provisionedAccessPoints.set(MetricConfigStore.getStore().getProvisionedInstances(NMSConstants.ACCESS_POINTS));

                provisionedConfigObjects.set(ConfigurationConfigStore.getStore().getProvisionedItems());

                promise.complete();

                LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                promise.fail(exception);
            }
        }

        else
        {
            promise.fail(String.format("failed to init store %s, reason: invalid boot sequence...", this.getClass().getSimpleName()));
        }

        return promise.future();
    }

    public long getConsumedLicenses()
    {
        return provisionedObjects.get() + Math.round(Math.ceil(CommonUtil.getDouble(provisionedAccessPoints.get()) /
                LicenseUtil.InstanceLicense.ACCESS_POINT.getValue()));
    }

    /**
     * Updates the cache by adding or removing a specified number of objects of a given type.
     * This method modifies the provisioned objects or access points based on the provided type and operation.
     * It logs the state of consumed licenses before and after the update operation.
     *
     * @param type      the type of objects to be updated (e.g., objects, apps, agents)
     * @param number    the number of objects to be added or removed from the cache
     * @param provision a boolean flag indicating the operation; {@code true} to add objects to the cache, {@code false} to remove objects from the cache
     */
    public void update(String type, int number, boolean provision)
    {
        LOGGER.info(String.format("%s objects consume before %s of device type %s and number %s", getConsumedLicenses(), provision ? "provision" : "unprovision", type, number));

        if (provision)
        {
            if (NMSConstants.ACCESS_POINTS.equalsIgnoreCase(type))
            {
                provisionedAccessPoints.addAndGet(number);
            }
            else
            {
                provisionedObjects.addAndGet(number);
            }
        }
        else
        {
            if (NMSConstants.ACCESS_POINTS.equalsIgnoreCase(type))
            {
                provisionedAccessPoints.addAndGet(-number);
            }
            else
            {
                provisionedObjects.addAndGet(-number);
            }
        }

        LOGGER.info(String.format("%s objects consume after %s of device type %s and number %s", getConsumedLicenses(), provision ? "provision" : "unprovision", type, number));
    }

    /**
     * @param type      type of Objects (example: objects, apps, agents)
     * @param provision `true` if added to cache and `false` to remove from cache
     */
    public void update(String type, boolean provision)
    {
        LOGGER.info(String.format("before %s of device type %s consume %s", provision ? "provision" : "unprovision", type, getConsumedLicenses()));

        if (provision)
        {
            if (NMSConstants.CONFIG_DEVICES.equalsIgnoreCase(type))
            {
                provisionedConfigObjects.getAndIncrement();
            }
            else
            {
                provisionedObjects.getAndIncrement();
            }
        }
        else
        {
            if (NMSConstants.CONFIG_DEVICES.equalsIgnoreCase(type))
            {
                provisionedConfigObjects.getAndDecrement();
            }
            else
            {
                provisionedObjects.getAndDecrement();
            }
        }

        LOGGER.info(String.format("after %s of device type %s consume %s", provision ? "provision" : "unprovision", type, getConsumedLicenses()));
    }

    public long getConsumedConfigLicenses()
    {
        return provisionedConfigObjects.get();
    }

    /**
     * Updates license consumption based on metric type and operation.
     * @param metric the metric object containing plugin information
     * @param provision true to add to license count, false to subtract
     * @param count number of instances to provision/unprovision
     */
    public void updateLicense(JsonObject metric, boolean provision, int count)
    {
        if (NMSConstants.isVirtualMachineMetric(metric.getString(Metric.METRIC_PLUGIN)))
        {
            update(VMS, count, provision);
        }

        else if (NMSConstants.isAccessPointMetric(metric.getString(Metric.METRIC_PLUGIN)))
        {
            update(ACCESS_POINTS, count, provision);
        }

        else if (NMSConstants.isWANLinkMetric(metric.getString(Metric.METRIC_PLUGIN)))
        {
            update(WAN_LINKS, count, provision);
        }

        else if (CONTAINER_PLUGINS.contains(metric.getString(Metric.METRIC_PLUGIN)))
        {
            update(CONTAINERS, count, provision);
        }

        else if (APPLICATION_PLUGINS.contains(metric.getString(Metric.METRIC_PLUGIN)) && (metric.containsKey(Metric.METRIC_OBJECT) && !ObjectConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_OBJECT)).getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(Category.DATABASE.getName())))
        {
            update(APPS, false);
        }
    }
}
