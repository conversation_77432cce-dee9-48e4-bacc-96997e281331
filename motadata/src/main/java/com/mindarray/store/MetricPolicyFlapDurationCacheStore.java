/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			    Notes
 *  02-May-2025		Chopra Deven    	MOTADATA-6050: caching of objectPolicyStatuses from MetricPolicyTriggerDurationCalculator
 */

package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.mindarray.policy.PolicyEngineConstants.*;

/**
 * Cache store for metric policy flap durations.
 * <p>
 * This class maintains a cache of policy statuses including severity data, durations, and ticks.
 * It provides methods to initialize, update, retrieve, and remove policy status information.
 */
public class MetricPolicyFlapDurationCacheStore extends AbstractCacheStore
{
    private static final MetricPolicyFlapDurationCacheStore STORE = new MetricPolicyFlapDurationCacheStore();

    private static final Logger LOGGER = new Logger(MetricPolicyFlapDurationCacheStore.class, GlobalConstants.MOTADATA_STORE, "Metric Policy Flap Duration Cache Store");

    // Key: policyKey, Value: policy context containing severity data, duration, ticks
    private static final Map<String, Map<String, Object>> flaps = new ConcurrentHashMap<>();

    // Stores only the duration per policy key for faster lookup
    private static final Map<String, Integer> flapDurations = new ConcurrentHashMap<>();

    /**
     * Private constructor to enforce singleton pattern.
     */
    private MetricPolicyFlapDurationCacheStore()
    {
        // Private constructor to enforce singleton pattern
    }

    /**
     * Gets the singleton instance of the cache store.
     *
     * @return The singleton instance
     */
    public static MetricPolicyFlapDurationCacheStore getStore()
    {
        return STORE;
    }

    /**
     * Initializes the cache store by loading policy status data from the event bus.
     * <p>
     * This method sends a request to the metric policy trigger duration query event bus
     * address and populates the cache with the received data. It handles any exceptions
     * that might occur during initialization.
     *
     * @return A Future that completes when initialization is done or fails with an exception
     */
    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        try
        {
            // Clear existing data before initialization
            flaps.clear();

            // Request policy status data from the event bus
            Bootstrap.vertx().eventBus().<JsonObject>request(
                    EventBusConstants.EVENT_METRIC_POLICY_TRIGGER_DURATION_QUERY,
                    new JsonObject(),
                    reply ->
                    {
                        if (reply.succeeded())
                        {
                            try
                            {
                                // Process the reply and populate the cache
                                var body = reply.result().body();

                                if (body != null)
                                {
                                    body.getMap().forEach((key, value) ->
                                    {
                                        if (key != null && value instanceof JsonObject)
                                        {
                                            // Convert JsonObject to ConcurrentHashMap and cache it
                                            flaps.computeIfAbsent(key, k -> new ConcurrentHashMap<>()).putAll(setFlapContext((JsonObject) value));

                                            // Cache the duration value separately for quicker access
                                            flapDurations.put(key, ((JsonObject) value).getInteger(PolicyEngineConstants.DURATION));
                                        }
                                    });
                                }

                                LOGGER.info("Metric Policy Flap Duration Cache Store initialized successfully with "
                                        + flaps.size() + " entries");

                                promise.complete();
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);

                                promise.fail(exception);
                            }
                        }
                        else
                        {
                            LOGGER.error(reply.cause());

                            promise.fail(reply.cause());
                        }
                    });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    /**
     * Extracts and structures essential policy status fields from the input JSON object.
     * <p>
     * This method creates a thread-safe map containing key policy data such as trigger ticks,
     * timestamps, durations, acknowledgments, and notes. Default values are used if any
     * field is missing to ensure consistency and avoid nulls.
     *
     * @param flapContext The source JSON object containing policy status information
     * @return A thread-safe map (ConcurrentHashMap) with extracted and normalized policy fields
     */
    private ConcurrentHashMap<String, Object> setFlapContext(JsonObject flapContext)
    {
        var context = new ConcurrentHashMap<String, Object>();

        context.put(POLICY_FIRST_TRIGGER_TICK, flapContext.getLong(POLICY_FIRST_TRIGGER_TICK, 0L));

        context.put(NMSConstants.PREVIOUS_FLAP_TIMESTAMP, flapContext.getLong(NMSConstants.PREVIOUS_FLAP_TIMESTAMP, 0L));

        context.put(EventBusConstants.EVENT_TIMESTAMP, flapContext.getLong(EventBusConstants.EVENT_TIMESTAMP, 0L));

        context.put(DURATION, flapContext.getInteger(DURATION, 0));

        context.put(GlobalConstants.SEVERITY, flapContext.getString(GlobalConstants.SEVERITY));

        context.put(POLICY_NOTE, flapContext.getString(POLICY_NOTE, GlobalConstants.EMPTY_VALUE));

        if (flapContext.containsKey(POLICY_ACKNOWLEDGE) && flapContext.getValue(POLICY_ACKNOWLEDGE) != null)
        {
            context.put(POLICY_ACKNOWLEDGE, flapContext.getString(POLICY_ACKNOWLEDGE));
        }

        return context;
    }

    /**
     * Updates the policy status for a given key.
     * <p>
     * This method replaces the existing policy status for the specified key with a new one.
     * If the key doesn't exist, a new entry is created.
     *
     * @param key                The policy key to update
     * @param flapContext The new policy status data
     */
    public void update(String key, JsonObject flapContext)
    {
        flaps.computeIfAbsent(key, k -> new ConcurrentHashMap<>()).putAll(setFlapContext(flapContext));
    }

    /**
     * Updates the duration value for a specific policy key.
     * <p>
     * This method updates only the duration field of the policy status for the specified key.
     * If the key doesn't exist, no action is taken.
     *
     * @param key      The policy key to update
     * @param duration The new duration value
     */
    public void updateFlapDuration(String key, int duration)
    {
        flapDurations.put(key, duration);
    }

    public int getFlapDuration(String key)
    {
        return flapDurations.getOrDefault(key, 0);
    }

    /**
     * Retrieves the policy status for a given key.
     * <p>
     * This method returns the policy status associated with the specified key.
     * If the key doesn't exist, an empty unmodifiable map is returned.
     *
     * @param key The policy key to retrieve
     * @return The policy status map, or an empty unmodifiable map if not found
     */
    public Map<String, Object> getItem(String key)
    {
        return flaps.getOrDefault(key, new ConcurrentHashMap<>());
    }

    /**
     * Removes a policy status from the cache.
     * <p>
     * This method removes the policy status associated with the specified key.
     * If the key doesn't exist, no action is taken.
     *
     * @param key The policy key to remove
     */
    public void remove(String key)
    {
        flaps.remove(key);
    }
}

