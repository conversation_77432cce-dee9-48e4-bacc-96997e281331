{"entity": "Metric Policy", "table": "tbl_config_metric_policy", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "policy.name", "title": "Policy Name", "type": "string", "rules": ["required"]}, {"name": "policy.type", "title": "Policy Type", "type": "string", "rules": ["required"]}, {"name": "policy.tags", "title": "Policy Tag(s)", "type": "list"}, {"name": "policy.context", "title": "Policy Context", "type": "map", "rules": ["required"]}, {"name": "policy.title", "title": "Policy Title", "type": "string"}, {"name": "policy.message", "title": "Policy Message", "type": "string"}, {"name": "policy.actions", "title": "Policy Action(s)", "type": "map"}], "entries": [{"type": "inline", "records": [{"policy.type": "<PERSON><PERSON>", "id": 10000000000001, "policy.name": "CPU Utilization", "policy.context": {"entity.type": "Monitor", "entities": [], "metric": "system.cpu.percent", "instance.type": "", "data.type": ["numeric"], "policy.trigger.time": 300, "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}, "policy.severity": {"CRITICAL": {"policy.condition": ">=", "policy.threshold": "95"}, "MAJOR": {"policy.condition": ">=", "policy.threshold": "85"}, "WARNING": {"policy.condition": ">=", "policy.threshold": "75"}, "CLEAR": {"policy.condition": "<", "policy.threshold": "75"}}}, "policy.email.notification.recipients": [], "policy.renotify": "no", "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.creation.time": 1653989795, "policy.state": "yes", "_type": "1"}, {"policy.type": "<PERSON><PERSON>", "id": 10000000000002, "policy.name": "Memory Utilization", "policy.context": {"entity.type": "Monitor", "entities": [], "metric": "system.memory.used.percent", "instance.type": "", "data.type": ["numeric"], "policy.trigger.time": 300, "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}, "policy.severity": {"CRITICAL": {"policy.condition": ">=", "policy.threshold": "95"}, "MAJOR": {"policy.condition": ">=", "policy.threshold": "85"}, "WARNING": {"policy.condition": ">=", "policy.threshold": "75"}, "CLEAR": {"policy.condition": "<", "policy.threshold": "75"}}}, "policy.email.notification.recipients": [], "policy.renotify": "no", "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.creation.time": 1653996180, "policy.state": "yes", "_type": "1"}, {"policy.type": "<PERSON><PERSON>", "id": 10000000000003, "policy.name": "Disk Utilization", "policy.context": {"entity.type": "Monitor", "entities": [], "metric": "system.disk.volume~used.percent", "instance.type": "system.disk.volume", "data.type": ["numeric"], "policy.trigger.time": 300, "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "exclude", "operator": "and", "conditions": [{"operand": "system.disk.volume~instance.name", "operator": "contain", "value": "/dev/loop"}]}]}}, "policy.severity": {"CRITICAL": {"policy.condition": ">=", "policy.threshold": "98"}, "MAJOR": {"policy.condition": ">=", "policy.threshold": "95"}, "WARNING": {"policy.condition": ">=", "policy.threshold": "85"}, "CLEAR": {"policy.condition": "<", "policy.threshold": "85"}}}, "policy.email.notification.recipients": [], "policy.renotify": "no", "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.creation.time": 1653996570, "policy.state": "yes", "_type": "1"}, {"policy.type": "<PERSON><PERSON>", "id": 10000000000004, "policy.name": "Interface Traffic Utilization", "policy.context": {"entity.type": "Monitor", "entities": [], "metric": "interface~traffic.utilization.percent", "instance.type": "interface", "data.type": ["numeric"], "policy.trigger.time": 300, "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}, "policy.severity": {"CRITICAL": {"policy.condition": ">=", "policy.threshold": "95"}, "MAJOR": {"policy.condition": ">=", "policy.threshold": "85"}, "WARNING": {"policy.condition": ">=", "policy.threshold": "75"}, "CLEAR": {"policy.condition": "<", "policy.threshold": "75"}}}, "policy.email.notification.recipients": [], "policy.renotify": "no", "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.creation.time": 1654002010, "policy.state": "yes", "_type": "1"}, {"policy.type": "<PERSON><PERSON>", "id": 10000000000005, "policy.name": "Network Latency", "policy.context": {"entity.type": "Group", "entities": [10000000000002, 10000000000026, 10000000000020, 10000000000023, 10000000000017], "metric": "ping.latency.ms", "instance.type": "", "data.type": ["numeric"], "policy.trigger.time": 300, "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}, "policy.severity": {"CRITICAL": {"policy.condition": ">=", "policy.threshold": "2000"}, "MAJOR": {"policy.condition": ">=", "policy.threshold": "1500"}, "WARNING": {"policy.condition": ">=", "policy.threshold": "1000"}, "CLEAR": {"policy.condition": "<", "policy.threshold": "1000"}}}, "policy.email.notification.recipients": [], "policy.renotify": "no", "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.creation.time": 1654002082, "policy.state": "yes", "_type": "1"}, {"policy.type": "<PERSON><PERSON>", "policy.name": "HyperV CPU Utilization", "policy.context": {"entity.type": "Group", "entities": [10000000000030], "metric": "hyperv.cpu.percent", "instance.type": "", "data.type": ["numeric"], "policy.trigger.time": 300, "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}, "policy.severity": {"CRITICAL": {"policy.condition": ">=", "policy.threshold": "95"}, "MAJOR": {"policy.condition": ">=", "policy.threshold": "85"}, "WARNING": {"policy.condition": ">=", "policy.threshold": "75"}, "CLEAR": {"policy.condition": "<", "policy.threshold": "75"}}}, "policy.email.notification.recipients": [], "policy.renotify": "no", "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.creation.time": 1654001893, "policy.state": "yes", "_type": "1", "id": 10000000000006}, {"policy.type": "<PERSON><PERSON>", "policy.name": "Firewall Reboot", "policy.context": {"entity.type": "Group", "entities": [10000000000009], "metric": "started.time.seconds", "instance.type": "", "data.type": ["numeric"], "policy.trigger.time": 300, "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}, "policy.severity": {"WARNING": {"policy.condition": "<=", "policy.threshold": "600"}, "CLEAR": {"policy.condition": "<", "policy.threshold": "600"}}}, "policy.email.notification.recipients": [], "policy.renotify": "no", "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.creation.time": 1653996437, "policy.state": "yes", "_type": "1", "id": 10000000000007}, {"policy.type": "<PERSON><PERSON>", "policy.name": "VMWare VM CPU Utilization", "policy.context": {"entity.type": "Group", "entities": [10000000000028], "metric": "esxi.vm~cpu.percent", "instance.type": "esxi.vm", "data.type": ["numeric"], "policy.trigger.time": 300, "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}, "policy.severity": {"CRITICAL": {"policy.condition": ">=", "policy.threshold": "95"}, "MAJOR": {"policy.condition": ">=", "policy.threshold": "85"}, "WARNING": {"policy.condition": ">=", "policy.threshold": "75"}, "CLEAR": {"policy.condition": "<", "policy.threshold": "75"}}}, "policy.email.notification.recipients": [], "policy.renotify": "no", "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.creation.time": 1654001792, "policy.state": "yes", "_type": "1", "id": 10000000000008}, {"policy.type": "<PERSON><PERSON>", "policy.name": "VMWare VM Memory Utilization", "policy.context": {"entity.type": "Group", "entities": [10000000000028], "metric": "esxi.vm~memory.used.percent", "instance.type": "esxi.vm", "data.type": ["numeric"], "policy.trigger.time": 300, "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}, "policy.severity": {"CRITICAL": {"policy.condition": ">=", "policy.threshold": "95"}, "MAJOR": {"policy.condition": ">=", "policy.threshold": "85"}, "WARNING": {"policy.condition": ">=", "policy.threshold": "75"}, "CLEAR": {"policy.condition": "<", "policy.threshold": "75"}}}, "policy.email.notification.recipients": [], "policy.renotify": "no", "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.creation.time": 1654001842, "policy.state": "yes", "_type": "1", "id": 10000000000009}, {"policy.type": "<PERSON><PERSON>", "policy.name": "VMWare ESX/ESXi CPU Utilization", "policy.context": {"entity.type": "Group", "entities": [10000000000028], "metric": "esxi.cpu.percent", "instance.type": "", "data.type": ["numeric"], "policy.trigger.time": 300, "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}, "policy.severity": {"CRITICAL": {"policy.condition": ">=", "policy.threshold": "95"}, "MAJOR": {"policy.condition": ">=", "policy.threshold": "85"}, "WARNING": {"policy.condition": ">=", "policy.threshold": "75"}, "CLEAR": {"policy.condition": "<", "policy.threshold": "75"}}}, "policy.email.notification.recipients": [], "policy.renotify": "no", "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.creation.time": 1654001677, "policy.state": "yes", "_type": "1", "id": 10000000000010}, {"policy.type": "<PERSON><PERSON>", "policy.name": "VMWare ESX/ESXi Memory Utilization", "policy.context": {"entity.type": "Group", "entities": [10000000000028], "metric": "esxi.memory.used.percent", "instance.type": "", "data.type": ["numeric"], "policy.trigger.time": 300, "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}, "policy.severity": {"CRITICAL": {"policy.condition": ">=", "policy.threshold": "95"}, "MAJOR": {"policy.condition": ">=", "policy.threshold": "85"}, "WARNING": {"policy.condition": ">=", "policy.threshold": "75"}, "CLEAR": {"policy.condition": "<", "policy.threshold": "75"}}}, "policy.email.notification.recipients": [], "policy.renotify": "no", "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.creation.time": 1654001737, "policy.state": "yes", "_type": "1", "id": 10000000000011}, {"policy.name": "Availability", "policy.type": "Availability", "policy.tags": ["Availability"], "policy.context": {"metric": "status", "entity.type": "Group", "entities": [10000000000017, 10000000000023, 10000000000020, 10000000000026, 10000000000002, 10000000000013, 10000000000033, 10000000000105, 10000000000108, 10000000000119, 10000000000136, 10000000000127], "policy.trigger.time": 300, "policy.trigger.occurrences": 3, "filters": {"data.filter": {}}}, "policy.deprovision.time": 0, "policy.email.notification.recipients": [], "policy.renotify": "no", "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.creation.time": 1653989532, "policy.state": "yes", "_type": "1", "id": 10000000000012}, {"policy.name": "Interface Availability", "policy.type": "Availability", "policy.tags": ["Interface Availability"], "policy.context": {"metric": "interface~status", "entity.type": "Monitor", "entities": [], "policy.trigger.time": 30, "policy.trigger.occurrences": 1, "filters": {"data.filter": {}}}, "policy.deprovision.time": 0, "policy.email.notification.recipients": [], "policy.renotify": "no", "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.creation.time": 1653989532, "policy.state": "yes", "_type": "1", "id": 10000000000013}, {"policy.type": "<PERSON><PERSON>", "policy.name": "Active Directory Reboot", "policy.context": {"entity.type": "Group", "entities": [10000000000090], "metric": "started.time.seconds", "policy.metric.plugins": "179,181,83", "instance.type": "", "data.type": ["numeric"], "policy.trigger.time": 300, "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}, "policy.severity": {"WARNING": {"policy.condition": "<=", "policy.threshold": "600"}, "CLEAR": {"policy.condition": ">=", "policy.threshold": "600"}}}, "policy.email.notification.recipients": [], "policy.renotify": "no", "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.creation.time": 1653989701, "policy.state": "yes", "_type": "1", "id": 10000000000014}, {"policy.type": "<PERSON><PERSON>", "policy.name": "Citrix Xen VM CPU Utilization", "policy.context": {"entity.type": "Group", "entities": [10000000000031], "metric": "citrix.xen.vm~cpu.percent", "instance.type": "citrix.xen.vm", "data.type": ["numeric"], "policy.trigger.time": 300, "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}, "policy.severity": {"CRITICAL": {"policy.condition": ">=", "policy.threshold": "95"}, "MAJOR": {"policy.condition": ">=", "policy.threshold": "85"}, "WARNING": {"policy.condition": ">=", "policy.threshold": "75"}, "CLEAR": {"policy.condition": "<", "policy.threshold": "75"}}}, "policy.email.notification.recipients": [], "policy.renotify": "no", "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.creation.time": 1654001613, "policy.state": "yes", "_type": "1", "id": 10000000000015}, {"policy.type": "<PERSON><PERSON>", "policy.name": "Ruckus Wireless CPU Utilization", "policy.context": {"entity.type": "Group", "entities": [10000000000022], "metric": "ruckus.wireless.access.point~cpu.percent", "instance.type": "wireless.access.point", "data.type": ["numeric"], "policy.trigger.time": 300, "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}, "policy.severity": {"CRITICAL": {"policy.condition": ">=", "policy.threshold": "95"}, "MAJOR": {"policy.condition": ">=", "policy.threshold": "85"}, "WARNING": {"policy.condition": ">=", "policy.threshold": "75"}, "CLEAR": {"policy.condition": "<", "policy.threshold": "75"}}}, "policy.email.notification.recipients": [], "policy.renotify": "no", "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.creation.time": 1654002436, "policy.state": "yes", "_type": "1", "id": 10000000000016}, {"policy.type": "<PERSON><PERSON>", "policy.name": "Process CPU Utilization", "policy.context": {"entity.type": "Monitor", "entities": [], "metric": "system.process~cpu.percent", "instance.type": "system.process", "data.type": ["numeric"], "policy.trigger.time": 300, "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}, "policy.severity": {"CRITICAL": {"policy.condition": ">=", "policy.threshold": "95"}, "MAJOR": {"policy.condition": ">=", "policy.threshold": "85"}, "WARNING": {"policy.condition": ">=", "policy.threshold": "75"}, "CLEAR": {"policy.condition": "<", "policy.threshold": "75"}}}, "policy.email.notification.recipients": [], "policy.renotify": "no", "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.creation.time": 1653996655, "policy.state": "yes", "_type": "1", "id": 10000000000017}, {"policy.name": "ESXi VM Availability", "policy.type": "Availability", "policy.tags": ["ESXi VM Availability"], "policy.context": {"metric": "esxi.vm~status", "entity.type": "Monitor", "entities": [], "policy.trigger.time": 30, "policy.trigger.occurrences": 1, "filters": {"data.filter": {}}}, "policy.deprovision.time": 0, "policy.email.notification.recipients": [], "policy.renotify": "no", "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.creation.time": 1653989532, "policy.state": "yes", "_type": "1", "id": 10000000000018}, {"policy.name": "Service Availability", "policy.type": "Availability", "policy.tags": ["Service Availability"], "policy.context": {"metric": "system.service~status", "entity.type": "Monitor", "entities": [], "policy.trigger.time": 900, "policy.trigger.occurrences": 1, "filters": {"data.filter": {}}}, "policy.deprovision.time": 0, "policy.email.notification.recipients": [], "policy.renotify": "no", "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.creation.time": 1653989532, "policy.state": "yes", "_type": "1", "id": 10000000000019}, {"policy.name": "Nutanix VM Availability", "policy.type": "Availability", "policy.tags": ["Nutanix VM Availability"], "policy.context": {"metric": "nutanix.vm~status", "entity.type": "Monitor", "entities": [], "policy.trigger.time": 30, "policy.trigger.occurrences": 1, "filters": {"data.filter": {}}}, "policy.deprovision.time": 0, "policy.email.notification.recipients": [], "policy.renotify": "no", "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.creation.time": 1653989532, "policy.state": "yes", "_type": "1", "id": 10000000000021}, {"policy.type": "<PERSON><PERSON>", "policy.name": "Nutanix VM CPU Utilization", "policy.context": {"entity.type": "Group", "entities": [10000000000107], "metric": "nutanix.vm~cpu.percent", "instance.type": "nutanix.vm", "data.type": ["numeric"], "policy.trigger.time": 300, "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}, "policy.severity": {"CRITICAL": {"policy.condition": ">=", "policy.threshold": "95"}, "MAJOR": {"policy.condition": ">=", "policy.threshold": "85"}, "WARNING": {"policy.condition": ">=", "policy.threshold": "75"}, "CLEAR": {"policy.condition": "<", "policy.threshold": "75"}}}, "policy.email.notification.recipients": [], "policy.renotify": "no", "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.creation.time": 1654001613, "policy.state": "yes", "_type": "1", "id": 10000000000023}, {"policy.type": "<PERSON><PERSON>", "policy.name": "Nutanix VM Memory Utilization", "policy.context": {"entity.type": "Group", "entities": [10000000000107], "metric": "nutanix.vm~memory.used.percent", "instance.type": "nutanix.vm", "data.type": ["numeric"], "policy.trigger.time": 300, "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}, "policy.severity": {"CRITICAL": {"policy.condition": ">=", "policy.threshold": "95"}, "MAJOR": {"policy.condition": ">=", "policy.threshold": "85"}, "WARNING": {"policy.condition": ">=", "policy.threshold": "75"}, "CLEAR": {"policy.condition": "<", "policy.threshold": "75"}}}, "policy.email.notification.recipients": [], "policy.renotify": "no", "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.creation.time": 1654001842, "policy.state": "yes", "_type": "1", "id": 10000000000024}], "version": "1.7"}]}