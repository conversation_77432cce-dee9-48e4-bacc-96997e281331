{"entity": "SLO Profile", "table": "tbl_config_slo_profile", "version": "1.0", "author": "<PERSON><PERSON><PERSON>", "props": [{"name": "slo.profile.name", "title": "SLO Profile Name", "type": "string", "rules": ["required", "unique"]}, {"name": "slo.profile.business.service.name", "title": "SLO Profile Business Service Name", "type": "string", "rules": ["required"]}, {"name": "slo.profile.type", "title": "SLO Profile Type", "type": "string", "rules": ["required"], "values": ["Availability", "Performance"]}, {"name": "slo.profile.context", "title": "SLO Profile Context", "type": "map", "rules": ["required"]}, {"name": "slo.profile.start.time", "title": "SLO Profile Start Time", "type": "string", "rules": ["required"]}, {"name": "slo.profile.tags", "title": "SLO Profile Tag(s)", "type": "list"}, {"name": "slo.notification.context", "title": "SLO Notification Context", "type": "map"}]}