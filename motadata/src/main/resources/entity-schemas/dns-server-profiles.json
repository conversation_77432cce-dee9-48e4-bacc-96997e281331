{"entity": "DNS Server Profile", "table": "tbl_config_dns_server_profile", "version": "1.0", "author": "<PERSON><PERSON><PERSON>", "props": [{"name": "dns.server.profile.name", "title": "DNS Server Name", "type": "string", "rules": ["required", "unique"]}, {"name": "dns.server.resolver.type", "title": "Resolver Type", "type": "string", "rules": ["required"]}, {"name": "dns.server.cache.size", "title": "<PERSON><PERSON>", "type": "numeric"}], "entries": []}