{"entity": "Group", "table": "tbl_config_group", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "group.name", "title": "Group Name", "type": "string", "rules": ["required"]}, {"name": "group.parent", "title": "Parent Group", "type": "numeric", "rules": ["required"]}], "entries": [{"type": "inline", "records": [{"group.name": "<PERSON><PERSON><PERSON>", "group.parent": 0, "id": 10000000000001, "group.context": {"group.auto.assign": "no"}}, {"group.name": "Network", "group.parent": 0, "id": 10000000000002, "group.context": {"group.auto.assign": "yes", "object.type": ["Switch", "Router", "Firewall", "SNMP Device", "Printer", "<PERSON><PERSON>r", "UPS", "Wireless Controller", "Hardware Sensor", "Email Gateway", "Windows (SNMP)", "Linux (SNMP)"]}}, {"group.name": "Switch", "group.parent": 10000000000002, "id": 10000000000003, "group.context": {"group.auto.assign": "yes", "object.type": ["Switch"]}}, {"group.name": "Cisco Systems", "group.parent": 10000000000003, "id": 10000000000004, "group.context": {"group.auto.assign": "yes", "object.type": ["Switch"], "object.vendor": ["Cisco Systems"]}}, {"group.name": "Juniper Networks", "group.parent": 10000000000003, "id": 10000000000005, "group.context": {"group.auto.assign": "yes", "object.type": ["Switch"], "object.vendor": ["Juniper Networks"]}}, {"group.name": "Hewlett Packard Enterprise", "group.parent": 10000000000003, "id": 10000000000048, "group.context": {"group.auto.assign": "yes", "object.type": ["Switch"], "object.vendor": ["Hewlett Packard Enterprise"]}}, {"group.name": "D-Link", "group.parent": 10000000000003, "id": 10000000000049, "group.context": {"group.auto.assign": "yes", "object.type": ["Switch"], "object.vendor": ["D-Link"]}}, {"group.name": "Extreme Networks", "group.parent": 10000000000003, "id": 10000000000050, "group.context": {"group.auto.assign": "yes", "object.type": ["Switch"], "object.vendor": ["Extreme Networks"]}}, {"group.name": "Brocade Communications Systems", "group.parent": 10000000000003, "id": 10000000000051, "group.context": {"group.auto.assign": "yes", "object.type": ["Switch"], "object.vendor": ["Brocade Communications Systems"]}}, {"group.name": "3Com Corporation", "group.parent": 10000000000003, "id": 10000000000052, "group.context": {"group.auto.assign": "yes", "object.type": ["Switch"], "object.vendor": ["3Com Corporation"]}}, {"group.name": "Netgear", "group.parent": 10000000000003, "id": 10000000000053, "group.context": {"group.auto.assign": "yes", "object.type": ["Switch"], "object.vendor": ["Netgear"]}}, {"group.name": "Barracuda Networks", "group.parent": 10000000000003, "id": 10000000000062, "group.context": {"group.auto.assign": "yes", "object.type": ["Switch"], "object.vendor": ["Barracuda Networks"]}}, {"group.name": "Router", "group.parent": 10000000000002, "id": 10000000000006, "group.context": {"group.auto.assign": "yes", "object.type": ["Router"]}}, {"group.name": "Cisco Systems", "group.parent": 10000000000006, "id": 10000000000007, "group.context": {"group.auto.assign": "yes", "object.type": ["Router"], "object.vendor": ["Cisco Systems"]}}, {"group.name": "Juniper Networks", "group.parent": 10000000000006, "id": 10000000000008, "group.context": {"group.auto.assign": "yes", "object.type": ["Router"], "object.vendor": ["Juniper Networks"]}}, {"group.name": "Alcatel", "group.parent": 10000000000006, "id": 10000000000054, "group.context": {"group.auto.assign": "yes", "object.type": ["Router"], "object.vendor": ["Alcatel"]}}, {"group.name": "MikroTik", "group.parent": 10000000000006, "id": 10000000000065, "group.context": {"group.auto.assign": "yes", "object.type": ["Router"], "object.vendor": ["MikroTik"]}}, {"group.name": "3Com Corporation", "group.parent": 10000000000006, "id": 10000000000055, "group.context": {"group.auto.assign": "yes", "object.type": ["Router"], "object.vendor": ["3Com Corporation"]}}, {"group.name": "Brocade Communications Systems", "group.parent": 10000000000006, "id": 10000000000056, "group.context": {"group.auto.assign": "yes", "object.type": ["Router"], "object.vendor": ["Brocade Communications Systems"]}}, {"group.name": "Blue Coat Systems", "group.parent": 10000000000006, "id": 10000000000057, "group.context": {"group.auto.assign": "yes", "object.type": ["Router"], "object.vendor": ["Blue Coat Systems"]}}, {"group.name": "Firewall", "group.parent": 10000000000002, "id": 10000000000009, "group.context": {"group.auto.assign": "yes", "object.type": ["Firewall"]}}, {"group.name": "Sonicwall", "group.parent": 10000000000009, "id": 10000000000010, "group.context": {"group.auto.assign": "yes", "object.vendor": ["SonicWall"], "object.type": ["Firewall"]}}, {"group.name": "Fortinet", "group.parent": 10000000000009, "id": 10000000000011, "group.context": {"group.auto.assign": "yes", "object.vendor": ["Fortinet"], "object.type": ["Firewall"]}}, {"group.name": "Palo Alto Networks", "group.parent": 10000000000009, "id": 10000000000012, "group.context": {"group.auto.assign": "yes", "object.vendor": ["Palo Alto Networks"], "object.type": ["Firewall"]}}, {"group.name": "Check Point", "group.parent": 10000000000009, "id": 10000000000058, "group.context": {"group.auto.assign": "yes", "object.vendor": ["Check Point"], "object.type": ["Firewall"]}}, {"group.name": "F5 Networks", "group.parent": 10000000000009, "id": 10000000000059, "group.context": {"group.auto.assign": "yes", "object.vendor": ["F5 Networks"], "object.type": ["Firewall"]}}, {"group.name": "Cyberoam Technologies", "group.parent": 10000000000009, "id": 10000000000060, "group.context": {"group.auto.assign": "yes", "object.vendor": ["Cyberoam Technologies"], "object.type": ["Firewall"]}}, {"group.name": "Barracuda Networks", "group.parent": 10000000000009, "id": 10000000000061, "group.context": {"group.auto.assign": "yes", "object.vendor": ["Barracuda Networks"], "object.type": ["Firewall"]}}, {"group.name": "<PERSON><PERSON>r", "group.parent": 10000000000002, "id": 10000000000062, "group.context": {"group.auto.assign": "yes", "object.type": ["<PERSON><PERSON>r"]}}, {"group.name": "Barracuda Networks", "group.parent": 10000000000062, "id": 10000000000063, "group.context": {"group.auto.assign": "yes", "object.vendor": ["Barracuda Networks"], "object.type": ["<PERSON><PERSON>r"]}}, {"group.name": "F5 Networks", "group.parent": 10000000000062, "id": 10000000000064, "group.context": {"group.auto.assign": "yes", "object.vendor": ["F5 Networks"], "object.type": ["<PERSON><PERSON>r"]}}, {"group.name": "Hardware Sensor", "group.parent": 10000000000002, "id": 10000000000099, "group.context": {"group.auto.assign": "yes", "object.type": ["Hardware Sensor"]}}, {"group.name": "Dell", "group.parent": 10000000000099, "id": 10000000000100, "group.context": {"group.auto.assign": "yes", "object.vendor": ["Dell"], "object.type": ["Hardware Sensor"]}}, {"group.name": "Hewlett Packard Enterprise", "group.parent": 10000000000099, "id": 10000000000101, "group.context": {"group.auto.assign": "yes", "object.vendor": ["Hewlett Packard Enterprise"], "object.type": ["Hardware Sensor"]}}, {"group.name": "Cloud", "group.parent": 0, "id": 10000000000013, "group.context": {"group.auto.assign": "yes", "object.type": ["AWS Cloud", "Azure Cloud", "Office 365", "Amazon DynamoDB", "Amazon EBS", "Amazon EC2", "AWS ELB", "Amazon RDS", "Amazon S3", "Amazon SNS", "Amazon CloudFront", "AWS Auto Scaling", "AWS Lambda", "Amazon SQS", "AWS Elastic Beanstalk", "Amazon DocumentDB", "Azure Cosmos DB", "Azure SQL Database", "Azure Storage", "Azure VM", "Azure WebApp", "Azure Service Bus", "Azure Application Gateway", "Azure Function", "Azure Load Balancer", "Azure VM Scale Set", "Azure CDN", "OneDrive", "Microsoft Teams", "SharePoint Online", "Exchange Online"]}}, {"group.name": "AWS Cloud", "group.parent": 10000000000013, "id": 10000000000014, "group.context": {"group.auto.assign": "yes", "object.type": ["AWS Cloud", "Amazon DynamoDB", "Amazon EBS", "Amazon EC2", "AWS ELB", "Amazon RDS", "Amazon S3", "Amazon SNS", "Amazon CloudFront", "AWS Auto Scaling", "AWS Lambda", "Amazon SQS", "AWS Elastic Beanstalk", "Amazon DocumentDB"]}}, {"group.name": "Azure Cloud", "group.parent": 10000000000013, "id": 10000000000015, "group.context": {"group.auto.assign": "yes", "object.type": ["Azure Cloud", "Azure Cosmos DB", "Azure SQL Database", "Azure Storage", "Azure VM", "Azure WebApp", "Azure Service Bus", "Azure Application Gateway", "Azure Function", "Azure Load Balancer", "Azure VM Scale Set", "Azure PostgreSQL Server", "Azure MySQL Server", "Azure CDN"]}}, {"group.name": "Office 365", "group.parent": 10000000000013, "id": 10000000000016, "group.context": {"group.auto.assign": "yes", "object.type": ["Office 365", "OneDrive", "Microsoft Teams", "SharePoint Online", "Exchange Online"]}}, {"group.name": "Server", "group.parent": 0, "id": 10000000000017, "group.context": {"group.auto.assign": "yes", "object.type": ["Windows", "Linux", "IBM AIX", "HP-UX", "Solar<PERSON>", "Windows Cluster", "IBM AS/400"]}}, {"group.name": "Windows", "group.parent": 10000000000017, "id": 10000000000018, "group.context": {"group.auto.assign": "yes", "object.type": ["Windows"]}}, {"group.name": "Linux", "group.parent": 10000000000017, "id": 10000000000019, "group.context": {"group.auto.assign": "yes", "object.type": ["Linux"]}}, {"group.name": "Wireless", "group.parent": 0, "id": 10000000000020, "group.context": {"group.auto.assign": "yes", "object.type": ["Ruckus Wireless", "Aruba Wireless", "Cisco Wireless"]}}, {"group.name": "Cisco Wireless", "group.parent": 10000000000020, "id": 10000000000021, "group.context": {"group.auto.assign": "yes", "object.type": ["Cisco Wireless"]}}, {"group.name": "Aruba Wireless", "group.parent": 10000000000020, "id": 10000000000067, "group.context": {"group.auto.assign": "yes", "object.type": ["Aruba Wireless"]}}, {"group.name": "Ruckus Wireless", "group.parent": 10000000000020, "id": 10000000000022, "group.context": {"group.auto.assign": "yes", "object.type": ["Ruckus Wireless"]}}, {"group.name": "Other", "group.parent": 0, "id": 10000000000023, "group.context": {"group.auto.assign": "yes", "object.type": ["Cisco UCS", "<PERSON>", "IBM Tape Library"]}}, {"group.name": "Email Gateway", "group.parent": 10000000000002, "id": 10000000000104, "group.context": {"group.auto.assign": "yes", "object.type": ["Email Gateway"]}}, {"group.name": "Cisco UCS", "group.parent": 10000000000023, "id": 10000000000024, "group.context": {"group.auto.assign": "yes", "object.type": ["Cisco UCS"]}}, {"group.name": "Symantec Corporation", "group.parent": 10000000000104, "id": 10000000000025, "group.context": {"group.auto.assign": "yes", "object.vendor": ["Symantec Corporation"], "object.type": ["Email Gateway"]}}, {"group.name": "IBM Tape Library", "group.parent": 10000000000023, "id": 10000000000102, "group.context": {"group.auto.assign": "yes", "object.type": ["IBM Tape Library"]}}, {"group.name": "IronPort Systems", "group.parent": 10000000000104, "id": 10000000000103, "group.context": {"group.auto.assign": "yes", "object.vendor": ["IronPort Systems"], "object.type": ["Email Gateway"]}}, {"group.name": "<PERSON>", "group.parent": 10000000000023, "id": 10000000000066, "group.context": {"group.auto.assign": "yes", "object.type": ["<PERSON>"]}}, {"group.name": "Virtualization", "group.parent": 0, "id": 10000000000026, "group.context": {"group.auto.assign": "yes", "object.type": ["vCenter", "VMware ESXi", "Hyper-V", "Hyper-V Cluster", "Citrix <PERSON>", "Citrix Xen Cluster"]}}, {"group.name": "vCenter", "group.parent": 10000000000026, "id": 10000000000027, "group.context": {"group.auto.assign": "yes", "object.type": ["vCenter"]}}, {"group.name": "VMware ESXi", "group.parent": 10000000000026, "id": 10000000000028, "group.context": {"group.auto.assign": "yes", "object.type": ["VMware ESXi"]}}, {"group.name": "Hyper-V Cluster", "group.parent": 10000000000026, "id": 10000000000029, "group.context": {"group.auto.assign": "yes", "object.type": ["Hyper-V Cluster"]}}, {"group.name": "Hyper-V", "group.parent": 10000000000026, "id": 10000000000030, "group.context": {"group.auto.assign": "yes", "object.type": ["Hyper-V"]}}, {"group.name": "Citrix <PERSON>", "group.parent": 10000000000026, "id": 10000000000031, "group.context": {"group.auto.assign": "yes", "object.type": ["Citrix <PERSON>"]}}, {"group.name": "Citrix Xen Cluster", "group.parent": 10000000000026, "id": 10000000000032, "group.context": {"group.auto.assign": "yes", "object.type": ["Citrix Xen Cluster"]}}, {"group.name": "Service Check", "group.parent": 0, "id": 10000000000033, "group.context": {"group.auto.assign": "yes", "object.type": ["<PERSON>", "Port", "RADIUS", "URL", "SSL Certificate", "DNS", "Domain", "Email", "FTP", "NTP"]}}, {"group.name": "<PERSON>", "group.parent": 10000000000033, "id": 10000000000034, "group.context": {"group.auto.assign": "yes", "object.type": ["<PERSON>"]}}, {"group.name": "Port", "group.parent": 10000000000033, "id": 10000000000035, "group.context": {"group.auto.assign": "yes", "object.type": ["Port"]}}, {"group.name": "RADIUS", "group.parent": 10000000000033, "id": 10000000000036, "group.context": {"group.auto.assign": "yes", "object.type": ["RADIUS"]}}, {"group.name": "URL", "group.parent": 10000000000033, "id": 10000000000037, "group.context": {"group.auto.assign": "yes", "object.type": ["URL"]}}, {"group.name": "SSL Certificate", "group.parent": 10000000000033, "id": 10000000000038, "group.context": {"group.auto.assign": "yes", "object.type": ["SSL Certificate"]}}, {"group.name": "DNS", "group.parent": 10000000000033, "id": 10000000000039, "group.context": {"group.auto.assign": "yes", "object.type": ["DNS"]}}, {"group.name": "Domain", "group.parent": 10000000000033, "id": 10000000000040, "group.context": {"group.auto.assign": "yes", "object.type": ["Domain"]}}, {"group.name": "Email", "group.parent": 10000000000033, "id": 10000000000041, "group.context": {"group.auto.assign": "yes", "object.type": ["Email"]}}, {"group.name": "FTP", "group.parent": 10000000000033, "id": 10000000000042, "group.context": {"group.auto.assign": "yes", "object.type": ["FTP"]}}, {"group.name": "NTP", "group.parent": 10000000000033, "id": 10000000000043, "group.context": {"group.auto.assign": "yes", "object.type": ["NTP"]}}, {"group.name": "Windows Cluster", "group.parent": 10000000000017, "id": 10000000000044, "group.context": {"group.auto.assign": "yes", "object.type": ["Windows Cluster"]}}, {"group.name": "IBM AIX", "group.parent": 10000000000017, "id": 10000000000045, "group.context": {"group.auto.assign": "yes", "object.type": ["IBM AIX"]}}, {"group.name": "HP-UX", "group.parent": 10000000000017, "id": 10000000000046, "group.context": {"group.auto.assign": "yes", "object.type": ["HP-UX"]}}, {"group.name": "Solar<PERSON>", "group.parent": 10000000000017, "id": 10000000000047, "group.context": {"group.auto.assign": "yes", "object.type": ["Solar<PERSON>"]}}, {"group.name": "PostgreSQL", "group.parent": 10000000000136, "id": 10000000000068, "group.context": {"group.auto.assign": "no"}}, {"group.name": "Oracle Database", "group.parent": 10000000000136, "id": 10000000000069, "group.context": {"group.auto.assign": "no"}}, {"group.name": "MySQL", "group.parent": 10000000000136, "id": 10000000000070, "group.context": {"group.auto.assign": "no"}}, {"group.name": "MariaDB", "group.parent": 10000000000136, "id": 10000000000071, "group.context": {"group.auto.assign": "no"}}, {"group.name": "IBM Db2", "group.parent": 0, "id": 10000000000072, "group.context": {"group.auto.assign": "no"}}, {"group.name": "SQL Server", "group.parent": 10000000000136, "id": 10000000000073, "group.context": {"group.auto.assign": "no"}}, {"group.name": "Sybase", "group.parent": 0, "id": 10000000000074, "group.context": {"group.auto.assign": "no"}}, {"group.name": "SAP HANA", "group.parent": 10000000000136, "id": 10000000000075, "group.context": {"group.auto.assign": "no"}}, {"group.name": "SAP MaxDB", "group.parent": 0, "id": 10000000000076, "group.context": {"group.auto.assign": "no"}}, {"group.name": "Zimbra", "group.parent": 0, "id": 10000000000077, "group.context": {"group.auto.assign": "no"}}, {"group.name": "Windows RDP", "group.parent": 0, "id": 10000000000078, "group.context": {"group.auto.assign": "no"}}, {"group.name": "Apache HTTP", "group.parent": 0, "id": 10000000000079, "group.context": {"group.auto.assign": "no"}}, {"group.name": "Light Httpd", "group.parent": 0, "id": 10000000000080, "group.context": {"group.auto.assign": "no"}}, {"group.name": "Microsoft IIS", "group.parent": 0, "id": 10000000000081, "group.context": {"group.auto.assign": "no"}}, {"group.name": "HAProxy", "group.parent": 0, "id": 10000000000082, "group.context": {"group.auto.assign": "no"}}, {"group.name": "<PERSON><PERSON><PERSON>", "group.parent": 0, "id": 10000000000083, "group.context": {"group.auto.assign": "no"}}, {"group.name": "Exchange Client Access Role", "group.parent": 0, "id": 10000000000084, "group.context": {"group.auto.assign": "no"}}, {"group.name": "Exchange Edge Transport Role", "group.parent": 0, "id": 10000000000085, "group.context": {"group.auto.assign": "no"}}, {"group.name": "Exchange Mailbox", "group.parent": 0, "id": 10000000000086, "group.context": {"group.auto.assign": "no"}}, {"group.name": "Exchange Mailbox Role", "group.parent": 0, "id": 10000000000087, "group.context": {"group.auto.assign": "no"}}, {"group.name": "IBM WebSphere", "group.parent": 0, "id": 10000000000088, "group.context": {"group.auto.assign": "no"}}, {"group.name": "<PERSON><PERSON><PERSON>", "group.parent": 0, "id": 10000000000089, "group.context": {"group.auto.assign": "no"}}, {"group.name": "Active Directory", "group.parent": 0, "id": 10000000000090, "group.context": {"group.auto.assign": "no"}}, {"group.name": "Bind9", "group.parent": 0, "id": 10000000000091, "group.context": {"group.auto.assign": "no"}}, {"group.name": "Windows DNS", "group.parent": 0, "id": 10000000000092, "group.context": {"group.auto.assign": "no"}}, {"group.name": "Windows DHCP", "group.parent": 0, "id": 10000000000093, "group.context": {"group.auto.assign": "no"}}, {"group.name": "Linux DHCP", "group.parent": 0, "id": 10000000000094, "group.context": {"group.auto.assign": "no"}}, {"group.name": "IBM MQ", "group.parent": 0, "id": 10000000000095, "group.context": {"group.auto.assign": "no"}}, {"group.name": "MSMQ", "group.parent": 0, "id": 10000000000096, "group.context": {"group.auto.assign": "no"}}, {"group.name": "RabbitMQ", "group.parent": 0, "id": 10000000000097, "group.context": {"group.auto.assign": "no"}}, {"group.name": "Apache Tomcat", "group.parent": 0, "id": 10000000000098, "group.context": {"group.auto.assign": "no"}}, {"group.name": "HCI", "group.parent": 0, "id": 10000000000105, "group.context": {"group.auto.assign": "yes", "object.type": ["Prism", "Nutanix"]}}, {"group.name": "Prism", "group.parent": 10000000000105, "id": 10000000000106, "group.context": {"group.auto.assign": "yes", "object.type": ["Prism"]}}, {"group.name": "Nutanix", "group.parent": 10000000000105, "id": 10000000000107, "group.context": {"group.auto.assign": "yes", "object.type": ["Nutanix"]}}, {"group.name": "SDN", "group.parent": 0, "id": 10000000000108, "group.context": {"group.auto.assign": "yes", "object.type": ["Cisco vManage", "<PERSON><PERSON>"]}}, {"group.name": "Cisco SD-WAN", "group.parent": 10000000000108, "id": 10000000000109, "group.context": {"group.auto.assign": "yes", "object.type": ["Cisco vManage", "Cisco vSmart", "Cisco vBond", "Cisco vEdge"]}}, {"group.name": "Manager", "group.parent": 10000000000109, "id": 10000000000110, "group.context": {"group.auto.assign": "yes", "object.type": ["Cisco vManage"]}}, {"group.name": "Controller", "group.parent": 10000000000109, "id": 10000000000111, "group.context": {"group.auto.assign": "yes", "object.type": ["Cisco vSmart"]}}, {"group.name": "Validator", "group.parent": 10000000000109, "id": 10000000000112, "group.context": {"group.auto.assign": "yes", "object.type": ["Cisco vBond"]}}, {"group.name": "WAN Edge", "group.parent": 10000000000109, "id": 10000000000113, "group.context": {"group.auto.assign": "yes", "object.type": ["Cisco vEdge"]}}, {"group.name": "<PERSON><PERSON>", "group.parent": 10000000000108, "id": 10000000000114, "group.context": {"group.auto.assign": "yes", "object.type": ["<PERSON><PERSON>", "Cisco Meraki Security", "Cisco Meraki Switch", "Cisco Meraki Radio"]}}, {"group.name": "Meraki Controller", "group.parent": 10000000000114, "id": 10000000000115, "group.context": {"group.auto.assign": "yes", "object.type": ["<PERSON><PERSON>"]}}, {"group.name": "Meraki Security (MX)", "group.parent": 10000000000114, "id": 10000000000116, "group.context": {"group.auto.assign": "yes", "object.type": ["Cisco Meraki Security"]}}, {"group.name": "<PERSON><PERSON><PERSON> (MS)", "group.parent": 10000000000114, "id": 10000000000117, "group.context": {"group.auto.assign": "yes", "object.type": ["Cisco Meraki Switch"]}}, {"group.name": "Meraki Radio (MR)", "group.parent": 10000000000114, "id": 10000000000118, "group.context": {"group.auto.assign": "yes", "object.type": ["Cisco Meraki Radio"]}}, {"group.name": "Storage", "group.parent": 0, "id": 10000000000119, "group.context": {"group.auto.assign": "yes", "object.type": ["NetApp ONTAP Cluster", "HPE StoreOnce", "HPE Primera", "HPE 3PAR", "Dell EMC Unity", "IBM FlashSystem"]}}, {"group.name": "NetApp", "group.parent": 10000000000119, "id": 10000000000120, "group.context": {"group.auto.assign": "yes", "object.type": ["NetApp ONTAP Cluster"]}}, {"group.name": "Hewlett Packard Enterprise", "group.parent": 10000000000119, "id": 10000000000121, "group.context": {"group.auto.assign": "yes", "object.type": ["HPE StoreOnce", "HPE Primera", "HPE 3PAR"]}}, {"group.name": "Dell", "group.parent": 10000000000119, "id": 10000000000135, "group.context": {"group.auto.assign": "yes", "object.type": ["Dell EMC Unity"]}}, {"group.name": "IBM", "group.parent": 10000000000119, "id": 10000000000133, "group.context": {"group.auto.assign": "yes", "object.type": ["IBM FlashSystem"]}}, {"group.name": "Cisco ACI", "group.parent": 10000000000108, "id": 10000000000122, "group.context": {"group.auto.assign": "yes", "object.type": ["Cisco ACI"]}}, {"group.name": "IBM AS/400", "group.parent": 10000000000017, "id": 10000000000123, "group.context": {"group.auto.assign": "yes", "object.type": ["IBM AS/400"]}}, {"group.name": "Windows (SNMP)", "group.parent": 10000000000002, "id": 10000000000124, "group.context": {"group.auto.assign": "yes", "object.type": ["Windows (SNMP)"]}}, {"group.name": "Linux (SNMP)", "group.parent": 10000000000002, "id": 10000000000125, "group.context": {"group.auto.assign": "yes", "object.type": ["Linux (SNMP)"]}}, {"group.name": "VMware NSX-T", "group.parent": 10000000000108, "id": 10000000000126, "group.context": {"group.auto.assign": "yes", "object.type": ["VMware NSX-T"]}}, {"group.name": "Container Orchestration", "group.parent": 0, "id": 10000000000127, "group.context": {"group.auto.assign": "yes", "object.type": ["<PERSON><PERSON>"]}}, {"group.name": "<PERSON><PERSON>", "group.parent": 10000000000127, "id": 10000000000128, "group.context": {"group.auto.assign": "yes", "object.type": ["<PERSON><PERSON>"]}}, {"group.name": "MongoDB", "group.parent": 10000000000136, "id": 10000000000129, "group.context": {"group.auto.assign": "no"}}, {"group.name": "Kubernetes", "group.parent": 10000000000127, "id": 10000000000131, "group.context": {"group.auto.assign": "yes", "object.type": ["Kubernetes"]}}, {"group.name": "Oracle WebLogic", "group.parent": 0, "id": 10000000000130, "group.context": {"group.auto.assign": "no", "object.type": ["Oracle WebLogic"]}}, {"group.name": "Oracle RAC Cluster", "group.parent": 10000000000136, "id": 10000000000132, "group.context": {"group.auto.assign": "no"}}, {"group.name": "Database", "group.parent": 0, "id": 10000000000136, "group.context": {"group.auto.assign": "yes", "object.type": ["Oracle RAC Cluster", "Oracle Database", "PostgreSQL", "MySQL", "SQL Server", "MariaDB", "SAP HANA", "MongoDB"]}}, {"group.name": "REST API", "group.parent": 10000000000033, "id": 10000000000134, "group.context": {"group.auto.assign": "yes", "object.type": ["REST API"]}}], "version": "3.5"}]}