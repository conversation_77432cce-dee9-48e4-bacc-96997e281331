package com.mindarray.dns;

import com.mindarray.*;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.DNSServerProfile;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.DNSCacheStore;
import com.mindarray.store.DNSServerProfileConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.GlobalConstants.ID;
import static com.mindarray.GlobalConstants.SYSTEM_REMOTE_ADDRESS;

@ExtendWith(VertxExtension.class)
@Timeout(60 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestDNSResolver
{
    public static final Logger LOGGER = new Logger(TestDNSResolver.class, "dns", "Test DNS Resolver");

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        Bootstrap.configDBService().deleteAll(DBConstants.TBL_DNS_SERVER_PROFILE, new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, DNSServerProfileConfigStore.getStore().getIds()), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
        {
            if (result.succeeded())
            {
                DNSServerProfileConfigStore.getStore().deleteItems(DNSServerProfileConfigStore.getStore().getIds());

                testContext.completeNow();
            }
            else
            {
                testContext.failNow(result.cause());

                LOGGER.error(result.cause());
            }
        });
    }
    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateDNSServer(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(DNSServerProfile.DNS_SERVER_PROFILE_NAME, "Google.com")
                .put(DNSServerProfile.DNS_SERVER_PROFILE_DESCRIPTION, "This is Google's DNS Server")
                .put(DNSServerProfile.DNS_SERVER_PROFILE_CONTEXT, new JsonObject()
                        .put(DNSServerProfile.DNS_SERVER_IP, "***********")
                        .put(DNSServerProfile.DNS_SERVER_PORT, 53)
                        .put(DNSServerProfile.DNS_SERVER_RESOLVER_TIMEOUT, 300))
                .put(DNSServerProfile.DNS_SERVER_RESOLVER_TYPE, DNSServerProfile.ResolverType.MANUAL.getName());

        TestAPIUtil.post(TestAPIConstants.DNS_SERVER_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(String.format("%s : response : %s", testInfo.getTestMethod().get().getName(), response.bodyAsJsonObject()));

            TestAPIUtil.assertCreateEntityTestResult(DNSServerProfileConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.DNS_SERVER_PROFILE.getName()), LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testRunDNSResolver(VertxTestContext testContext, TestInfo testInfo)
    {
        DNSCacheStore.getStore().add("***************");

        TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_DNS_RESOLVER, new JsonObject());

        TestUtil.vertx().setTimer(5 * 1000, handler ->
        {
            var items = DNSServerProfileConfigStore.getStore().getItems();

            var dnsServer = new JsonObject();

            for (var index = 0; index < items.size(); index++)
            {
                if (items.getJsonObject(index).getJsonObject(DNSServerProfile.DNS_SERVER_PROFILE_CONTEXT).getString(DNSServerProfile.DNS_SERVER_IP).equalsIgnoreCase("***********"))
                {
                    dnsServer = items.getJsonObject(index);
                }
            }

            LOGGER.info(String.format("%s : dns server : %s", testInfo.getTestMethod().get().getName(), dnsServer));

            Assertions.assertNotNull(dnsServer);

            Assertions.assertFalse(dnsServer.getJsonArray(DNSServerProfile.DNS_SERVER_RESOLVED_OBJECTS, new JsonArray()).isEmpty());

            Assertions.assertNotNull(dnsServer.getJsonArray(DNSServerProfile.DNS_SERVER_RESOLVED_OBJECTS).getLong(0));

            var obj = ObjectConfigStore.getStore().getItem(dnsServer.getJsonArray(DNSServerProfile.DNS_SERVER_RESOLVED_OBJECTS).getLong(0));

            LOGGER.info(String.format("%s : object : %s", testInfo.getTestMethod().get().getName(), obj));

            Assertions.assertNotNull(obj);

            Assertions.assertEquals("************", obj.getString(AIOpsObject.OBJECT_IP));

            Bootstrap.configDBService().update(DBConstants.TBL_OBJECT,
                    new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, obj.getLong(ID)),
                    new JsonObject().put(AIOpsObject.OBJECT_IP, "**********"),
                    DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                    result ->
                    {
                        if (result.succeeded())
                        {
                            LOGGER.info(String.format("Object %s ip updated successfully...", obj.getString(AIOpsObject.OBJECT_NAME)));

                            ObjectConfigStore.getStore().updateItem(obj.getLong(ID));

                            ObjectConfigStore.getStore().updateIP(obj.getString(AIOpsObject.OBJECT_IP), "**********");

                            LOGGER.info(String.format("%s : dns cache for *************** : %s", testInfo.getTestMethod().get().getName(), DNSCacheStore.getStore().get("***************")));

                            Assertions.assertEquals("testcase-domain-fdqn.com", DNSCacheStore.getStore().get("***************"));

                            testContext.completeNow();
                        }
                        else
                        {
                            LOGGER.warn(String.format("Failed to update object ip for %s with reason %s", obj.getString(AIOpsObject.OBJECT_NAME), result.cause().getMessage()));
                        }
                    });

        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testGetDNSServerProfiles(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(TestAPIConstants.DNS_SERVER_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(String.format("%s : Gelt All DNS Server Profiles Response : %s", testInfo.getTestMethod().get().getName(), response.bodyAsJsonObject()));

            TestAPIUtil.assertGETAllRequestTestResult(response, DNSServerProfileConfigStore.getStore(), null);

            testContext.completeNow();
        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testGetReference(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(TestAPIConstants.DNS_SERVER_API_ENDPOINT + "/" + DNSServerProfileConfigStore.getStore().getItems().getJsonObject(0).getLong(ID) + "/references", testContext.succeeding(response -> testContext.verify(() ->
        {
            LOGGER.info(String.format("%s : Get Reference Response : %s", testInfo.getTestMethod().get().getName(), response.bodyAsJsonObject()));

            TestAPIUtil.assertEntityReferenceTestResult(response, APIConstants.Entity.OBJECT.getName(), 1);

            testContext.completeNow();
        })));
    }
}
