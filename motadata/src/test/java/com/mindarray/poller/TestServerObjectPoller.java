/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.poller;

import com.mindarray.*;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.Metric;
import com.mindarray.api.MetricPlugin;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.GroupConfigStore;
import com.mindarray.store.MetricConfigStore;
import com.mindarray.store.MetricPluginConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.METRIC_PLUGIN_API_ENDPOINT;
import static com.mindarray.api.APIConstants.REQUEST_PARAM_IDS;
import static com.mindarray.api.MetricPlugin.METRIC_PLUGIN_ENTITY_TYPE;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.APPLICATION;
import static com.mindarray.nms.NMSConstants.OBJECTS;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(VertxExtension.class)
@Timeout(220 * 1000)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestServerObjectPoller
{

    public static final JsonObject POLLER_CONTEXT = new JsonObject();
    private static final Logger LOGGER = new Logger(TestServerObjectPoller.class, MOTADATA_NMS, "Server Poller Test");
    private static MessageConsumer<JsonObject> messageConsumer;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {

        try
        {
            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                    + "test" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + "poll-parameters.json");

            if (file.exists())
            {
                TestUtil.startEventStreaming(new JsonObject().put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_COMPLETED));

                POLLER_CONTEXT.mergeIn(new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)));

                testContext.completeNow();
            }
        }
        catch (Exception exception)
        {
            testContext.failNow(exception.getMessage());

            exception.printStackTrace();
        }
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        TestUtil.vertx().eventBus().send(UI_ACTION_METRIC_POLL_TIMESTAMP_FETCH, new JsonObject().put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(ID, MetricConfigStore.getStore().getItemByMetricPlugin(ObjectConfigStore.getStore().getItemByIP("************", NMSConstants.Type.LINUX).getLong(ID), NMSConstants.Type.LINUX.getName())));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testPollLinuxMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.LINUX.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testPollLinuxCPUCoreMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.LINUX_CPU_CORE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testPollLinuxDiskMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.LINUX_DISK.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testPollLinuxNetworkInterfaceMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.LINUX_NETWORK_INTERFACE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testPollLinuxAvailabilityMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.AVAILABILITY.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testPollLinuxNetworkServiceMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NETWORK_SERVICE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testPollLinuxProcessMetric(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        var metric = TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.LINUX_PROCESS.getName()), testContext);

        metric.put(OBJECTS, new JsonArray().add(metric.getJsonArray(OBJECTS).getJsonObject(0)));

        TestNMSUtil.assertMetricPollResponseTestResult(metric, testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testPollLinuxDirectoryMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.LINUX_DIR.getName()), testContext).put(OBJECTS, new JsonArray().add(new JsonObject().put(AIOpsObject.OBJECT_NAME, "/var/log"))), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testPollLinuxFileMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.LINUX_FILE.getName()), testContext).put(OBJECTS, new JsonArray().add(new JsonObject().put(AIOpsObject.OBJECT_NAME, "/etc/hosts"))), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testPollWindowsMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()), NMSConstants.Category.SERVER).getLong(GlobalConstants.ID)).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.WINDOWS.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testPollWindowsCPUCoreMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()), NMSConstants.Category.SERVER).getLong(GlobalConstants.ID)).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.WINDOWS_CPU_CORE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testPollWindowsDiskMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()), NMSConstants.Category.SERVER).getLong(GlobalConstants.ID)).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.WINDOWS_DISK.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testPollWindowsNetworkInterfaceMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()), NMSConstants.Category.SERVER).getLong(GlobalConstants.ID)).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.WINDOWS_NETWORK_INTERFACE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testPollWindowsAvailabilityMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()), NMSConstants.Category.SERVER).getLong(GlobalConstants.ID)).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.AVAILABILITY.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testPollWindowsNetworkServiceMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()), NMSConstants.Category.SERVER).getLong(GlobalConstants.ID)).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NETWORK_SERVICE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testPollWindowsProcessMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        var metric = TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()), NMSConstants.Category.SERVER).getLong(GlobalConstants.ID)).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.WINDOWS_PROCESS.getName()), testContext);

        metric.put(OBJECTS, new JsonArray().add(metric.getJsonArray(OBJECTS).getJsonObject(0)));

        TestNMSUtil.assertMetricPollResponseTestResult(metric, testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testPollWindowsDirectoryMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()), NMSConstants.Category.SERVER).getLong(GlobalConstants.ID)).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.WINDOWS_DIR.getName()), testContext).put(OBJECTS, new JsonArray().add(new JsonObject().put(AIOpsObject.OBJECT_NAME, "C://users"))), testContext);

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testPollWindowsFileMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()), NMSConstants.Category.SERVER).getLong(GlobalConstants.ID)).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.WINDOWS_FILE.getName()), testContext).put(OBJECTS, new JsonArray().add(new JsonObject().put(AIOpsObject.OBJECT_NAME, "C://Desktop/idea.sh"))), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testPollSolarisMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SOLARIS.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testPollSolarisCPUCoreMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SOLARIS_CPU_CORE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testPollSolarisDiskMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SOLARIS_DISK.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testPollSolarisNetworkInterfaceMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SOLARIS_NETWORK_INTERFACE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testPollSolarisAvailabilityMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.AVAILABILITY.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testPollSolarisNetworkServiceMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NETWORK_SERVICE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testPollSolarisProcessMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        var metric = TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SOLARIS_PROCESS.getName()), testContext);

        metric.put(OBJECTS, new JsonArray().add(metric.getJsonArray(OBJECTS).getJsonObject(0)));

        TestNMSUtil.assertMetricPollResponseTestResult(metric, testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testPollWindowsClusterMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.WINDOWS_CLUSTER.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testPollWindowsClusterDiskMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.WINDOWS_CLUSTER_DISK.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    void testPollIBMAIXMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.IBM_AIX.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    @Disabled
        //todo: when metric.state changed to enable, we will poll this metric
    void testPollIBMAIXCPUCoreMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.IBM_AIX_CPU_CORE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    void testPollIBMAIXDiskMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.IBM_AIX_DISK.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    void testPollIBMAIXNetworkInterfaceMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.IBM_AIX_NETWORK_INTERFACE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(32)
    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    void testPollIBMAIXNetworkServiceMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NETWORK_SERVICE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(33)
    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    void testPollHPUXMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.HP_UX.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(34)
    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    void testPollHPUXCPUCoreMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.HP_UX_CPU_CORE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(34)
    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    void testPollHPUXDiskMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.HP_UX_DISK.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(35)
    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    void testPollHPUXNetworkServiceMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NETWORK_SERVICE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(36)
    void testPollWindowsTaskSchedulerMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()), NMSConstants.Category.SERVER).getLong(GlobalConstants.ID)).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.WINDOWS_TASK_SCHEDULER.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(37)
    void testPollWindowsDotNetAppMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()), NMSConstants.Type.WINDOWS));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()), NMSConstants.Category.SERVER).getLong(GlobalConstants.ID)).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.DOTNET.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(38)
    void testPollLinux2Metric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.LINUX.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(39)
    void testGetLastPollTime1(VertxTestContext testContext, TestInfo testInfo)
    {
        var id = ObjectConfigStore.getStore().getItemByIP("************");

        Assertions.assertNotNull(id);

        var groups = new JsonArray(GroupConfigStore.getStore().getItems().stream().map(item -> JsonObject.mapFrom(item).getLong(ID)).collect(Collectors.toList()));

        assertNotNull(groups);

        Assertions.assertFalse(groups.isEmpty());

        var context = TestConstants.prepareParams("testCreateCustomMetricHavingGroup");

        context.put(MetricPlugin.METRIC_PLUGIN_ENTITIES, new JsonArray().add(id));

        var objects = new JsonArray(ObjectConfigStore.getStore().getItemsByMultiValueFieldAll(AIOpsObject.OBJECT_GROUPS, groups).stream().map(item -> JsonObject.mapFrom(item).getLong(ID)).collect(Collectors.toList()));

        assertNotNull(objects);

        Assertions.assertFalse(objects.isEmpty());

        if (!context.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT).containsKey(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE))
        {
            context.put(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE, MetricConfigStore.getStore().getItemsByObject(id).getFirst().getLong(Metric.METRIC_CREDENTIAL_PROFILE));
        }

        context.put(MetricPlugin.METRIC_PLUGIN_NAME, context.getString(MetricPlugin.METRIC_PLUGIN_NAME) + System.currentTimeMillis());

        context.put(METRIC_PLUGIN_ENTITY_TYPE, APIConstants.Entity.OBJECT.getName());

        context.put(MetricPlugin.METRIC_PLUGIN_TYPE, "Linux");

        LOGGER.info(String.format("context ; %s ", context.encode()));

        TestAPIUtil.post(METRIC_PLUGIN_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertNotNull(response.bodyAsJsonObject().getLong(ID));

                    LOGGER.info(String.format("API Response of Metric Plugin Create: %s ", response.bodyAsJsonObject().encode()));

                    TestAPIUtil.assertCreateEntityTestResult(MetricPluginConfigStore.getStore(), context, response.bodyAsJsonObject(),
                            String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.METRIC_PLUGIN.getName()), null, LOGGER, testInfo.getTestMethod().get().getName());

                    TestAPIUtil.put(METRIC_PLUGIN_API_ENDPOINT + "/" + response.bodyAsJsonObject().getLong(ID) + "/assign", new JsonObject().put(REQUEST_PARAM_IDS, new JsonArray().add(id)), testContext.succeeding(responseHandler ->
                            testContext.verify(() ->
                            {
                                LOGGER.info(String.format("API Response of Metric Plugin Update : %s ", responseHandler.bodyAsJsonObject().encode()));

                                assertEquals(String.format(InfoMessageConstants.ASSIGN_SUCCEEDED, APIConstants.Entity.METRIC_PLUGIN.getName()), responseHandler.bodyAsJsonObject().getString(GlobalConstants.MESSAGE));

                                assertEquals(SC_OK, responseHandler.bodyAsJsonObject().getInteger(APIConstants.RESPONSE_CODE));

                                var count = new AtomicInteger(0);

                                messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
                                {
                                    if (message.body().containsKey(EVENT_CONTEXT))
                                    {
                                        var event = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                                        if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_METRIC_POLL_TIMESTAMP_FETCH))
                                        {
                                            var items = MetricConfigStore.getStore().getItemsByObject(id);

                                            var metrics = new JsonArray();

                                            for (var item : items)
                                            {
                                                if ((item.getString(Metric.METRIC_TYPE).equalsIgnoreCase(NMSConstants.Type.LINUX.getName()) || (NMSConstants.CUSTOM_METRIC_TYPES.contains(item.getString(Metric.METRIC_TYPE)) && item.containsKey(Metric.METRIC_CONTEXT) && item.getJsonObject(Metric.METRIC_CONTEXT).getString(MetricPlugin.METRIC_PLUGIN_TYPE).equalsIgnoreCase(NMSConstants.Type.LINUX.getName()))) && (!metrics.contains(item.getString(Metric.METRIC_NAME))))
                                                {
                                                    metrics.add(item.getString(Metric.METRIC_NAME));
                                                }
                                            }

                                            var object = event.getJsonObject(RESULT);

                                            LOGGER.info(String.format("result ; %s ", object.encode()));

                                            LOGGER.info(String.format("metrics : %s ", metrics));

                                            for (var index = 0; index < metrics.size(); index++)
                                            {
                                                Assertions.assertTrue(object.containsKey(metrics.getString(index)));

                                                object.remove(metrics.getString(index));
                                            }

                                            object.remove(ID);

                                            object.remove(EVENT_TIMESTAMP);

                                            Assertions.assertEquals(0, object.size());

                                            messageConsumer.unregister(result -> testContext.completeNow());
                                        }
                                    }
                                });

                                TestUtil.vertx().setPeriodic(2000, timer ->
                                {
                                    TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_METRIC_POLL_SCHEDULE, id);

                                    count.getAndIncrement();

                                    if (count.get() == 10)
                                    {
                                        TestUtil.vertx().eventBus().send(UI_ACTION_METRIC_POLL_TIMESTAMP_FETCH, new JsonObject().put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(ID, id));

                                        TestUtil.vertx().cancelTimer(timer);
                                    }
                                });

                            })));

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(40)
    void testGetLastPollTime2(VertxTestContext testContext)
    {
        var id = ObjectConfigStore.getStore().getItemByIP("************");

        Assertions.assertNotNull(id);

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_CONTEXT))
            {
                var event = CodecUtil.toJSONObject(message.body().getBinary(EVENT_CONTEXT));

                if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_METRIC_POLL_TIMESTAMP_FETCH))
                {
                    var items = MetricConfigStore.getStore().getItemsByObject(id);

                    var metrics = new JsonArray();

                    for (var item : items)
                    {
                        if (item.getString(Metric.METRIC_TYPE).equalsIgnoreCase(NMSConstants.Type.MARIADB.getName()))
                        {
                            metrics.add(item.getString(Metric.METRIC_NAME));
                        }
                    }

                    var result = event.getJsonObject(RESULT);

                    LOGGER.info(String.format("result ; %s ", result.encode()));

                    LOGGER.info(String.format("metrics : %s ", metrics));

                    for (var index = 0; index < metrics.size(); index++)
                    {
                        Assertions.assertTrue(result.containsKey(metrics.getString(index)));

                        result.remove(metrics.getString(index));
                    }

                    result.remove(ID);

                    result.remove(EVENT_TIMESTAMP);

                    Assertions.assertEquals(0, result.size());

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        TestUtil.vertx().eventBus().send(UI_ACTION_METRIC_POLL_TIMESTAMP_FETCH, new JsonObject().put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(APPLICATION, NMSConstants.Type.MARIADB.getName()).put(ID, id));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(41)
    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    void testPollIBMAS400Metric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.IBM_AS_400.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(42)
    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    void testPollIBMAS400JobMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.IBM_AS_400_JOB.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(43)
    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    void testPollIBMAS400PoolMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.IBM_AS_400_POOL.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(44)
    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    void testPollIBMAS400MessageMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName())));

        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.IBM_AS_400_MESSAGE.getName()), testContext), testContext);
    }

}
