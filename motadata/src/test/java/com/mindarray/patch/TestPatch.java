/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.patch;

import com.mindarray.*;
import com.mindarray.agent.AgentConstants;
import com.mindarray.api.*;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.integration.IntegrationConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.notification.Notification;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.runbook.Runbook;
import com.mindarray.store.*;
import com.mindarray.util.*;
import com.mindarray.visualization.VisualizationConstants;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.api.Agent.*;
import static com.mindarray.api.CredentialProfile.DEFAULT_EMAIL_CREDENTIAL_PROFILE;
import static com.mindarray.api.LDAPServer.*;
import static com.mindarray.api.MailServerConfiguration.MAIL_SERVER_CREDENTIAL_PROFILE;
import static com.mindarray.api.Metric.METRIC_TYPE;
import static com.mindarray.api.MetricPolicy.POLICY_RENOTIFY;
import static com.mindarray.api.PasswordPolicy.PASSWORD_POLICY_MAXIMUM_LENGTH;
import static com.mindarray.api.RemoteEventProcessor.*;
import static com.mindarray.api.Scheduler.SCHEDULER_CONTEXT;
import static com.mindarray.api.Scheduler.SCHEDULER_STATE;
import static com.mindarray.api.Template.*;
import static com.mindarray.db.DBConstants.*;
import static com.mindarray.policy.PolicyEngineConstants.*;
import static com.mindarray.util.CronExpressionUtil.CRON_ONCE;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|SLAVE$")
@Timeout(60 * 1000)
public class TestPatch
{
    private static final Logger LOGGER = new Logger(TestPatch.class, MOTADATA_PATCH, "Patch Test");

    private static final JsonArray OBJECTS = new JsonArray();

    private static final JsonObject CONTEXT = new JsonObject();
    private static final String policyKey = String.join(SEPARATOR, "1000", "6354", "Metric Policy", "system.cpu.percent");
    private static long templateId;

    private static long policyId;

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testRegisterRemoteEntities(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        save(new JsonObject().put(REMOTE_EVENT_PROCESSOR_HOST, "motadatadefaulttest")
                .put(REMOTE_EVENT_PROCESSOR_TYPE, "DEFAULT")
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_IP, "************")
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, UUID.randomUUID().toString().replace("-", EMPTY_VALUE).trim().toUpperCase())
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_VERSION, "8.0.0")
                .put("remote.event.type", "REMOTE POLLER"), TBL_REMOTE_EVENT_PROCESSOR, RemoteEventProcessorConfigStore.getStore());

        save(new JsonObject().put(REMOTE_EVENT_PROCESSOR_HOST, "motadatacollectortest")
                .put(REMOTE_EVENT_PROCESSOR_TYPE, "COLLECTOR")
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_IP, "************")
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, UUID.randomUUID().toString().replace("-", EMPTY_VALUE).trim().toUpperCase())
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_VERSION, "8.0.0")
                .put("remote.event.type", "REMOTE POLLER"), TBL_REMOTE_EVENT_PROCESSOR, RemoteEventProcessorConfigStore.getStore());

        OBJECTS.add("motadatacollectortest");

        save(new JsonObject().put(REMOTE_EVENT_PROCESSOR_HOST, "datastoretest")
                .put(REMOTE_EVENT_PROCESSOR_TYPE, "PRIMARY")
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_IP, "************")
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, UUID.randomUUID().toString().replace("-", EMPTY_VALUE).trim().toUpperCase())
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_VERSION, "8.0.0")
                .put("remote.event.type", "DATANODE"), TBL_REMOTE_EVENT_PROCESSOR, RemoteEventProcessorConfigStore.getStore());

        OBJECTS.add("datastoretest");

        save(new JsonObject().put(REMOTE_EVENT_PROCESSOR_HOST, "datastoresecondarytest")
                .put(REMOTE_EVENT_PROCESSOR_TYPE, "SECONDARY")
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_IP, "************")
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, UUID.randomUUID().toString().replace("-", EMPTY_VALUE).trim().toUpperCase())
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_VERSION, "8.0.0")
                .put("remote.event.type", "DATANODE"), TBL_REMOTE_EVENT_PROCESSOR, RemoteEventProcessorConfigStore.getStore());

        OBJECTS.add("datastoresecondarytest");

        save(new JsonObject().put(REMOTE_EVENT_PROCESSOR_HOST, "mastertest")
                        .put(REMOTE_EVENT_PROCESSOR_TYPE, "MASTER")
                        .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_IP, "************")
                        .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, UUID.randomUUID().toString().replace("-", EMPTY_VALUE).trim().toUpperCase())
                        .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_VERSION, "8.0.0")
                , TBL_REMOTE_EVENT_PROCESSOR, RemoteEventProcessorConfigStore.getStore());

        OBJECTS.add("mastertest");

        save(new JsonObject().put(REMOTE_EVENT_PROCESSOR_HOST, "slavetest")
                .put(REMOTE_EVENT_PROCESSOR_TYPE, "SLAVE")
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_IP, "************")
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, UUID.randomUUID().toString().replace("-", EMPTY_VALUE).trim().toUpperCase())
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_VERSION, "8.0.0"), TBL_REMOTE_EVENT_PROCESSOR, RemoteEventProcessorConfigStore.getStore());

        OBJECTS.add("slavetest");

        save(new JsonObject().put(REMOTE_EVENT_PROCESSOR_HOST, "replicatest")
                .put(REMOTE_EVENT_PROCESSOR_TYPE, "REPLICA")
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_IP, "************")
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, UUID.randomUUID().toString().replace("-", EMPTY_VALUE).trim().toUpperCase())
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_VERSION, "8.0.0")
                .put("remote.event.type", "REPLICA"), TBL_REMOTE_EVENT_PROCESSOR, RemoteEventProcessorConfigStore.getStore());

        OBJECTS.add("replicatest");

        save(new JsonObject().put(AGENT_UUID, "QWERTYUIOP")
                .put(AGENT_CONFIGS, new JsonObject())
                .put(AGENT_DEFAULT_CONFIG, new JsonObject())
                .put(AGENT_VERSION, MotadataConfigUtil.getVersion())
                .put(AGENT_BUSINESS_HOUR_PROFILE, DEFAULT_ID)
                .put(AGENT_STATE, NMSConstants.State.ENABLE.name())
                .put(ID, CommonUtil.newId())
                .put(AGENT_STATUS_TYPE, AgentConstants.AgentStatusType.HEARTBEAT.getName())
                .put(AGENT_STATUS, STATUS_UP), TBL_AGENT, AgentConfigStore.getStore());

        save(new JsonObject("{\n  \"artifact.id\": \"D30B554580A6442F9CE2E77295433773\",\n  \"artifact.type\": \"agent\",\n  \"artifact.version\": \"8.0.0\",\n  \"error.code\": \"MD000\",\n  \"_type\": \"1\"\n}"), TBL_ARTIFACT, ArtifactConfigStore.getStore());

        save(new JsonObject("{\n  \"artifact.id\": \"D30B554580A6E77295433773\",\n  \"artifact.type\": \"secondary\",\n  \"artifact.version\": \"8.0.0\",\n  \"error.code\": \"MD000\",\n  \"_type\": \"1\"\n}"), TBL_ARTIFACT, ArtifactConfigStore.getStore());

        save(new JsonObject("{\n  \"artifact.id\": \"QWERTYUIOP\",\n  \"artifact.type\": \"agent\",\n  \"artifact.version\": \"" + MotadataConfigUtil.getVersion() + "\",\n  \"error.code\": \"MD000\",\n  \"_type\": \"1\"\n}"), TBL_ARTIFACT, ArtifactConfigStore.getStore());

        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id -> testContext.completeNow());
    }

    @RepeatedIfExceptionsTest(suspend = 3000)
    @Order(2)
    void testApplyAndValidatePatch8_0_4(VertxTestContext vertxTestContext) throws Exception
    {
        var clazz = Patch804.class.getConstructor().newInstance();

        clazz.doPatch();

        TestUtil.vertx().setTimer(12 * 1000, handler ->
        {
            LOGGER.info(String.format("remote event processor : %s ", RemoteEventProcessorConfigStore.getStore().getItems()));

            var item = RemoteEventProcessorConfigStore.getStore().getItemByValue(REMOTE_EVENT_PROCESSOR_TYPE, "DEFAULT");

            assertNull(item);

            item = RemoteEventProcessorConfigStore.getStore().getItemByValue(REMOTE_EVENT_PROCESSOR_TYPE, "MASTER");

            assertNull(item);

            LOGGER.info("current version " + MotadataConfigUtil.getVersion());

            var processors = RemoteEventProcessorConfigStore.getStore().getItems();

            for (var index = 0; index < processors.size(); index++)
            {
                var processor = processors.getJsonObject(index);

                LOGGER.trace(String.format("processor : %s ", processor.encode()));

                assertTrue(processor.containsKey(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE));

                if (!processor.getString(REMOTE_EVENT_PROCESSOR_UUID).equalsIgnoreCase(Bootstrap.getRegistrationId()) && !processor.getString(REMOTE_EVENT_PROCESSOR_UUID).equalsIgnoreCase("8179c220-0d38-45bb-bbb8-4fc50d746f7a"))
                {
                    assertTrue(processor.getString(REMOTE_EVENT_PROCESSOR_VERSION).equalsIgnoreCase("8.0.4"));
                }

                if (processor.getString(REMOTE_EVENT_PROCESSOR_HOST).equalsIgnoreCase("motadatadefaulttest"))
                {
                    assertTrue(processor.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(GlobalConstants.BootstrapType.APP.name()));

                    assertTrue(processor.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE).equalsIgnoreCase(GlobalConstants.InstallationMode.STANDALONE.name()));
                }
                else if (processor.getString(REMOTE_EVENT_PROCESSOR_HOST).equalsIgnoreCase("mastertest"))
                {
                    assertTrue(processor.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(GlobalConstants.BootstrapType.APP.name()));

                    assertTrue(processor.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE).equalsIgnoreCase(GlobalConstants.InstallationMode.PRIMARY.name()));
                }
                else if (processor.getString(REMOTE_EVENT_PROCESSOR_HOST).equalsIgnoreCase("slavetest"))
                {
                    assertTrue(processor.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(GlobalConstants.BootstrapType.APP.name()));

                    assertTrue(processor.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE).equalsIgnoreCase(InstallationMode.SECONDARY.name()));
                }
                else if (processor.getString(REMOTE_EVENT_PROCESSOR_HOST).equalsIgnoreCase("replicatest"))
                {
                    assertTrue(processor.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(GlobalConstants.BootstrapType.APP.name()));

                    assertTrue(processor.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE).equalsIgnoreCase(InstallationMode.FAILOVER.name()));
                }
                else if (processor.getString(REMOTE_EVENT_PROCESSOR_HOST).equalsIgnoreCase("motadatacollectortest"))
                {
                    assertTrue(processor.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(GlobalConstants.BootstrapType.COLLECTOR.name()));

                    assertTrue(processor.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE).equalsIgnoreCase(GlobalConstants.InstallationMode.STANDALONE.name()));
                }
                else if (processor.getString(REMOTE_EVENT_PROCESSOR_HOST).equalsIgnoreCase("datastoretest"))
                {
                    assertTrue(processor.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(GlobalConstants.BootstrapType.DATASTORE.name()));

                    assertTrue(processor.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE).equalsIgnoreCase(GlobalConstants.InstallationMode.PRIMARY.name()));
                }
                else if (processor.getString(REMOTE_EVENT_PROCESSOR_HOST).equalsIgnoreCase("datastoresecondarytest"))
                {
                    assertTrue(processor.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(GlobalConstants.BootstrapType.DATASTORE.name()));

                    assertTrue(processor.getString(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE).equalsIgnoreCase(InstallationMode.SECONDARY.name()));
                }
            }

            var items = ArtifactConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                var artifact = items.getJsonObject(index);

                LOGGER.trace(String.format("artifact : %s ", artifact.encode()));

                if (artifact.getString(MotadataApp.ARTIFACT_ID).equalsIgnoreCase("D30B554580A6442F9CE2E77295433773"))
                {
                    assertTrue(artifact.getString(MotadataApp.ARTIFACT_TYPE).equalsIgnoreCase("agent"));

                    assertTrue(artifact.getString(MotadataApp.ARTIFACT_VERSION).equalsIgnoreCase("8.0.4"));

                    assertTrue(artifact.containsKey(MotadataApp.ARTIFACT_MODE));

                    assertTrue(artifact.getString(MotadataApp.ARTIFACT_MODE).equalsIgnoreCase("standalone"));
                }
                else if (artifact.getString(MotadataApp.ARTIFACT_ID).equalsIgnoreCase("D30B554580A6E77295433773"))
                {
                    assertTrue(artifact.getString(MotadataApp.ARTIFACT_TYPE).equalsIgnoreCase("datastore"));

                    assertTrue(artifact.getString(MotadataApp.ARTIFACT_VERSION).equalsIgnoreCase("8.0.4"));

                    assertTrue(artifact.containsKey(MotadataApp.ARTIFACT_MODE));

                    assertTrue(artifact.getString(MotadataApp.ARTIFACT_MODE).equalsIgnoreCase("secondary"));
                }
            }

            for (var index = 0; index < OBJECTS.size(); index++)
            {
                delete(RemoteEventProcessorConfigStore.getStore().getItemByValue(REMOTE_EVENT_PROCESSOR_HOST, OBJECTS.getString(index)).getLong(ID), RemoteEventProcessorConfigStore.getStore());
            }

            vertxTestContext.completeNow();

        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000)
    @Order(3)
    void testPatch8_0_2(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

            var clazz = Patch802.class.getConstructor().newInstance();

            clazz.doPatch();

            TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), id ->
            {
                var item = AgentConfigStore.getStore().getItem(AgentConfigStore.getStore().getAgentId("QWERTYUIOP"));

                LOGGER.info(String.format("item : %s", item));

                assertNotNull(item);

                assertNotNull(item.getString(AGENT_STATUS));

                assertEquals(STATUS_DOWN, item.getString(AGENT_STATUS, EMPTY_VALUE));

                testContext.completeNow();
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000)
    @Order(4)
    void testPatch8_0_6(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

            var clazz = Patch806.class.getConstructor().newInstance();

            var promises = new ArrayList<Future<Void>>();

            var updateMetricPolicy = Promise.<Void>promise();

            var updateEventPolicy = Promise.<Void>promise();

            promises.add(updateMetricPolicy.future());

            promises.add(updateEventPolicy.future());

            LOGGER.info("Before update metric policy : " + MetricPolicyConfigStore.getStore().getItem(10000000000001L).encode());

            LOGGER.info("Before update event policy : " + EventPolicyConfigStore.getStore().getItem(10000000000110L).encode());

            Bootstrap.configDBService.update(TBL_METRIC_POLICY,
                    new JsonObject().put(FIELD_NAME, ID).put(VALUE, 10000000000001L),
                    MetricPolicyConfigStore.getStore().getItem(10000000000001L).put(POLICY_EMAIL_NOTIFICATION_RECIPIENTS, new JsonArray().add("<EMAIL>")).put(POLICY_RENOTIFY, NO),
                    DEFAULT_USER, MOTADATA_SYSTEM,
                    result ->
                    {

                        if (result.succeeded())
                        {

                            MetricPolicyConfigStore.getStore().updateItem(10000000000001L).onComplete(asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    LOGGER.info("After update metric policy : " + MetricPolicyConfigStore.getStore().getItem(10000000000001L).encode());

                                    updateMetricPolicy.complete();
                                }
                                else
                                {
                                    LOGGER.info("Failed to update metric policy : " + asyncResult.cause());

                                    updateMetricPolicy.fail(result.cause());
                                }
                            });
                        }
                        else
                        {
                            LOGGER.info("Failed to update metric policy : " + result.cause());

                            updateMetricPolicy.fail(result.cause());
                        }
                    });

            Bootstrap.configDBService.update(TBL_EVENT_POLICY,
                    new JsonObject().put(FIELD_NAME, ID).put(VALUE, 10000000000110L),
                    EventPolicyConfigStore.getStore().getItem(10000000000110L).put(POLICY_EMAIL_NOTIFICATION_RECIPIENTS, new JsonArray().add("<EMAIL>")),
                    DEFAULT_USER, MOTADATA_SYSTEM,
                    result ->
                    {

                        if (result.succeeded())
                        {
                            EventPolicyConfigStore.getStore().updateItem(10000000000110L).onComplete(asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    LOGGER.info("After update event policy : " + EventPolicyConfigStore.getStore().getItem(10000000000110L).encode());

                                    updateEventPolicy.complete();
                                }
                                else
                                {
                                    LOGGER.info("Failed to update event policy : " + asyncResult.cause());

                                    updateEventPolicy.fail(result.cause());
                                }
                            });
                        }
                        else
                        {
                            LOGGER.info("Failed to update event policy : " + result.cause());

                            updateEventPolicy.fail(result.cause());
                        }
                    });

            Future.join(promises).onComplete(result ->
            {
                if (result.succeeded())
                {
                    clazz.doPatch().onComplete(asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            LOGGER.info("Patch executed successfully ...");

                            var item = RebrandingConfigStore.getStore().getItem();

                            assertNotNull(item);

                            LOGGER.info(String.format("item : %s", item.encode()));

                            assertEquals(DEFAULT_ID, item.getLong(ID));

                            var updatedMetricPolicy = MetricPolicyConfigStore.getStore().getItem(10000000000001L);

                            var updatedEventPolicy = EventPolicyConfigStore.getStore().getItem(10000000000110L);

                            LOGGER.info("After patch metric policy : " + updatedMetricPolicy.encode());

                            LOGGER.info("After patch event policy : " + updatedEventPolicy.encode());

                            assertTrue(updatedMetricPolicy.containsKey(POLICY_NOTIFICATION_CONTEXT) && updatedMetricPolicy.containsKey(POLICY_RENOTIFICATION_CONTEXT));

                            assertTrue(updatedEventPolicy.containsKey(POLICY_NOTIFICATION_CONTEXT));

                            assertNotNull(updatedMetricPolicy.getJsonArray(POLICY_NOTIFICATION_CONTEXT));

                            assertTrue(updatedMetricPolicy.getJsonArray(POLICY_NOTIFICATION_CONTEXT).getJsonObject(0).getJsonArray(POLICY_EMAIL_NOTIFICATION_RECIPIENTS).contains("<EMAIL>"));

                            assertTrue(updatedMetricPolicy.getJsonArray(POLICY_RENOTIFICATION_CONTEXT).getJsonObject(0).getJsonArray(POLICY_EMAIL_NOTIFICATION_RECIPIENTS).contains("<EMAIL>"));

                            assertTrue(updatedEventPolicy.getJsonArray(POLICY_NOTIFICATION_CONTEXT).getJsonObject(0).getJsonArray(POLICY_EMAIL_NOTIFICATION_RECIPIENTS).contains("<EMAIL>"));

                            testContext.completeNow();
                        }
                        else
                        {
                            LOGGER.error(asyncResult.cause());

                            testContext.failNow(asyncResult.cause());
                        }
                    });
                }
                else
                {
                    testContext.failNow(result.cause());
                }
            });

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000)
    @Order(5)
    void testPatch8_0_7(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

            var dateTime = DateTimeUtil.getScheduledTimestamp(System.currentTimeMillis() + (2000 * 1000L));

            var event = new JsonObject().put(Scheduler.SCHEDULER_TIMELINE, CRON_ONCE)
                    .put(Scheduler.SCHEDULER_TIMES, new JsonArray().add(dateTime.split(" ")[1]))
                    .put(Scheduler.SCHEDULER_START_DATE, dateTime.split(" ")[0])
                    .put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.TOPOLOGY.getName())
                    .put(SCHEDULER_STATE, YES)
                    .put(SCHEDULER_CONTEXT, new JsonObject().put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS, new JsonArray().add(218956789L))
                            .put(NMSConstants.TOPOLOGY_LINK_LAYER, new JsonArray().add("L2").add("L3")));

            Bootstrap.configDBService().save(DBConstants.TBL_SCHEDULER, event, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
            {
                if (result.succeeded())
                {
                    SchedulerConfigStore.getStore().addItem(result.result()).onComplete(asyncResult -> JobScheduler.scheduleCustomJob(SchedulerConfigStore.getStore().getItem(result.result())));

                    TestUtil.vertx().setTimer(3000, timer ->
                    {

                        try
                        {
                            var clazz = Patch807.class.getConstructor().newInstance();

                            clazz.doPatch().onComplete(asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    LOGGER.info("Patch executed successfully ...");

                                    // C drive patch

                                    assertNull(SystemFileConfigStore.getStore().getItem(10000000000001L));

                                    var items = MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.WINDOWS_DIR.getName());

                                    for (var index = 0; index < items.size(); index++)
                                    {
                                        var item = items.getJsonObject(index);

                                        var context = item.getJsonObject(Metric.METRIC_CONTEXT);

                                        if (context != null && !context.isEmpty() && context.containsKey(NMSConstants.OBJECTS) && CommonUtil.isNotNullOrEmpty(context.getJsonArray(NMSConstants.OBJECTS)))
                                        {
                                            for (var object : context.getJsonArray(NMSConstants.OBJECTS))
                                            {
                                                assertFalse(JsonObject.mapFrom(object).getString(AIOpsObject.OBJECT_NAME).equalsIgnoreCase("C:\\"));
                                            }
                                        }

                                        if (item.containsKey(NMSConstants.METRIC_INSTANCES) && CommonUtil.isNotNullOrEmpty(item.getJsonArray(NMSConstants.METRIC_INSTANCES)))
                                        {
                                            for (var object : item.getJsonArray(NMSConstants.METRIC_INSTANCES))
                                            {
                                                assertNotEquals("C:\\", object);
                                            }
                                        }
                                    }

                                    // network layer protocols patch

                                    var ids = SchedulerConfigStore.getStore().flatItems(Scheduler.SCHEDULER_JOB_TYPE, "Topology", ID);

                                    assertNotNull(ids);

                                    assertFalse(ids.isEmpty());

                                    var valid = true;

                                    for (var index = 0; index < ids.size(); index++)
                                    {
                                        var item = SchedulerConfigStore.getStore().getItem(ids.getLong(index));

                                        LOGGER.info(String.format("item : %s", item.encode()));

                                        if (!item.getJsonObject(SCHEDULER_CONTEXT).containsKey(NMSConstants.TOPOLOGY_PROTOCOLS))
                                        {
                                            valid = false;

                                            break;
                                        }
                                        else
                                        {
                                            assertFalse(item.getJsonObject(SCHEDULER_CONTEXT).getJsonArray(NMSConstants.TOPOLOGY_PROTOCOLS).isEmpty());
                                        }
                                    }

                                    if (valid)
                                    {
                                        testContext.completeNow();
                                    }
                                    else
                                    {
                                        testContext.failNow("invalid item");
                                    }
                                }
                                else
                                {
                                    LOGGER.error(asyncResult.cause());

                                    testContext.failNow(asyncResult.cause());
                                }
                            });
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            testContext.failNow(exception);
                        }
                    });
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    @Timeout(120 * 1000)
    void testCreateLDAPEntities(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

            LDAPServerConfigStore.getStore().deleteItems(LDAPServerConfigStore.getStore().getIds());

            var ldapServerContext = new JsonObject("{\"ldap.server.auto.sync\": \"no\",\"ldap.server.enable.auth\": \"no\",\"ldap.server.fqdn\": \"motadata.in\",\"ldap.server.host\": \"************\",\"ldap.server.port\": 389,\"ldap.server.username\": \"dhvani\",\"_type\": \"0\",\"ldap.server.password\": \"Mind@123\",\"ldap.server.protocol\": \"PLAIN\",\"ldap.server.user.groups\": [\"motadata users\"],\"id\": 10000000000001}");

            var userContext = new JsonObject("{\"user.name\": \"testldap\",\"user.first.name\": \"test\",\"user.last.name\": \"ldap\",\"user.status\": \"yes\",\"user.type\": \"LDAP\",\"_type\": \"1\",\"user.preferences\": {}}");

            Bootstrap.configDBService().save(TBL_LDAP_SERVER, ldapServerContext, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
            {
                if (result.succeeded())
                {
                    LDAPServerConfigStore.getStore().addItem(result.result());

                    var count = new AtomicInteger(0);

                    TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(3), timer ->
                    {
                        if (LDAPServerConfigStore.getStore().getItem(10000000000001L) != null && !LDAPServerConfigStore.getStore().getItem(10000000000001L).isEmpty())
                        {
                            TestUtil.vertx().cancelTimer(timer);

                            Bootstrap.configDBService().save(TBL_USER, userContext, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    UserConfigStore.getStore().addItem(asyncResult.result());

                                    count.set(0);

                                    TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(3), timerId ->
                                    {
                                        if (UserConfigStore.getStore().getItemByValue(User.USER_NAME, "testldap") != null && !UserConfigStore.getStore().getItemByValue(User.USER_NAME, "testldap").isEmpty())
                                        {
                                            TestUtil.vertx().cancelTimer(timerId);

                                            testContext.completeNow();
                                        }
                                        else if (count.incrementAndGet() > 5)
                                        {
                                            TestUtil.vertx().cancelTimer(timer);

                                            LOGGER.warn("failed to create user within time");

                                            testContext.failNow("failed to create user within time");
                                        }
                                    });
                                }
                                else
                                {
                                    LOGGER.warn("failed to add LDAP user");

                                    testContext.failNow(asyncResult.cause());
                                }
                            });
                        }
                        else if (count.incrementAndGet() > 5)
                        {
                            TestUtil.vertx().cancelTimer(timer);

                            LOGGER.warn("failed to create LDAP server within time");

                            testContext.failNow("failed to create LDAP server within time");
                        }
                    });
                }
                else
                {
                    LOGGER.warn("failed to add LDAP server");

                    testContext.failNow(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    @Timeout(120 * 1000)
    void testExecute807LDAPServerPatch(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

            var clazz = Patch807.class.getConstructor().newInstance();

            var ldapServerContext = new JsonObject("{\"ldap.server.auto.sync\": \"no\",\"ldap.server.enable.auth\": \"no\",\"ldap.server.fqdn\": \"motadata.in\",\"ldap.server.host\": \"************\",\"ldap.server.port\": 389,\"ldap.server.username\": \"dhvani\",\"ldap.server.password\": \"Mind@123\",\"ldap.server.protocol\": \"PLAIN\",\"ldap.server.user.groups\": [\"motadata users\"],\"id\": 10000000000001}");

            clazz.doPatch().onComplete(response ->
            {
                if (response.succeeded())
                {
                    var user = UserConfigStore.getStore().getItemByValue(User.USER_NAME, "testldap");

                    if (user != null && !user.isEmpty())
                    {
                        assertTrue(user.containsKey(User.USER_LDAP_SERVER));

                        var item = LDAPServerConfigStore.getStore().getItem(10000000000001L);

                        assertNotNull(item);

                        assertFalse(item.isEmpty());

                        assertEquals(ENTITY_TYPE_USER, item.getString(FIELD_TYPE));

                        Bootstrap.configDBService().delete(TBL_LDAP_SERVER,
                                new JsonObject().put(FIELD_NAME, ID).put(VALUE, 10000000000001L),
                                DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                                {
                                    if (result.succeeded())
                                    {
                                        LDAPServerConfigStore.getStore().deleteItem(10000000000001L);

                                        ldapServerContext.remove("ldap.server.host");

                                        var count = new AtomicInteger(0);

                                        Bootstrap.configDBService().save(TBL_LDAP_SERVER,
                                                ldapServerContext,
                                                DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, future ->
                                                {
                                                    if (future.succeeded())
                                                    {
                                                        LDAPServerConfigStore.getStore().addItem(future.result());

                                                        TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(3), timer ->
                                                        {
                                                            if (LDAPServerConfigStore.getStore().getItem() != null && !LDAPServerConfigStore.getStore().getItem().isEmpty())
                                                            {
                                                                clazz.doPatch().onComplete(asyncResult ->
                                                                {
                                                                    if (asyncResult.succeeded())
                                                                    {
                                                                        assertNull(LDAPServerConfigStore.getStore().getItem());

                                                                        testContext.completeNow();
                                                                    }
                                                                    else
                                                                    {
                                                                        LOGGER.warn("failed to execute patch");

                                                                        testContext.failNow(asyncResult.cause());
                                                                    }
                                                                });
                                                            }
                                                            else if (count.incrementAndGet() > 5)
                                                            {
                                                                TestUtil.vertx().cancelTimer(timer);

                                                                LOGGER.warn("failed to create LDAP server within time");

                                                                testContext.failNow("failed to create LDAP server within time");
                                                            }
                                                        });
                                                    }
                                                    else
                                                    {
                                                        LOGGER.warn("failed to update LDAP server");

                                                        testContext.failNow(future.cause());
                                                    }
                                                });
                                    }
                                    else
                                    {
                                        LOGGER.warn("failed to delete LDAP server");

                                        testContext.failNow(result.cause());
                                    }
                                });
                    }
                    else
                    {
                        LOGGER.warn("failed to get user");

                        testContext.failNow("failed to get user");
                    }
                }
                else
                {
                    LOGGER.warn("failed to execute patch");

                    testContext.failNow(response.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testExecute809TagPatch(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

            var clazz = Patch809.class.getConstructor().newInstance();

            var item = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemByIP("**********"));

            item.put(AIOpsObject.OBJECT_USER_TAGS, new JsonArray().add("TEst:AIOPS").add("test-motadata"));

            item.put(AIOpsObject.OBJECT_SYSTEM_TAGS, new JsonArray().add("TEst:AIOPS").add("test-motadata"));

            Bootstrap.configDBService().update(DBConstants.TBL_OBJECT, new JsonObject().put(FIELD_NAME, GlobalConstants.ID).put(VALUE, item.getLong(ID)),
                    item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                    {
                        if (result.succeeded())
                        {
                            ObjectConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    LOGGER.info(String.format("Successfully updated object %s", item.getString(AIOpsObject.OBJECT_NAME, EMPTY_VALUE)));

                                    clazz.doPatch().onComplete(response ->
                                    {
                                        if (response.succeeded())
                                        {
                                            Assertions.assertTrue(ObjectConfigStore.getStore().getItem(item.getLong(ID)).getJsonArray(AIOpsObject.OBJECT_USER_TAGS).contains("test:aiops"));

                                            Assertions.assertTrue(ObjectConfigStore.getStore().getItem(item.getLong(ID)).getJsonArray(AIOpsObject.OBJECT_SYSTEM_TAGS).contains("test:aiops"));

                                            testContext.completeNow();
                                        }
                                        else
                                        {
                                            LOGGER.warn("failed to execute patch");

                                            testContext.failNow(response.cause());
                                        }
                                    });

                                }
                                else
                                {
                                    LOGGER.warn(String.format("Unable to update object store item %s with reason %s", item.encode(), asyncResult.cause().getMessage()));
                                }
                            });
                        }
                        else
                        {
                            LOGGER.warn(String.format("Unable to update object config store item %s with reason %s", item.encode(), result.cause().getMessage()));
                        }
                    });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testExecute808Patch(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

            var clazz = Patch808.class.getConstructor().newInstance();

            clazz.doPatch().onComplete(response ->
            {
                if (response.succeeded())
                {
                    var context = DataRetentionPolicyConfigStore.getStore().getItem(DEFAULT_ID).getJsonObject(DataRetentionPolicy.DATA_RETENTION_POLICY_CONTEXT);

                    Assertions.assertEquals(15, context.getJsonObject("CONFIG").getInteger(VERSION));

                    testContext.completeNow();
                }
                else
                {
                    LOGGER.warn("failed to execute patch");

                    testContext.failNow(response.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testExecute8010Patch(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));
            var clazz = Patch8010.class.getConstructor().newInstance();
            clazz.doPatch().onComplete(response ->
            {
                if (response.succeeded())
                {
                    var context = DataRetentionPolicyConfigStore.getStore().getItem(DEFAULT_ID).getJsonObject(DataRetentionPolicy.DATA_RETENTION_POLICY_CONTEXT);
                    Assertions.assertEquals(2, context.getJsonObject("FLOW").getInteger("raw"));
                    testContext.completeNow();
                }
                else
                {
                    LOGGER.warn("failed to execute patch");
                    testContext.failNow(response.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testExecute8011Patch(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

            var clazz = Patch8011.class.getConstructor().newInstance();

            var logCategoryContext = new JsonObject("{\"runbook.plugin.name\": \"Test Runbook 1\",\"runbook.plugin.type\": \"Custom Script\",\"runbook.plugin.entity.type\": \"Monitor\",\"runbook.plugin.entities\": [],\"runbook.plugin.log.collection\": \"yes\",\"runbook.plugin.variables\": {\"service.name\": \"service name\"},\"runbook.plugin.context\": {\"script\": \"\",\"script.language\": \"go\"}}");

            var otherCategoryContext = new JsonObject("{\"runbook.plugin.name\": \"Test Runbook 2\",\"runbook.plugin.type\": \"Trace Route\",\"runbook.plugin.entity.type\": \"Monitor\",\"runbook.plugin.entities\": [],\"runbook.plugin.context\": {\"timeout\": 30,\"max.hops\": 30,\"retries\": 1}}");

            Bootstrap.configDBService().save(TBL_RUNBOOK_PLUGIN, logCategoryContext, "admin", "127.0.0.1", result ->
            {
                if (result.succeeded())
                {
                    RunbookPluginConfigStore.getStore().addItem(result.result());

                    Bootstrap.configDBService().save(TBL_RUNBOOK_PLUGIN, otherCategoryContext, "admin", "127.0.0.1", asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            RunbookPluginConfigStore.getStore().addItem(asyncResult.result()).onComplete(voidResult ->
                            {
                                if (voidResult.succeeded())
                                {
                                    clazz.doPatch().onComplete(response ->
                                    {
                                        if (response.succeeded())
                                        {
                                            try
                                            {
                                                var logCategoryItem = RunbookPluginConfigStore.getStore().getItemByValue(RunbookPlugin.RUNBOOK_PLUGIN_NAME, "Test Runbook 1");

                                                LOGGER.trace("logCategoryItem : " + logCategoryItem.encode());

                                                Assertions.assertNotNull(logCategoryItem);

                                                Assertions.assertEquals(Runbook.RunbookCategory.LOG_COLLECTION.getName(), logCategoryItem.getString(RunbookPlugin.RUNBOOK_PLUGIN_CATEGORY));

                                                var otherCategoryItem = RunbookPluginConfigStore.getStore().getItemByValue(RunbookPlugin.RUNBOOK_PLUGIN_NAME, "Test Runbook 2");

                                                LOGGER.trace("otherCategoryItem : " + otherCategoryItem.encode());

                                                Assertions.assertNotNull(otherCategoryItem);

                                                Assertions.assertEquals(Runbook.RunbookCategory.OTHER.getName(), otherCategoryItem.getString(RunbookPlugin.RUNBOOK_PLUGIN_CATEGORY));

                                                RunbookPluginConfigStore.getStore().deleteItem(otherCategoryItem.getLong(ID));

                                                RunbookPluginConfigStore.getStore().deleteItem(logCategoryItem.getLong(ID));

                                                testContext.completeNow();
                                            }
                                            catch (Exception exception)
                                            {
                                                LOGGER.error(exception);

                                                testContext.failNow(exception);
                                            }
                                        }
                                        else
                                        {
                                            LOGGER.warn("failed to execute patch");

                                            testContext.failNow(response.cause());
                                        }
                                    });
                                }
                                else
                                {
                                    testContext.failNow(voidResult.cause());
                                }
                            });
                        }
                        else
                        {
                            testContext.failNow(asyncResult.cause());
                        }
                    });
                }
                else
                {
                    testContext.failNow(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testAddCustomTab(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

        var context = new JsonObject("{\"template.name\": \"custom-tab\", \"template.tabs\": [{\"tab.id\": 10000000000099, \"text\": \"Overview\", \"user.created\": \"no\"}],\"style\": {\"font.size\": \"medium\", \"h.gap\": 8, \"v.gap\": 8, \"row.height\": 85},\"template.widgets\": []}");

        context.put(TEMPLATE_NAME, "Template Test " + System.currentTimeMillis());

        TestAPIUtil.post(VISUALIZATION_TEMPLATE_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            LOGGER.info(testInfo.getTestMethod().get().getName() + ": POST response: " + response.bodyAsJsonObject().encode());

                            templateId = response.bodyAsJsonObject().getLong(GlobalConstants.ID);

                            Assertions.assertFalse(TemplateConfigStore.getStore().getItem(templateId).isEmpty());

                            var item = TemplateConfigStore.getStore().getItem(10000000000099L);

                            Assertions.assertNotNull(item);

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testExecute8012CustomTabPatch(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            Patch8012.class.getConstructor().newInstance().doPatch().onComplete(response ->
            {

                var item = TemplateConfigStore.getStore().getItem(10000000000099L);

                LOGGER.info(testInfo.getTestMethod().get().getName() + ": item : " + item.encode());

                Assertions.assertNotNull(item);

                Assertions.assertTrue(item.containsKey("custom.tabs"));

                Assertions.assertFalse(item.getJsonArray("custom.tabs").isEmpty());

                for (var i = 0; i < item.getJsonArray("custom.tabs").size(); i++)
                {
                    var customTab = item.getJsonArray("custom.tabs").getJsonObject(i);

                    if (customTab.getLong("tab.id") == templateId)
                    {
                        testContext.completeNow();

                        break;
                    }
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testCreateBackupProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

            var profile = BackupProfileConfigStore.getStore().getItemByValue(BackupProfile.BACKUP_PROFILE_TYPE, BackupProfile.BackupProfileType.REPORT_DB.getName());

            Bootstrap.configDBService().delete(TBL_BACKUP_PROFILE,
                    new JsonObject().put(FIELD_NAME, ID).put(VALUE, profile.getLong(ID)),
                    DEFAULT_USER,
                    SYSTEM_REMOTE_ADDRESS, asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            var item = new JsonObject("{ \"backup.profile.name\": \"Report DB Backup Profile\", \"backup.profile.type\": \"Report DB\", \"backup.storage.profile\": 10000000000002, \"id\": 10000000000002 }");

                            Bootstrap.configDBService().save(TBL_BACKUP_PROFILE,
                                    item,
                                    DEFAULT_USER,
                                    SYSTEM_REMOTE_ADDRESS,
                                    result ->
                                    {
                                        if (result.succeeded())
                                        {
                                            testContext.completeNow();
                                        }
                                        else
                                        {
                                            testContext.failNow(result.cause());

                                            LOGGER.error(result.cause());
                                        }
                                    });
                        }
                        else
                        {
                            LOGGER.warn("failed to delete existing backup profile");

                            LOGGER.error(asyncResult.cause());
                        }
                    });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getCause());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testUpdateScheduler(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

            var profile = BackupProfileConfigStore.getStore().getItemByValue(BackupProfile.BACKUP_PROFILE_TYPE, BackupProfile.BackupProfileType.REPORT_DB.getName());

            var schedulers = SchedulerConfigStore.getStore().getItemsByValue(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.DATABASE_BACKUP.getName());

            for (var index = 0; index < schedulers.size(); index++)
            {
                if (schedulers.getJsonObject(index).getJsonObject(Scheduler.SCHEDULER_CONTEXT).getJsonArray(NMSConstants.OBJECTS).getLong(0).equals(profile.getLong(ID)))
                {
                    CONTEXT.put("id", schedulers.getJsonObject(index).getLong(ID));

                    break;
                }
            }

            LOGGER.info("scheduler : " + SchedulerConfigStore.getStore().getItem(CONTEXT.getLong(ID)).encode());

            Bootstrap.configDBService().update(DBConstants.TBL_SCHEDULER,
                    new JsonObject().put(FIELD_NAME, ID).put(VALUE, CONTEXT.getLong(ID)),
                    new JsonObject("{\"scheduler.timeline\": \"Weekly\"}"),
                    DEFAULT_USER,
                    SYSTEM_REMOTE_ADDRESS, result ->
                    {
                        if (result.succeeded())
                        {
                            testContext.completeNow();
                        }
                        else
                        {
                            LOGGER.warn("failed to update scheduler");

                            LOGGER.error(result.cause());
                        }
                    });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getCause());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testExecute8012BackupProfilePatch(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

            var clazz = Patch8012.class.getConstructor().newInstance();

            clazz.doPatch().onComplete(response ->
            {
                if (response.succeeded())
                {
                    var count = new AtomicInteger(0);

                    TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(3), timer ->
                    {
                        var item = BackupProfileConfigStore.getStore().getItemByValue(BackupProfile.BACKUP_PROFILE_TYPE, BackupProfile.BackupProfileType.REPORT_DB.getName());

                        LOGGER.info("profile : " + item.encode());

                        if (count.incrementAndGet() < 5)
                        {
                            if (item.containsKey(BackupProfile.BACKUP_PROFILE_CONTEXT) && item.getJsonObject(BackupProfile.BACKUP_PROFILE_CONTEXT).containsKey(DatastoreConstants.DATASTORE_TYPES))
                            {
                                Assertions.assertEquals(6, item.getJsonObject(BackupProfile.BACKUP_PROFILE_CONTEXT).getJsonArray(DatastoreConstants.DATASTORE_TYPES).size());

                                Assertions.assertTrue(SchedulerConfigStore.getStore().getItem(CONTEXT.getLong(ID)).getString(Scheduler.SCHEDULER_TIMELINE).equalsIgnoreCase("daily"));

                                TestUtil.vertx().cancelTimer(timer);

                                testContext.completeNow();
                            }
                        }
                        else
                        {
                            TestUtil.vertx().cancelTimer(timer);

                            testContext.failNow("failed to execute patch!");
                        }
                    });
                }
                else
                {
                    LOGGER.warn("failed to execute patch");

                    testContext.failNow(response.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getCause());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testExecute8013TagPatch(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

            var items = ObjectConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                if (item.containsKey(AIOpsObject.OBJECT_TAGS))
                {
                    LOGGER.info(item);

                    Assertions.assertNotNull(TagConfigStore.getStore().getItems(item.getJsonArray(AIOpsObject.OBJECT_TAGS)));
                }
            }

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getCause());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testCreatePolicies(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

            var metricPolicyPayload = new JsonObject("{ \"policy.name\" : \"Demo-Patch\", \"policy.type\" : \"Metric Threshold\", \"policy.rolling.window\" : null, \"policy.scheduled\" : \"no\", \"policy.context\" : { \"entity.type\" : \"Monitor\", \"entities\" : [  ], \"metric\" : \"system.cpu.percent\", \"policy.metric.plugins\" : [ 88, 158, 512, 508, 518, 181 ], \"policy.trigger.time\" : 300, \"policy.trigger.occurrences\" : 1, \"policy.auto.clear.timer.seconds\" : 43200, \"policy.severity\" : { \"CRITICAL\" : { \"policy.condition\" : \">=\", \"policy.threshold\" : \"40\" }, \"MAJOR\" : { \"policy.condition\" : \">=\", \"policy.threshold\" : \"30\" }, \"WARNING\" : { \"policy.condition\" : \">=\", \"policy.threshold\" : \"20\" } }, \"filters\" : { \"data.filter\" : { } } }, \"policy.email.notification.subject\" : \"$$$severity$$$ - $$$object.name$$$-1\", \"policy.message\" : \"$$$counter$$$ has entered into $$$severity$$$ state with value $$$value$$$ on $$$object.host$$$($$$object.ip$$$)\", \"policy.renotify\" : \"yes\", \"policy.renotify.acknowledged\" : \"yes\", \"policy.actions\" : { \"CRITICAL\" : [ 10000000000001, 10000000000025 ], \"WARNING\" : [ 10000000000001, 10000000000025 ], \"MAJOR\" : [ 10000000000001, 10000000000025 ], \"CLEAR\" : [ 10000000000001 ] }, \"policy.notification.context\" : [ { \"policy.email.notification.recipients\" : [ \"<EMAIL>\" ], \"policy.user.notification.recipients\" : [ \"admin\" ], \"policy.notification.severity\" : [ \"CRITICAL\", \"MAJOR\", \"WARNING\", \"CLEAR\" ] } ], \"policy.renotification.context\" : [ { \"policy.email.notification.recipients\" : [ \"<EMAIL>\" ], \"policy.user.notification.recipients\" : [ \"admin\" ], \"policy.renotification.timer.seconds\" : 1800, \"policy.notification.severity\" : \"CRITICAL\" }, { \"policy.email.notification.recipients\" : [ \"<EMAIL>\" ], \"policy.user.notification.recipients\" : [ \"admin\" ], \"policy.renotification.timer.seconds\" : 3600, \"policy.notification.severity\" : \"WARNING\" }, { \"policy.email.notification.recipients\" : [ \"<EMAIL>\" ], \"policy.user.notification.recipients\" : [ \"admin\" ], \"policy.renotification.timer.seconds\" : 43200, \"policy.notification.severity\" : \"MAJOR\" } ], \"policy.sound.notification\" : \"yes\", \"policy.sound.notification.severity\" : [ \"CRITICAL\", \"WARNING\" ], \"policy.archived\" : \"no\", \"policy.creation.time\" : 1733924525, \"policy.state\" : \"yes\"}");

            var eventPolicyPayload = new JsonObject("{ \"policy.name\" : \"ABC-Patch\", \"policy.type\" : \"Log\", \"policy.scheduled\" : \"no\", \"policy.context\" : { \"entities\" : [ ], \"data.point\" : \"message\", \"aggregator\" : \"count\", \"operator\" : \">=\", \"value\" : 100, \"trigger.mode\" : \"combined\", \"evaluation.window\" : 1, \"evaluation.window.unit\" : \"minute\", \"evaluation.frequency\" : 1, \"evaluation.frequency.unit\" : \"minute\", \"policy.severity\" : \"CRITICAL\", \"policy.trigger.occurrences\" : 1, \"policy.auto.clear.timer.seconds\" : 0, \"filters\" : { \"data.filter\" : { } } }, \"policy.email.notification.subject\" : \"$$$severity$$$ - $$$policy.type$$$ : $$$policy.name$$$ has been triggered\", \"policy.message\" : \"$$$policy.type$$$: $$$policy.name$$$ has been triggered with $$$severity$$$ severity\", \"policy.renotify\" : \"no\", \"policy.renotify.acknowledged\" : \"no\", \"policy.actions\" : [ 10000000000001 ], \"policy.suppress.action\" : \"no\", \"policy.clear.state\" : \"no\", \"policy.notification.context\" : [ { \"policy.email.notification.recipients\" : [ \"<EMAIL>\" ], \"policy.user.notification.recipients\" : [ \"admin\" ] } ], \"policy.sound.notification\" : \"no\", \"policy.archived\" : \"no\", \"policy.creation.time\" : 1734335225, \"policy.state\" : \"yes\" }");

            var integrationPayload = new JsonObject("{ \"integration.type\": \"ServiceOps\", \"integration.server.url\": \"http://************\", \"integration.server.url.timeout.sec\": 5, \"username\": \"motadata\", \"password\": \"Mind!@#$%^&*(\", \"client.id\": \"new-client\", \"client.secret\": \"GGzsnKoJyr1dTCP5Rmiu\", \"source\": \"Aiops\", \"integration.failed.notification.email.recipient\": [ \"<EMAIL>\" ], \"integration.auto.close.ticket.status\": \"yes\", \"urgency\": { \"Low\": \"WARNING\", \"Medium\": \"MAJOR\", \"High\": \"CRITICAL\", \"Urgent\": \"DOWN\" }, \"integration.ticket.resolution.state\": \"Closed\", \"access_token\": \"eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FXaRDwwAc9w9bylj6cvllsCIRg7DHxgAuGtqcyhuKUcFePru6PpXmDjbufKF9Hd3u42DTia0oDMNCmwDtTBhNuM-3PKuOPU00YH1e1u8x7We8GjvPO50RXcQOf6qFpzygx0KTnjGr0-cs0A5F0U2wrTqKGj0PRgcvytq2KlxNeMBGrbWFmDJGWPbAluXHQ4hT_3ZMmSSK5fB17RXnUAFmjVjuGPsntdvzwL-RhfGPxZa65yBinX5dM4DCQtCaCtQO4-7JeCpoTjZlTJavHCkEf0m7H1_aUGNFP37OVWOXURb4DO1NU8Ug5vr5RI9wVptgzd9C1bYicyh5YIvUuMCZw\", \"expires_in\": 43199 }");

            var field = IntegrationCacheStore.class.getDeclaredField("items");

            field.setAccessible(true);

            var items = (Map<String, Object>) field.get(IntegrationCacheStore.getStore());

            items.put(IntegrationConstants.IntegrationType.SERVICEOPS.getName() + SEPARATOR + policyKey, "INC000#7658");

            var futures = new ArrayList<Future<Void>>();

            var promise = Promise.<Void>promise();

            futures.add(promise.future());

            Bootstrap.configDBService().save(TBL_METRIC_POLICY, metricPolicyPayload, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
            {
                if (result.succeeded())
                {
                    MetricPolicyConfigStore.getStore().addItem(result.result()).onComplete(asyncResult ->
                    {
                        LOGGER.info("metric policy" + result.result());

                        CONTEXT.put("metric-policy", result.result());

                        promise.complete();
                    });
                }
                else
                {
                    promise.fail(result.cause());

                    LOGGER.error(result.cause());
                }
            });

            var future = Promise.<Void>promise();

            futures.add(future.future());

            Bootstrap.configDBService().save(TBL_EVENT_POLICY, eventPolicyPayload, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
            {
                if (result.succeeded())
                {
                    EventPolicyConfigStore.getStore().addItem(result.result()).onComplete(asyncResult ->
                    {
                        LOGGER.info("event policy : " + result.result());

                        CONTEXT.put("event-policy", result.result());

                        future.complete();
                    });
                }
                else
                {
                    future.fail(result.cause());

                    LOGGER.error(result.cause());
                }
            });

            var asyncPromise = Promise.<Void>promise();

            futures.add(asyncPromise.future());

            var item = IntegrationConfigStore.getStore().getItemByValue(Integration.INTEGRATION_TYPE, IntegrationConstants.IntegrationType.SERVICEOPS.getName());

            Bootstrap.configDBService().update(TBL_INTEGRATION,
                    new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                    integrationPayload, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                    {
                        if (result.succeeded())
                        {
                            IntegrationConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                            {
                                LOGGER.info("integration : " + result.result());

                                asyncPromise.complete();
                            });
                        }
                        else
                        {
                            asyncPromise.fail(result.cause());

                            LOGGER.error(result.cause());
                        }
                    });

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    testContext.completeNow();
                }
                else
                {
                    LOGGER.error(result.cause());

                    testContext.failNow(result.cause());
                }
            });

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getCause());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testDeleteCustomTemplateTab(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

        TestAPIUtil.delete(VISUALIZATION_TEMPLATE_API_ENDPOINT + "/" + templateId,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertDeleteEntityTestResult(TemplateConfigStore.getStore(), response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_DELETED, "Template"));

                            testContext.completeNow();
                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testExecutePatch8014(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

            var clazz = Patch8014.class.getConstructor().newInstance();

            clazz.doPatch().onComplete(result ->
            {
                try
                {
                    if (result.succeeded())
                    {
                        var retries = new AtomicInteger(0);

                        TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(3), timer ->
                        {
                            try
                            {
                                var item = MetricPolicyConfigStore.getStore().getItem(CONTEXT.getLong("metric-policy"));

                                LOGGER.trace("metric policy : " + item.encode());

                                if (retries.incrementAndGet() < 5)
                                {
                                    if (item.containsKey(POLICY_ACTIONS) && !item.getJsonObject(POLICY_ACTIONS).isEmpty()
                                            && item.containsKey(POLICY_TITLE)
                                            && item.getJsonObject(POLICY_ACTIONS).containsKey(PolicyTriggerActionType.RUNBOOK.getName())
                                            && item.getJsonObject(POLICY_ACTIONS).containsKey(PolicyTriggerActionType.NOTIFICATION.getName())
                                            && item.getJsonObject(POLICY_ACTIONS).containsKey(PolicyTriggerActionType.RENOTIFICATION.getName())
                                            && item.getJsonObject(POLICY_ACTIONS).containsKey(PolicyTriggerActionType.INTEGRATION.getName()))
                                    {
                                        Assertions.assertNotNull(item.getJsonObject(POLICY_ACTIONS));

                                        Assertions.assertTrue(CommonUtil.isNotNullOrEmpty(item.getString(POLICY_TITLE)));

                                        Assertions.assertNotNull(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()));

                                        Assertions.assertFalse(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).isEmpty());

                                        Assertions.assertNotNull(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.NOTIFICATION.getName()));

                                        Assertions.assertFalse(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.NOTIFICATION.getName()).isEmpty());

                                        Assertions.assertNotNull(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.NOTIFICATION.getName()).getJsonObject(Notification.NotificationType.EMAIL.getName()));

                                        Assertions.assertFalse(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.NOTIFICATION.getName()).getJsonObject(Notification.NotificationType.EMAIL.getName()).isEmpty());

                                        Assertions.assertNotNull(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.NOTIFICATION.getName()).getJsonObject(Notification.NotificationType.SOUND.getName()));

                                        Assertions.assertFalse(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.NOTIFICATION.getName()).getJsonObject(Notification.NotificationType.SOUND.getName()).isEmpty());

                                        Assertions.assertNotNull(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.RENOTIFICATION.getName()));

                                        Assertions.assertFalse(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.RENOTIFICATION.getName()).isEmpty());

                                        Assertions.assertNotNull(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.INTEGRATION.getName()));

                                        Assertions.assertFalse(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.INTEGRATION.getName()).isEmpty());

                                        Assertions.assertNotNull(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.INTEGRATION.getName()).getJsonArray(Severity.CRITICAL.name()));

                                        Assertions.assertFalse(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.INTEGRATION.getName()).getJsonArray(Severity.CRITICAL.name()).isEmpty());

                                        Assertions.assertNotNull(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.INTEGRATION.getName()).getJsonArray(Severity.WARNING.name()));

                                        Assertions.assertFalse(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.INTEGRATION.getName()).getJsonArray(Severity.WARNING.name()).isEmpty());

                                        Assertions.assertNotNull(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.INTEGRATION.getName()).getJsonArray(Severity.MAJOR.name()));

                                        Assertions.assertFalse(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.INTEGRATION.getName()).getJsonArray(Severity.MAJOR.name()).isEmpty());

                                        Assertions.assertNotNull(IntegrationProfileConfigStore.getStore().getItems());

                                        Assertions.assertTrue(IntegrationProfileConfigStore.getStore().getItems().size() >= 5);

                                        Assertions.assertNotNull(IntegrationConfigStore.getStore().getItemByValue(Integration.INTEGRATION_TYPE, IntegrationConstants.IntegrationType.SERVICEOPS.getName()));

                                        var integration = IntegrationConfigStore.getStore().getItemByValue(Integration.INTEGRATION_TYPE, IntegrationConstants.IntegrationType.SERVICEOPS.getName());

                                        Assertions.assertNotNull(integration.getJsonObject(Integration.INTEGRATION_CONTEXT));

                                        Assertions.assertFalse(integration.getJsonObject(Integration.INTEGRATION_CONTEXT).isEmpty());

                                        Assertions.assertTrue(integration.containsKey(Integration.INTEGRATION_ACCESS_TOKEN));

                                        Assertions.assertTrue(integration.getJsonObject(Integration.INTEGRATION_CONTEXT).containsKey(Notification.EMAIL_NOTIFICATION_RECIPIENTS));

                                        Assertions.assertTrue(integration.getJsonObject(Integration.INTEGRATION_CONTEXT).containsKey(GlobalConstants.TARGET));

                                        Assertions.assertTrue(integration.getJsonObject(Integration.INTEGRATION_CONTEXT).containsKey(Integration.SYNC_INTERVAL));

                                        Assertions.assertTrue(integration.getJsonObject(Integration.INTEGRATION_CONTEXT).containsKey(Integration.AUTO_SYNC));

                                        Assertions.assertTrue(integration.getJsonObject(Integration.INTEGRATION_CONTEXT).containsKey(APIConstants.CLIENT_ID));

                                        Assertions.assertTrue(integration.getJsonObject(Integration.INTEGRATION_CONTEXT).containsKey("client.secret"));

                                        Assertions.assertTrue(integration.getJsonObject(Integration.INTEGRATION_CONTEXT).containsKey("source"));

                                        Assertions.assertTrue(integration.getJsonObject(Integration.INTEGRATION_CONTEXT).containsKey(Integration.ALERT_REOCCURRENCE_ACTION));

                                        Assertions.assertTrue(integration.getJsonObject(Integration.INTEGRATION_CONTEXT).containsKey(USERNAME));

                                        Assertions.assertTrue(integration.getJsonObject(Integration.INTEGRATION_CONTEXT).containsKey(PASSWORD));

                                        var field = IntegrationCacheStore.class.getDeclaredField("profiles");

                                        field.setAccessible(true);

                                        var profiles = (Map<String, Long>) field.get(IntegrationCacheStore.getStore());

                                        Assertions.assertNotNull(profiles);

                                        Assertions.assertTrue(profiles.containsKey(policyKey));

                                        Assertions.assertNotNull(profiles.get(policyKey));

                                        item = EventPolicyConfigStore.getStore().getItem(CONTEXT.getLong("event-policy"));

                                        LOGGER.trace("event policy : " + item.encode());

                                        if (item.containsKey(POLICY_ACTIONS) && !item.getJsonObject(POLICY_ACTIONS).isEmpty()
                                                && item.containsKey(POLICY_TITLE)
                                                && item.getJsonObject(POLICY_ACTIONS).containsKey(PolicyTriggerActionType.RUNBOOK.getName())
                                                && item.getJsonObject(POLICY_ACTIONS).containsKey(PolicyTriggerActionType.NOTIFICATION.getName()))
                                        {
                                            Assertions.assertNotNull(item.getJsonObject(POLICY_ACTIONS));

                                            Assertions.assertTrue(CommonUtil.isNotNullOrEmpty(item.getString(POLICY_TITLE)));

                                            Assertions.assertNotNull(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()));

                                            Assertions.assertFalse(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).isEmpty());

                                            Assertions.assertNotNull(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.NOTIFICATION.getName()));

                                            Assertions.assertFalse(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.NOTIFICATION.getName()).isEmpty());

                                            Assertions.assertNotNull(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.NOTIFICATION.getName()).getJsonObject(Notification.NotificationType.EMAIL.getName()));

                                            Assertions.assertFalse(item.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.NOTIFICATION.getName()).getJsonObject(Notification.NotificationType.EMAIL.getName()).isEmpty());

                                            TestUtil.vertx().cancelTimer(timer);

                                            testContext.completeNow();
                                        }
                                    }
                                }
                                else
                                {
                                    TestUtil.vertx().cancelTimer(timer);

                                    testContext.failNow("failed to execute patch!");
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);

                                testContext.failNow(exception.getCause());
                            }
                        });
                    }
                    else
                    {
                        LOGGER.warn("failed to execute patch");

                        testContext.failNow(result.cause());
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    testContext.failNow(exception.getCause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getCause());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testGetCustomTemplate(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "/" + 10000000000099L,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            var result = response.bodyAsJsonObject().getJsonObject(RESULT);

                            LOGGER.info("Template get response : " + result.encode());

                            assertNotNull(result);

                            assertFalse(result.isEmpty());

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testExecute8014MailServerConfigurationPatch(VertxTestContext testContext)
    {
        try
        {
            Patch8014.class.getConstructor().newInstance().doPatch().onComplete(response ->
            {
                var item = MailServerConfigStore.getStore().getItem();

                Assertions.assertNotNull(item);

                Assertions.assertEquals(DEFAULT_EMAIL_CREDENTIAL_PROFILE, item.getLong(MAIL_SERVER_CREDENTIAL_PROFILE));

                testContext.completeNow();
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000)
    @Order(21)
    void testValidatePatch8015(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

            var dateTime = DateTimeUtil.getScheduledTimestamp(System.currentTimeMillis() + (2000 * 1000L));

            var event = new JsonObject().put(Scheduler.SCHEDULER_TIMELINE, CRON_ONCE)
                    .put(Scheduler.SCHEDULER_TIMES, new JsonArray().add(dateTime.split(" ")[1]))
                    .put(Scheduler.SCHEDULER_START_DATE, dateTime.split(" ")[0])
                    .put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.TOPOLOGY.getName())
                    .put(SCHEDULER_STATE, YES)
                    .put(SCHEDULER_CONTEXT, new JsonObject().put("topology.plugin.exclude.target.type", "ip.address.range").put("topology.plugin.exclude.targets", new JsonArray().add("***********-255")).put(TopologyPlugin.TOPOLOGY_PLUGIN_ENTRY_POINTS, new JsonArray().add(218956790L))
                            .put(NMSConstants.TOPOLOGY_LINK_LAYER, new JsonArray().add("L2").add("L3")));

            Bootstrap.configDBService().save(DBConstants.TBL_SCHEDULER, event, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
            {
                if (result.succeeded())
                {
                    SchedulerConfigStore.getStore().addItem(result.result()).onComplete(asyncResult -> JobScheduler.scheduleCustomJob(SchedulerConfigStore.getStore().getItem(result.result())));

                    TestUtil.vertx().setTimer(3000, timer ->
                    {

                        try
                        {
                            var clazz = Patch8015.class.getConstructor().newInstance();

                            clazz.doPatch().onComplete(asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    LOGGER.info("Patch executed successfully ...");

                                    // topology include exclude criteria patch patch

                                    var ids = SchedulerConfigStore.getStore().flatItems(Scheduler.SCHEDULER_JOB_TYPE, "Topology", ID);

                                    assertNotNull(ids);

                                    assertFalse(ids.isEmpty());

                                    for (var index = 0; index < ids.size(); index++)
                                    {
                                        var item = SchedulerConfigStore.getStore().getItem(ids.getLong(index));

                                        LOGGER.info(String.format("item : %s", item.encode()));

                                        var context = item.getJsonObject(SCHEDULER_CONTEXT);

                                        if (context.containsKey("topology.plugin.exclude.targets") && !context.getJsonArray("topology.plugin.exclude.targets").isEmpty())
                                        {
                                            Assertions.assertNotNull(context.getJsonArray("topology.plugin.filter.targets"));

                                            Assertions.assertNotNull(context.getString("topology.plugin.filter.target.type"));

                                            Assertions.assertFalse(context.getJsonArray("topology.plugin.filter.targets").isEmpty());

                                            Assertions.assertTrue(context.getString("topology.plugin.filter.target.type").equalsIgnoreCase("exclude-" + context.getString("topology.plugin.exclude.target.type")));

                                            var items = ObjectConfigStore.getStore().getItems();

                                            var objectNames = new HashSet<>();

                                            for (var i = 0; i < items.size(); i++)
                                            {
                                                var objectName = items.getJsonObject(i).getString(AIOpsObject.OBJECT_NAME);

                                                Assertions.assertNotNull(objectName);

                                                Assertions.assertFalse(objectNames.contains(objectName));

                                                objectNames.add(objectName);
                                            }

                                            testContext.completeNow();
                                        }
                                    }
                                }
                                else
                                {
                                    LOGGER.error(asyncResult.cause());

                                    testContext.failNow(asyncResult.cause());
                                }
                            });
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            testContext.failNow(exception);
                        }
                    });
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000)
    @Order(22)
    void testValidatePatch8015ServiceNowPatch(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.put(INTEGRATION_API_ENDPOINT + "/" + 10000000000002L,
                new JsonObject("{\"integration.context\": { \"target\": \"http://localhost:6334/integration-test/now/200/\", \"timeout\": 60, \"username\": \"admin\", \"password\": \"admin\", \"email.notification.recipients\": [ \"<EMAIL>\" ], \"request.type\": \"incident\", \"auto.sync\": \"no\", \"sync.interval\": 2, \"alert.reoccurrence.action\": \"reopen\" }, \"integration.type\": \"ServiceNow\"}"),
                response ->
                {
                    if (response.succeeded())
                    {
                        try
                        {
                            var clazz = Patch8015.class.getConstructor().newInstance();

                            clazz.doPatch().onComplete(asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    LOGGER.info("ServiceNow Patch executed successfully ...");

                                    var item = IntegrationConfigStore.getStore().getItem(IntegrationConstants.IntegrationId.SERVICENOW.getName());

                                    assertNotNull(item);

                                    if (item.containsKey(Integration.INTEGRATION_CONTEXT)
                                            && !item.getJsonObject(Integration.INTEGRATION_CONTEXT).isEmpty()
                                            && item.getJsonObject(Integration.INTEGRATION_CONTEXT).containsKey(GlobalConstants.TARGET)
                                            && CommonUtil.isNotNullOrEmpty(item.getJsonObject(Integration.INTEGRATION_CONTEXT).getString(GlobalConstants.TARGET)))
                                    {
                                        var id = item.getJsonObject(Integration.INTEGRATION_CONTEXT).getLong(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE);

                                        assertNotNull(id);

                                        assertTrue(CredentialProfileConfigStore.getStore().existItem(id));

                                    }

                                    testContext.completeNow();
                                }
                                else
                                {
                                    LOGGER.error(asyncResult.cause());

                                    testContext.failNow(asyncResult.cause());
                                }
                            });
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            testContext.failNow(exception);
                        }
                    }
                    else
                    {
                        LOGGER.error(response.cause());

                        testContext.failNow(response.cause());
                    }
                });
    }

    @RepeatedIfExceptionsTest(suspend = 3000)
    @Order(22)
    void testValidatePatch8015IntegrationProfilePatch(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var clazz = Patch8015.class.getConstructor().newInstance();

            clazz.doPatch().onComplete(result ->
            {
                if (result.succeeded())
                {
                    LOGGER.info("Integration Profile executed successfully ...");

                    var items = IntegrationProfileConfigStore.getStore().getItems();

                    for (var index = 0; index < items.size(); index++)
                    {
                        assertTrue(items.getJsonObject(index).containsKey(IntegrationProfile.INTEGRATION_CATEGORY));
                    }

                    testContext.completeNow();
                }
                else
                {
                    LOGGER.error(result.cause());

                    testContext.failNow(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }

    }

    @RepeatedIfExceptionsTest(suspend = 3000)
    @Order(23)
    void testValidatePatch8016(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var clazz = Patch8016.class.getConstructor().newInstance();

            var payload = TestConstants.prepareParams("testMicrosoftTeamCredentialProfile");

            Assertions.assertNotNull(payload);

            payload.put(CredentialProfile.CREDENTIAL_PROFILE_NAME, payload.getString(CredentialProfile.CREDENTIAL_PROFILE_NAME) + System.currentTimeMillis());

            TestAPIUtil.createCredentialProfile(payload, testContext).onComplete(response ->
            {
                if (response.succeeded())
                {
                    var credentialId = response.result();

                    clazz.doPatch().onComplete(result ->
                    {
                        if (result.succeeded())
                        {
                            LOGGER.info("Credential Profile executed successfully ...");

                            var item = CredentialProfileConfigStore.getStore().getItem(credentialId);

                            assertTrue(item.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT).containsKey(OAuthUtil.GRANT_TYPE));

                            assertEquals(NMSConstants.OAuthGrantType.AUTHORIZATION_CODE.getName(), item.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT).getString(OAuthUtil.GRANT_TYPE));

                            testContext.completeNow();
                        }
                        else
                        {
                            LOGGER.error(result.cause());

                            testContext.failNow(result.cause());
                        }
                    });

                }
                else
                {
                    LOGGER.error(response.cause());

                    testContext.failNow(response.cause());
                }
            });

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000)
    @Order(23)
    void testValidatePatch8016ServiceOpsCredentialProfileMigration(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.put(INTEGRATION_API_ENDPOINT + "/" + IntegrationConstants.IntegrationId.SERVICEOPS.getName(),
                new JsonObject("{ \"integration.type\": \"ServiceOps\", \"integration.server.url\": \"http://************\", \"integration.server.url.timeout.sec\": 5, \"username\": \"motadata\", \"password\": \"Mind!@#$%^&*(\", \"client.id\": \"new-client\", \"client.secret\": \"GGzsnKoJyr1dTCP5Rmiu\", \"source\": \"Aiops\", \"integration.failed.notification.email.recipient\": [ \"<EMAIL>\" ], \"integration.auto.close.ticket.status\": \"yes\", \"urgency\": { \"Low\": \"WARNING\", \"Medium\": \"MAJOR\", \"High\": \"CRITICAL\", \"Urgent\": \"DOWN\" }, \"integration.ticket.resolution.state\": \"Closed\", \"access_token\": \"eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FXaRDwwAc9w9bylj6cvllsCIRg7DHxgAuGtqcyhuKUcFePru6PpXmDjbufKF9Hd3u42DTia0oDMNCmwDtTBhNuM-3PKuOPU00YH1e1u8x7We8GjvPO50RXcQOf6qFpzygx0KTnjGr0-cs0A5F0U2wrTqKGj0PRgcvytq2KlxNeMBGrbWFmDJGWPbAluXHQ4hT_3ZMmSSK5fB17RXnUAFmjVjuGPsntdvzwL-RhfGPxZa65yBinX5dM4DCQtCaCtQO4-7JeCpoTjZlTJavHCkEf0m7H1_aUGNFP37OVWOXURb4DO1NU8Ug5vr5RI9wVptgzd9C1bYicyh5YIvUuMCZw\", \"expires_in\": 43199 }"),
                response ->
                {
                    if (response.succeeded())
                    {
                        try
                        {
                            var clazz = Patch8016.class.getConstructor().newInstance();

                            clazz.doPatch().onComplete(asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    LOGGER.info("ServiceNow Patch executed successfully ...");

                                    var item = IntegrationConfigStore.getStore().getItem(IntegrationConstants.IntegrationId.SERVICENOW.getName());

                                    assertNotNull(item);

                                    if (item.containsKey(Integration.INTEGRATION_CONTEXT)
                                            && !item.getJsonObject(Integration.INTEGRATION_CONTEXT).isEmpty()
                                            && item.getJsonObject(Integration.INTEGRATION_CONTEXT).containsKey(GlobalConstants.TARGET)
                                            && CommonUtil.isNotNullOrEmpty(item.getJsonObject(Integration.INTEGRATION_CONTEXT).getString(GlobalConstants.TARGET)))
                                    {
                                        var id = item.getJsonObject(Integration.INTEGRATION_CONTEXT).getLong(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE);

                                        assertNotNull(id);

                                        assertTrue(CredentialProfileConfigStore.getStore().existItem(id));

                                    }

                                    testContext.completeNow();
                                }
                                else
                                {
                                    LOGGER.error(asyncResult.cause());

                                    testContext.failNow(asyncResult.cause());
                                }
                            });
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            testContext.failNow(exception);
                        }
                    }
                    else
                    {
                        LOGGER.error(response.cause());

                        testContext.failNow(response.cause());
                    }
                });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testValidatePatch8017LDAPServerPatch(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

            var context = new JsonObject()
                    .put("ldap.server.host", "************")
                    .put(LDAP_SERVER_FQDN, "motadata.in")
                    .put(LDAP_SERVER_PORT, 389)
                    .put(LDAP_SERVER_USERNAME, "dhvani")
                    .put(LDAP_SERVER_PASSWORD, "Mind@123")
                    .put(LDAP_AUTHENTICATION, NO)
                    .put(LDAP_AUTO_SYNC, NO)
                    .put(LDAP_SERVER_PROTOCOL, LDAP_SERVER_PROTOCOL_PLAIN)
                    .put(LDAP_SERVER_USER_GROUPS, new JsonArray().add("motadata users"));

            Bootstrap.configDBService().save(TBL_LDAP_SERVER, context, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
            {
                if (result.succeeded())
                {
                    LDAPServerConfigStore.getStore().addItem(result.result()).onComplete(asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            try
                            {
                                var clazz = Patch8017.class.getConstructor().newInstance();

                                clazz.doPatch().onComplete(future ->
                                {
                                    if (future.succeeded())
                                    {
                                        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), timer ->
                                        {
                                            var item = LDAPServerConfigStore.getStore().getItem(result.result());

                                            LOGGER.info("item : " + item.encode());

                                            Assertions.assertTrue(item.containsKey(LDAP_SERVER_PRIMARY_HOST));

                                            Assertions.assertTrue(CommonUtil.isNotNullOrEmpty(item.getString(LDAP_SERVER_PRIMARY_HOST)));

                                            testContext.completeNow();
                                        });
                                    }
                                    else
                                    {
                                        LOGGER.error(future.cause());

                                        testContext.failNow(future.cause());
                                    }
                                });
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);

                                testContext.failNow(exception);
                            }
                        }
                        else
                        {
                            LOGGER.error(asyncResult.cause());

                            testContext.failNow(asyncResult.cause());
                        }
                    });
                }
                else
                {
                    LOGGER.error(result.cause());

                    testContext.failNow(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testValidatePatch8018(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

            var item = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemByIP("fd00:1:1:1::47"));

            item.remove(ID);

            Bootstrap.configDBService().save(TBL_OBJECT, item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
            {
                if (result.succeeded())
                {
                    ObjectConfigStore.getStore().addItem(result.result()).onComplete(asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            try
                            {
                                var clazz = Patch8018.class.getConstructor().newInstance();

                                clazz.doPatch().onComplete(future ->
                                {
                                    if (future.succeeded())
                                    {
                                        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), timer ->
                                        {
                                            var object = ObjectConfigStore.getStore().getItem(result.result());

                                            LOGGER.info("item : " + object.encode());

                                            Assertions.assertTrue(RunbookPluginConfigStore.getStore().getItem(Runbook.RunbookPluginId.PING.getName()).getJsonArray(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES).contains(object.getLong(ID)));

                                            Assertions.assertTrue(RunbookPluginConfigStore.getStore().getItem(Runbook.RunbookPluginId.TRACE_ROUTE.getName()).getJsonArray(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES).contains(object.getLong(ID)));

                                            testContext.completeNow();
                                        });
                                    }
                                    else
                                    {
                                        LOGGER.error(future.cause());

                                        testContext.failNow(future.cause());
                                    }
                                });
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);

                                testContext.failNow(exception);
                            }
                        }
                        else
                        {
                            LOGGER.error(asyncResult.cause());

                            testContext.failNow(asyncResult.cause());
                        }
                    });
                }
                else
                {
                    LOGGER.error(result.cause());

                    testContext.failNow(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testCreateForecastPolicy(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{ \"policy.name\": \"Old Forecast Policy\", \"policy.type\": \"Forecast\", \"policy.context\": { \"entities\": [], \"metric\": \"system.cpu.percent\", \"policy.metric.plugins\": [ 181, 505 ], \"policy.trigger.time\": 300, \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 10, \"policy.severity\": { \"CRITICAL\": { \"policy.condition\": \">=\", \"policy.threshold\": \"95\" }, \"MAJOR\": { \"policy.condition\": \">=\", \"policy.threshold\": \"85\" }, \"WARNING\": { \"policy.condition\": \">=\", \"policy.threshold\": \"75\" } }, \"filters\": { \"data.filter\": {} } }, \"policy.email.notification.recipients\": [], \"policy.monitor.polling.failed.notification.status\": \"no\", \"policy.actions\": { \"Runbook\": { \"CRITICAL\": [ { \"id\": 10000000000002 } ], \"WARNING\": [ { \"id\": 10000000000002 } ] } } }");

        context.put(POLICY_NAME, "cpu forecast" + System.currentTimeMillis());

        TestAPIUtil.post(METRIC_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encodePrettily());

                    TestAPIUtil.assertCreateEntityTestResult(MetricPolicyConfigStore.getStore(), response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.METRIC_POLICY.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                    policyId = response.bodyAsJsonObject().getLong(GlobalConstants.ID);

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000)
    @Order(27)
    void testValidatePatch8019(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            var clazz = Patch8019.class.getConstructor().newInstance();

            clazz.doPatch().onComplete(result ->
            {
                if (result.succeeded())
                {
                    testContext.completeNow();
                }
                else
                {
                    LOGGER.error(result.cause());

                    testContext.failNow(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    void testGetForecastPolicy(VertxTestContext testContext, TestInfo testInfo)
    {

        LOGGER.info(String.format("running test case : %s with policyID :: %s", testInfo.getTestMethod().get().getName(), CommonUtil.getString(policyId)));

        TestAPIUtil.delete(METRIC_POLICY_API_ENDPOINT + "/" + policyId, testContext.succeeding(response ->

                testContext.verify(() ->
                {
                    LOGGER.info(String.format("API response for : %s -> %s", testInfo.getTestMethod().get().getName(), response.bodyAsJsonObject().encode()));

                    TestAPIUtil.assertNotExistEntityDeleteTestResult(response, APIConstants.Entity.METRIC_POLICY.getName(), String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, APIConstants.Entity.METRIC_POLICY.getName()));

                    testContext.completeNow();
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testValidatePatch8020(VertxTestContext testContext, TestInfo testInfo)
    {

        LOGGER.info(String.format("running test case : %s ", testInfo.getTestMethod().get().getName()));

        TestAPIUtil.post(FLOW_IP_GROUP_API_ENDPOINT, new JsonObject("{\"flow.ip.group\" : [ \"**********-60\" ],\"flow.ip.group.name\" : \"test.domain.com\"}"),
                response ->
                {
                    if (response.succeeded())
                    {
                        try
                        {
                            var clazz = Patch8020.class.getConstructor().newInstance();

                            clazz.doPatch().onComplete(asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    LOGGER.info("8.0.20 Patch executed successfully ...");

                                    var item = FlowDomainMapperConfigStore.getStore().getItemByValue(FlowDomainMapper.FLOW_DOMAIN_MAPPER_NAME, "test.domain.com");

                                    assertNotNull(item);

                                    assertTrue(item.containsKey(FlowDomainMapper.FLOW_DOMAIN_MAPPER_GROUP));

                                    assertFalse(item.getJsonArray(FlowDomainMapper.FLOW_DOMAIN_MAPPER_GROUP).isEmpty());

                                    assertTrue(item.containsKey(FlowDomainMapper.FLOW_DOMAIN_MAPPER_CATEGORY));

                                    assertEquals(61, item.getJsonArray(FlowDomainMapper.FLOW_DOMAIN_MAPPER_GROUP).size());

                                    testContext.completeNow();
                                }
                                else
                                {
                                    LOGGER.error(asyncResult.cause());

                                    testContext.failNow(asyncResult.cause());
                                }
                            });
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            testContext.failNow(exception);
                        }
                    }
                    else
                    {
                        LOGGER.error(response.cause());

                        testContext.failNow(response.cause());
                    }
                });
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testPatchMetricExplorerPermissions(VertxTestContext testContext, TestInfo testInfo)
    {

        LOGGER.info(String.format("running test case : %s ", testInfo.getTestMethod().get().getName()));

        TestAPIUtil.post(USER_ROLE_API_ENDPOINT, new JsonObject("{\"user.role.name\":\"demo role\",\"user.role.context\":[\"my-account-settings:read\",\"my-account-settings:read-write\",\"dashboards:read\",\"widgets:read\",\"monitor-settings:read\",\"group-settings:read\",\"user-settings:read\",\"metric-explorer:read\"]}"),
                response ->
                {
                    if (response.succeeded())
                    {
                        try
                        {
                            var clazz = Patch8020.class.getConstructor().newInstance();

                            clazz.doPatch().onComplete(result ->
                            {
                                if (result.succeeded())
                                {
                                    LOGGER.info("8.0.20 Patch Metric Explorers Permission executed successfully ...");

                                    var item = UserRoleConfigStore.getStore().getItem(response.result().bodyAsJsonObject().getLong(ID));

                                    assertNotNull(item);

                                    assertTrue(item.containsKey(UserRole.USER_ROLE_CONTEXT));

                                    var context = item.getJsonArray(UserRole.USER_ROLE_CONTEXT);

                                    assertNotNull(context);

                                    assertTrue(context.contains("metric-explorers:read"));

                                    assertTrue(context.contains("metric-explorers:read-write"));

                                    assertTrue(context.contains("metric-explorers:delete"));

                                    testContext.completeNow();
                                }
                                else
                                {
                                    LOGGER.error(result.cause());

                                    testContext.failNow(result.cause());
                                }
                            });
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            testContext.failNow(exception);
                        }
                    }
                    else
                    {
                        LOGGER.error(response.cause());

                        testContext.failNow(response.cause());
                    }
                });
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testValidatePatch8021LDAPServerPatch(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

            var context = new JsonObject()
                    .put("ldap.server.host", "************")
                    .put(LDAP_SERVER_FQDN, "motadata.in")
                    .put(LDAP_SERVER_PORT, 389)
                    .put(LDAP_SERVER_USERNAME, "dhvani")
                    .put(LDAP_SERVER_PASSWORD, "Mind@123")
                    .put(LDAP_AUTHENTICATION, NO)
                    .put(LDAP_AUTO_SYNC, NO)
                    .put(LDAP_SERVER_PROTOCOL, LDAP_SERVER_PROTOCOL_PLAIN)
                    .put(LDAP_SERVER_USER_GROUPS, new JsonArray().add("motadata users"));

            Bootstrap.configDBService().save(TBL_LDAP_SERVER, context, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
            {
                if (result.succeeded())
                {
                    LDAPServerConfigStore.getStore().addItem(result.result()).onComplete(asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            try
                            {
                                var clazz = Patch8021.class.getConstructor().newInstance();

                                clazz.doPatch().onComplete(future ->
                                {
                                    if (future.succeeded())
                                    {
                                        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(10), timer ->
                                        {
                                            var item = LDAPServerConfigStore.getStore().getItem(result.result());

                                            LOGGER.info("item : " + item.encode());

                                            Assertions.assertTrue(item.containsKey(LDAP_SERVER_TYPE));

                                            assertEquals(LDAPServer.LDAP_SERVER_TYPE_AD, item.getString(LDAP_SERVER_TYPE));

                                            testContext.completeNow();
                                        });
                                    }
                                    else
                                    {
                                        LOGGER.error(future.cause());

                                        testContext.failNow(future.cause());
                                    }
                                });
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);

                                testContext.failNow(exception);
                            }
                        }
                        else
                        {
                            LOGGER.error(asyncResult.cause());

                            testContext.failNow(asyncResult.cause());
                        }
                    });
                }
                else
                {
                    LOGGER.error(result.cause());

                    testContext.failNow(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    void testValidatePatch8022(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

            var context = new JsonObject().put(Scheduler.SCHEDULER_JOB_TYPE, JobScheduler.JobType.REDISCOVER.getName())
                    .put(Scheduler.SCHEDULER_START_DATE, "23-05-2025")
                    .put(Scheduler.SCHEDULER_TIMES, new JsonArray().add("00:00"))
                    .put(Scheduler.SCHEDULER_TIMELINE, CronExpressionUtil.CRON_ONCE)
                    .put(SCHEDULER_CONTEXT, new JsonObject().put(NMSConstants.REDISCOVER_JOB, NMSConstants.RediscoverJob.PROCESS.getName()).put(NMSConstants.AUTO_PROVISION_STATUS, YES));

            TestAPIUtil.post(SCHEDULER_API_ENDPOINT, context,
                    testContext.succeeding(response ->
                            testContext.verify(() ->
                                    TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(5), id ->
                                    {
                                        try
                                        {
                                            TestAPIUtil.assertMultiCreateEntityTestResult(SchedulerConfigStore.getStore(), response.bodyAsJsonObject(), "Scheduler");

                                            var schedulerId = response.bodyAsJsonObject().getJsonArray(ID).getLong(0);

                                            var clazz = Patch8022.class.getConstructor().newInstance();

                                            clazz.doPatch().onComplete(asyncResult ->
                                            {
                                                try
                                                {
                                                    if (asyncResult.succeeded())
                                                    {
                                                        LOGGER.info("Patch 8.0.22 executed successfully ...");

                                                        var item = SchedulerConfigStore.getStore().getItem(schedulerId);

                                                        Assertions.assertNotNull(item);

                                                        Assertions.assertNotNull(item.getJsonObject(SCHEDULER_CONTEXT).getString(NMSConstants.AUTO_PROVISION_STATUS));

                                                        Assertions.assertEquals(NO, item.getJsonObject(SCHEDULER_CONTEXT).getString(NMSConstants.AUTO_PROVISION_STATUS));

                                                        testContext.completeNow();
                                                    }
                                                    else
                                                    {
                                                        LOGGER.error(asyncResult.cause());

                                                        testContext.failNow(asyncResult.cause());
                                                    }
                                                }
                                                catch (Exception exception)
                                                {
                                                    LOGGER.error(exception);

                                                    testContext.failNow(exception);
                                                }
                                            });
                                        }
                                        catch (Exception exception)
                                        {
                                            LOGGER.error(exception);

                                            testContext.failNow(exception);
                                        }
                                    }))));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(0)
    void testValidatePatch8025(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

            var items = new JsonArray("[{\"metric.explorer.name\":\"ABCD\",\"metric.explorer.access.type\":\"Private\",\"users\":[70012028658,70012028659],\"metric.explorer.global.view.enabled\":\"yes\",\"metric.explorer.object.id\":-1,\"metric.explorer.context\":{\"metric.explorer.charts\":[{\"metric\":\"ping.packet.lost.percent\",\"object.id\":52241766366},{\"metric\":\"ping.latency.ms\",\"object.id\":52241766366}],\"metric.explorer.preference\":{\"visualization.timeline\":{\"relative.timeline\":\"today\"},\"visualization.type\":\"line\",\"visualization.granularity\":{\"value\":5,\"unit\":\"m\"},\"query.type\":\"aggregation\"},\"metric.explorer.merged.counter.map\":{}},\"_type\":\"1\",\"id\":77019864080},{\"metric.explorer.name\":\"Yash\",\"metric.explorer.access.type\":\"Public\",\"metric.explorer.global.view.enabled\":\"yes\",\"metric.explorer.object.id\":-1,\"metric.explorer.context\":{\"metric.explorer.charts\":[{\"metric\":\"ping.min.latency.ms\",\"object.id\":52241766335}],\"metric.explorer.preference\":{\"visualization.timeline\":{\"relative.timeline\":\"today\"},\"visualization.type\":\"line\",\"visualization.granularity\":{\"value\":5,\"unit\":\"m\"},\"query.type\":\"aggregation\"},\"metric.explorer.merged.counter.map\":{\"52241753429-system.blocked.processes\":[]}},\"_type\":\"1\",\"id\":77019864079}]");


            Bootstrap.configDBService().saveAll("tbl_config_metric_explorer", items, SYSTEM_USER, SYSTEM_REMOTE_ADDRESS,result ->
            {
                if (result.succeeded())
                {
                    try
                    {
                        Bootstrap.configDBService().save(TBL_SCHEDULER, new JsonObject(), SYSTEM_USER, MOTADATA_SYSTEM, asyncFuture ->
                        {
                            if (asyncFuture.succeeded())
                            {
                                Assertions.assertNotNull(asyncFuture.result());

                                SchedulerConfigStore.getStore().addItem(asyncFuture.result()).onComplete(response ->
                                {
                                    if (response.succeeded())
                                    {
                                        try
                                        {
                                            Assertions.assertFalse(SchedulerConfigStore.getStore().getItem(asyncFuture.result()).containsKey(Scheduler.SCHEDULER_JOB_TYPE));

                                            var clazz = Patch8025.class.getConstructor().newInstance();

                                            clazz.doPatch().onComplete(asyncResult ->
                                            {
                                                try
                                                {
                                                    if (asyncResult.succeeded())
                                                    {
                                                        LOGGER.info("Patch 8.0.25 executed successfully ...");

                                                        var entries = ExplorerConfigStore.getStore().getItemsByValue(Explorer.EXPLORER_TYPE, APIConstants.ExplorerType.METRIC.getName());

                                                        Assertions.assertNotNull(entries);

                                                        var newMetricSize = MetricConfigStore.getStore().getItemsByValue(METRIC_TYPE,"VMware NSX-T").size();

                                                        var newDiscoverySize = DiscoveryConfigStore.getStore().getItemsByValue(Discovery.DISCOVERY_OBJECT_TYPE,"VMware NSX-T").size();

                                                        var newObjectSize = MetricConfigStore.getStore().getItemsByValue(AIOpsObject.OBJECT_TYPE,"VMware NSX-T").size();

                                                        Assertions.assertTrue(newMetricSize >= 0);

                                                        Assertions.assertTrue(newDiscoverySize >= 0);

                                                        Assertions.assertTrue(newObjectSize >= 0);

                                                        for (var index = 0; index < entries.size() ; index++)
                                                        {
                                                            var entry = entries.getJsonObject(index);

                                                            entry.getMap().keySet().forEach(key ->
                                                            {
                                                                if (key.startsWith("metric.explorer"))
                                                                {
                                                                    testContext.failNow("key starts with metric.explorer : " + entry.encode());
                                                                }
                                                            });
                                                        }

                                                        assertNull(SchedulerConfigStore.getStore().getItem(asyncFuture.result()));

                                                        testContext.completeNow();
                                                    }
                                                    else
                                                    {
                                                        LOGGER.error(asyncResult.cause());

                                                        testContext.failNow(asyncResult.cause());
                                                    }
                                                }
                                                catch (Exception exception)
                                                {
                                                    LOGGER.error(exception);

                                                    testContext.failNow(exception);
                                                }
                                            });
                                        }
                                        catch (Exception exception)
                                        {
                                            LOGGER.error(exception);

                                            testContext.failNow(exception);
                                        }
                                    }
                                    else
                                    {
                                        LOGGER.error(response.cause());

                                        testContext.failNow(response.cause());
                                    }
                                });
                            }
                            else
                            {
                                LOGGER.error(asyncFuture.cause());

                                testContext.failNow(asyncFuture.cause());
                            }
                        });
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        testContext.failNow(exception);
                    }
                }
                else
                {
                    LOGGER.error(result.cause());

                    testContext.failNow(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(33)
    @Timeout(220 * 1000)
    void testValidatePatch8026(VertxTestContext testContext, TestInfo testInfo)
    {

        LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

        var ipObjects = ObjectConfigStore.getStore().getItemsByValue(AIOpsObject.OBJECT_IP, "fd00:1:1:1::47");

        for (int i = 0; i < ipObjects.size(); i++)
        {
            var obj = ipObjects.getJsonObject(i);

            ObjectConfigStore.getStore().deleteItem(obj.getLong(ID));

        }
        try
        {
            var clazz = Patch8026.class.getConstructor().newInstance();

            var item = ObjectConfigStore.getStore().getItem();

            var objectId = item.getLong(ID);

            Bootstrap.configDBService().update(TBL_OBJECT,
                    new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                    item.put(DBConstants.GARBAGE_FIELDS, new JsonArray().add(VisualizationConstants.TEMPLATE_ID)),
                    DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                    result ->
                    {
                        if (result.succeeded())
                        {
                            ObjectConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {

                                    clazz.doPatch().onComplete(patchResult ->
                                    {
                                        try
                                        {
                                            if (patchResult.succeeded())
                                            {
                                                LOGGER.info("Patch 8.0.26 executed successfully ...");

                                                var object = ObjectConfigStore.getStore().getItem(objectId);

                                                assertTrue(object.containsKey(VisualizationConstants.TEMPLATE_ID));

                                                var template = TemplateConfigStore.getStore().getItem();

                                                assertTrue(template.containsKey(TEMPLATE_PARENT_ID));

                                                var id = 10000000000012L;

                                                var category = 10000000000136L;

                                                var policy = MetricPolicyConfigStore.getStore().getItem(id);

                                                assertTrue(policy.getJsonObject(PolicyEngineConstants.POLICY_CONTEXT).getJsonArray(GlobalConstants.ENTITIES).contains(category));

                                                testContext.completeNow();
                                            }
                                            else
                                            {
                                                LOGGER.error(patchResult.cause());

                                                testContext.failNow(patchResult.cause());
                                            }
                                        }
                                        catch (Exception exception)
                                        {
                                            LOGGER.error(exception);

                                            testContext.failNow(exception);
                                        }
                                    });
                                }
                                else
                                {
                                    LOGGER.error(asyncResult.cause());

                                    testContext.failNow(asyncResult.cause());
                                }
                            });
                        }

                        else
                        {
                            LOGGER.error(result.cause());

                            testContext.failNow(result.cause());
                        }
                    });

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }


    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(34)
    void testValidatePasswordPolicyPatch8026(VertxTestContext testContext, TestInfo testInfo)
    {

        LOGGER.info(String.format("running test case : %s", testInfo.getTestMethod().get().getName()));

        try
        {
            var clazz = Patch8026.class.getConstructor().newInstance();

            var item = PasswordPolicyConfigStore.getStore().getItem();

            var objectId = item.getLong(ID);

            Bootstrap.configDBService().update(TBL_PASSWORD_POLICY,
                    new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, objectId),
                    item.put(PASSWORD_POLICY_MAXIMUM_LENGTH, 64),
                    DEFAULT_USER, MOTADATA_SYSTEM,
                    result ->
                    {
                        if (result.succeeded())
                        {
                            PasswordPolicyConfigStore.getStore().updateItem(objectId).onComplete(asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    clazz.doPatch().onComplete(asyncResult1 ->
                                    {
                                        try
                                        {
                                            if (asyncResult1.succeeded())
                                            {
                                                LOGGER.info("Patch 8.0.26 executed successfully ...");

                                                var object = PasswordPolicyConfigStore.getStore().getItem(objectId);

                                                assertTrue(object.containsKey(PASSWORD_POLICY_MAXIMUM_LENGTH));

                                                testContext.completeNow();
                                            }
                                            else
                                            {
                                                LOGGER.error(asyncResult1.cause());

                                                testContext.failNow(asyncResult1.cause());
                                            }
                                        }
                                        catch (Exception exception)
                                        {
                                            LOGGER.error(exception);

                                            testContext.failNow(exception);
                                        }
                                    });
                                }
                                else
                                {
                                    LOGGER.error(asyncResult.cause());

                                    testContext.failNow(asyncResult.cause());
                                }
                            });
                        }

                        else
                        {
                            LOGGER.error(result.cause());

                            testContext.failNow(result.cause());
                        }
                    });

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception);
        }


    }



    void save(JsonObject record, String table, AbstractConfigStore store)
    {
        Bootstrap.configDBService().save(table, record, "admin", "127.0.0.1", result ->
        {
            if (result.succeeded())
            {
                store.addItem(result.result());
            }
        });
    }

    void delete(long id, AbstractConfigStore store)
    {
        LOGGER.info(String.format("deleting item : %s ", id));

        Bootstrap.configDBService().delete(DBConstants.TBL_REMOTE_EVENT_PROCESSOR, new JsonObject().put(FIELD_NAME, ID).put(VALUE, id), "admin", "127.0.0.1", result ->
        {
            if (result.succeeded())
            {
                store.deleteItem(id);
            }
        });
    }

}
