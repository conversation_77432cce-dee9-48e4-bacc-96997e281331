/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 */
package com.mindarray.report;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.TestUtil;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Report;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.RemoteEventProcessorCacheStore;
import com.mindarray.store.RemoteEventProcessorConfigStore;
import com.mindarray.store.ReportConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import com.mindarray.visualization.TestVisualizationManager;
import com.mindarray.visualization.VisualizationConstants;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.zeromq.SocketType;
import org.zeromq.ZMQ;

import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.REPORT_API_ENDPOINT;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.datastore.DatastoreConstants.DataType.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.manager.MotadataAppManager.HEARTBEAT_STATE;
import static com.mindarray.nms.NMSConstants.STATE_RUNNING;
import static com.mindarray.visualization.VisualizationConstants.QUERY_PROGRESS;
import static com.mindarray.visualization.VisualizationConstants.VISUALIZATION_NAME;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestReportManager
{
    private static final Logger LOGGER = new Logger(TestVisualizationManager.class, GlobalConstants.MOTADATA_REPORTING, "Report Manager Test");
    private static final ZMQ.Socket SUBSCRIBER = Bootstrap.zcontext().socket(SocketType.SUB);
    private static final Map<Long, VertxTestContext> QUERY_CONTEXTS = new HashMap<>(); //query ids
    private static final JsonObject RESPONSES = new JsonObject();
    private static final JsonObject BATCH_RESPONSES = new JsonObject();
    private static final AtomicBoolean HAS_MORE_EVENT = new AtomicBoolean(true);
    private static final AtomicInteger COUNTER = new AtomicInteger(0);
    private static final JsonArray QUERY_IDS = new JsonArray();
    private static final JsonObject RESPONSES_BY_TYPE = new JsonObject()
            .put("testCapacityPlanReport1", new JsonObject("{\"result\":[{\"Timestamp\":1722501000000,\"1^system.cpu.percent^avg\":-3.4028234663852886e+38,\"2^system.cpu.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722501300000,\"1^system.cpu.percent^avg\":-3.4028234663852886e+38,\"2^system.cpu.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722501600000,\"1^system.cpu.percent^avg\":-3.4028234663852886e+38,\"2^system.cpu.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722501900000,\"1^system.cpu.percent^avg\":-3.4028234663852886e+38,\"2^system.cpu.percent^avg\":1},{\"Timestamp\":1722502200000,\"1^system.cpu.percent^avg\":65.49,\"2^system.cpu.percent^avg\":0},{\"Timestamp\":1722502500000,\"1^system.cpu.percent^avg\":6.71,\"2^system.cpu.percent^avg\":0},{\"Timestamp\":1722502800000,\"1^system.cpu.percent^avg\":-3.4028234663852886e+38,\"2^system.cpu.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722503100000,\"1^system.cpu.percent^avg\":-3.4028234663852886e+38,\"2^system.cpu.percent^avg\":-3.4028234663852886e+38}]}"))
            .put("testCapacityPlanReport2", new JsonObject("{\"result\":[{\"Timestamp\":1722500700000,\"1^system.disk.used.percent^avg\":-3.4028234663852886e+38,\"2^system.disk.used.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722501000000,\"1^system.disk.used.percent^avg\":-3.4028234663852886e+38,\"2^system.disk.used.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722501300000,\"1^system.disk.used.percent^avg\":-3.4028234663852886e+38,\"2^system.disk.used.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722501600000,\"1^system.disk.used.percent^avg\":-3.4028234663852886e+38,\"2^system.disk.used.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722501900000,\"1^system.disk.used.percent^avg\":-3.4028234663852886e+38,\"2^system.disk.used.percent^avg\":3},{\"Timestamp\":1722502200000,\"1^system.disk.used.percent^avg\":54.95,\"2^system.disk.used.percent^avg\":3},{\"Timestamp\":1722502500000,\"1^system.disk.used.percent^avg\":54.95,\"2^system.disk.used.percent^avg\":3},{\"Timestamp\":1722502800000,\"1^system.disk.used.percent^avg\":-3.4028234663852886e+38,\"2^system.disk.used.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722503100000,\"1^system.disk.used.percent^avg\":-3.4028234663852886e+38,\"2^system.disk.used.percent^avg\":-3.4028234663852886e+38}]}"))
            .put("testCapacityPlanReport3", new JsonObject("{\"result\":[{\"Timestamp\":1722501000000,\"2^system.memory.used.percent^avg\":-3.4028234663852886e+38,\"1^system.memory.used.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722501300000,\"2^system.memory.used.percent^avg\":-3.4028234663852886e+38,\"1^system.memory.used.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722501600000,\"2^system.memory.used.percent^avg\":-3.4028234663852886e+38,\"1^system.memory.used.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722501900000,\"2^system.memory.used.percent^avg\":67.96,\"1^system.memory.used.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722502200000,\"2^system.memory.used.percent^avg\":67.82,\"1^system.memory.used.percent^avg\":64.9},{\"Timestamp\":1722502500000,\"2^system.memory.used.percent^avg\":67.81,\"1^system.memory.used.percent^avg\":62.61},{\"Timestamp\":1722502800000,\"2^system.memory.used.percent^avg\":-3.4028234663852886e+38,\"1^system.memory.used.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722503100000,\"2^system.memory.used.percent^avg\":-3.4028234663852886e+38,\"1^system.memory.used.percent^avg\":-3.4028234663852886e+38}]}"))
            .put("testExecuteCustomScript", new JsonObject("{\"result\":[{\"monitor\":\"1\",\"system.cpu.percent^avg\":10.0}]}"))
            .put("testMultiAvailability", new JsonObject("{\"result\":[{\"entity.id\":54638428208,\"object.type\":\"Citrix Xen\",\"object.vendor\":\"\",\"object.ip\":\"*************\",\"monitor\":\"xen71master\",\"citrix.xen.vm\":\"Ubuntu Trusty Tahr 14.04\",\"citrix.xen.vm.uptime.seconds.sum\":514500,\"citrix.xen.vm.uptime.seconds.sum.formatted\":\"5 d 22 h 55 m\",\"citrix.xen.vm.uptime.percent.avg\":100,\"citrix.xen.vm.uptime.percent.avg.formatted\":\"100%\"}]}"))
            .put("testMultiAvailability1", new JsonObject("{\"result\":[{\"entity.id\":54638428189,\"object.type\":\"Hyper-V\",\"object.vendor\":\"\",\"object.ip\":\"************\",\"monitor\":\"win-node2\",\"hyperv.vm\":\"Linux\",\"hyperv.vm.uptime.seconds.sum\":514500,\"hyperv.vm.uptime.seconds.sum.formatted\":\"5 d 22 h 55 m\",\"hyperv.vm.uptime.percent.avg\":100,\"hyperv.vm.uptime.percent.avg.formatted\":\"100%\"}]}"))
            .put("testMultiAvailability2", new JsonObject("{\"result\":[{\"entity.id\":54638428099,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi18.motadata.local\",\"esxi.vm\":\"RADWIN-License-Utility-Server_11.186\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428098,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi26.motadata.local\",\"esxi.vm\":\"8.0-nilesh database windows 10_10.132\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428101,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi13.motadata.local\",\"esxi.vm\":\"Aiops8.0.4\",\"esxi.vm.uptime.seconds.sum\":514800,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h\",\"esxi.vm.downtime.seconds.sum\":300,\"esxi.vm.downtime.seconds.sum.formatted\":\"5 m\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428097,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi27.motadata.local\",\"esxi.vm\":\"8.0_Xenserver2_8.170-clone\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428100,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi15.motadata.local\",\"esxi.vm\":\"8.0_ranjit_motadata_scalability_9.85\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428098,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi26.motadata.local\",\"esxi.vm\":\"8.0_SQL_failover_domain_9.145\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428101,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi13.motadata.local\",\"esxi.vm\":\"8.0.8_24.04_ISO_Collector\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428097,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi27.motadata.local\",\"esxi.vm\":\"GNS3_VM-2.2.32_10.120\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428100,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi15.motadata.local\",\"esxi.vm\":\"8.0_Dev_Build_Ubuntu-20_8.62\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428100,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi15.motadata.local\",\"esxi.vm\":\"Support_Darshk_IPAM_\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428100,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi15.motadata.local\",\"esxi.vm\":\"8.0_VMware-vCenter-Server-Appliance-5.5.0_10.161\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428098,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi26.motadata.local\",\"esxi.vm\":\"vCLS-b7ec3087-32b5-4dc9-9631-277e9109d924\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428097,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi27.motadata.local\",\"esxi.vm\":\"BIGIP-F5\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428100,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi15.motadata.local\",\"esxi.vm\":\"AIOPS-Ubuntu-24-13.14\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428100,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi15.motadata.local\",\"esxi.vm\":\"AIOPS-Ranjit-HA-4_15.241\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428097,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi27.motadata.local\",\"esxi.vm\":\"Ankit-RHEL-7.1__8.63\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428097,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi27.motadata.local\",\"esxi.vm\":\"GNS3-Linux-Client-10.121\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428101,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi13.motadata.local\",\"esxi.vm\":\"AIOPS_8.0.6_Database\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428101,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi13.motadata.local\",\"esxi.vm\":\"ISo_172.16.15.212_Master\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428101,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi13.motadata.local\",\"esxi.vm\":\"NMS_metric_master_13.115\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428101,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi13.motadata.local\",\"esxi.vm\":\"ISO_8.0.6_15.212_Collector\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428100,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi15.motadata.local\",\"esxi.vm\":\"Ubuntu- GitLab 10.187\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428098,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi26.motadata.local\",\"esxi.vm\":\"NILESH_internal-masterDont-off_8.67\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428101,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi13.motadata.local\",\"esxi.vm\":\"8.0.5_Collector\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428101,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi13.motadata.local\",\"esxi.vm\":\"AIOps-collector-2\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428101,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi13.motadata.local\",\"esxi.vm\":\"8.0.8_24.04_ISO_Master\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428097,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi27.motadata.local\",\"esxi.vm\":\"Ankit-Ubuntu-14-Motadata-upgrade_10.63\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428098,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi26.motadata.local\",\"esxi.vm\":\"8.0_Krunal-agent_U20_8.66-8.61\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428099,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi18.motadata.local\",\"esxi.vm\":\"ServiceOps_PCMC_Yash\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428101,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi13.motadata.local\",\"esxi.vm\":\"Aiops_Database\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428100,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi15.motadata.local\",\"esxi.vm\":\"8.0_PMG_VM_8.102-new\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428100,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi15.motadata.local\",\"esxi.vm\":\"jenkins-sonarqube_10.77\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428101,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi13.motadata.local\",\"esxi.vm\":\"AIOps-collector-1\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428098,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi26.motadata.local\",\"esxi.vm\":\"AIX-vm-10.64\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428101,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi13.motadata.local\",\"esxi.vm\":\"QA_Prahar_windows2016_12.138_13.95\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428098,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi26.motadata.local\",\"esxi.vm\":\"8.0_nilesh parmar_ubuntu_8.136\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428097,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi27.motadata.local\",\"esxi.vm\":\"agent-test ubuntu 14 blank_9.149\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428097,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi27.motadata.local\",\"esxi.vm\":\"GNS3-Linux-Client-10.128\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428097,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi27.motadata.local\",\"esxi.vm\":\"vCLS-c224c48c-e09e-4096-89bb-7fcf59e77b63\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428101,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi13.motadata.local\",\"esxi.vm\":\"Flow_Perfromance_upto_5000eps_172.16.15.234\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428101,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi13.motadata.local\",\"esxi.vm\":\"NFR_Maste_172.16.15.213\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428101,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi13.motadata.local\",\"esxi.vm\":\"8.0.8_24.04_ISO_Database\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428101,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi13.motadata.local\",\"esxi.vm\":\"Functional_ServiceOps_Abhi\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428097,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi27.motadata.local\",\"esxi.vm\":\"GNS3 VM-ESXI_10.120-old-10.127\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428101,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi13.motadata.local\",\"esxi.vm\":\"Log_Perfromance_upto_5000eps_172.16.15.235\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428101,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi13.motadata.local\",\"esxi.vm\":\"ISO_8.0.6_15.212_DB\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428098,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi26.motadata.local\",\"esxi.vm\":\"Viram-AIOPS-RPE-13.40\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428100,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi15.motadata.local\",\"esxi.vm\":\"jenkins agent2__10.117\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428097,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi27.motadata.local\",\"esxi.vm\":\"8.0_Xenserver3_8.173-clone\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428100,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi15.motadata.local\",\"esxi.vm\":\"Viram_AIOPS-10.145\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428101,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi13.motadata.local\",\"esxi.vm\":\"8.0.5_Master\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428098,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi26.motadata.local\",\"esxi.vm\":\"8.0_SQL_failover_node2_9.159\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428098,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi26.motadata.local\",\"esxi.vm\":\"HP-UX-VM_13.67\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428098,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi26.motadata.local\",\"esxi.vm\":\"8.0_SQL_failover_node1_9.146\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428101,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi13.motadata.local\",\"esxi.vm\":\"8.0.5_Datbase\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428099,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi18.motadata.local\",\"esxi.vm\":\"RADWIN_RPE1_Functional\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428099,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi18.motadata.local\",\"esxi.vm\":\"RADWIN_Master_Functional\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428098,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi26.motadata.local\",\"esxi.vm\":\"8.0_ubuntu_mail server_8.114\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428101,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi13.motadata.local\",\"esxi.vm\":\"RHEL_8.0.8\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"},{\"entity.id\":54638428100,\"object.type\":\"VMware ESXi\",\"object.ip\":\"************\",\"monitor\":\"esxi15.motadata.local\",\"esxi.vm\":\"Dev_Aagam_master_13.26\",\"esxi.vm.uptime.seconds.sum\":515100,\"esxi.vm.uptime.seconds.sum.formatted\":\"5 d 23 h 5 m\",\"esxi.vm.downtime.seconds.sum\":null,\"esxi.vm.downtime.seconds.sum.formatted\":\"0\",\"esxi.vm.uptime.percent.avg\":100,\"esxi.vm.uptime.percent.avg.formatted\":\"100%\",\"esxi.vm.downtime.percent.avg\":0,\"esxi.vm.downtime.percent.avg.formatted\":\"0%\"}]}"))
            .put("testMultiTopNResponse", new JsonObject("{\"result\":[{\"entity.id\":50915505094,\"object.type\":\"Cisco Wireless\",\"object.vendor\":\"Cisco Systems\",\"object.ip\":\"***********\",\"monitor\":\"CISCO-WLC\",\"cisco.wireless.controller.cpu.percent.avg\":0.16,\"cisco.wireless.controller.cpu.percent.avg.formatted\":\"0.16%\",\"cisco.wireless.controller.cpu.percent.min\":0,\"cisco.wireless.controller.cpu.percent.min.formatted\":\"0%\",\"cisco.wireless.controller.cpu.percent.max\":7,\"cisco.wireless.controller.cpu.percent.max.formatted\":\"7%\"}]}"))
            .put("testMultiTopNResponse1", new JsonObject("{\"result\":[{\"entity.id\":50915505172,\"object.type\":\"Aruba Wireless\",\"object.vendor\":\"Aruba Networks\",\"object.ip\":\"*************\",\"monitor\":\"ArubaMC-VA_BB_8A_50\",\"aruba.wireless.controller.cpu.percent.avg\":10.54,\"aruba.wireless.controller.cpu.percent.avg.formatted\":\"10.54%\",\"aruba.wireless.controller.cpu.percent.max\":35,\"aruba.wireless.controller.cpu.percent.max.formatted\":\"35%\",\"aruba.wireless.controller.cpu.percent.min\":7,\"aruba.wireless.controller.cpu.percent.min.formatted\":\"7%\"}]}"))
            .put("testCPUForecastResponse", new JsonObject("{\"prediction.index\":2,\"result\":[{\"Timestamp\":1725408000000,\"1^system.cpu.percent^avg\":70,\"1^system.cpu.percent^avg^upperbound\":70,\"1^system.cpu.percent^avg^lowerbound\":70},{\"Timestamp\":1725408000000,\"1^system.cpu.percent^avg\":75,\"1^system.cpu.percent^avg^upperbound\":70,\"1^system.cpu.percent^avg^lowerbound\":70},{\"Timestamp\":1725451200000,\"1^system.cpu.percent^avg\":85,\"1^system.cpu.percent^avg^upperbound\":70,\"1^system.cpu.percent^avg^lowerbound\":70},{\"Timestamp\":1725494400000,\"1^system.cpu.percent^avg\":95,\"1^system.cpu.percent^avg^upperbound\":70,\"1^system.cpu.percent^avg^lowerbound\":70},{\"Timestamp\":1725451200000,\"1^system.cpu.percent^avg\":100,\"1^system.cpu.percent^avg^upperbound\":70,\"1^system.cpu.percent^avg^lowerbound\":70},{\"Timestamp\":1725494400000,\"1^system.cpu.percent^avg\":100,\"1^system.cpu.percent^avg^upperbound\":70,\"1^system.cpu.percent^avg^lowerbound\":70}],\"instance\":\"\"}"))
            .put("testDiskForecastResponse", new JsonObject("{\"prediction.index\":2,\"result\":[{\"Timestamp\":1725408000000,\"1^system.disk.used.percent^avg\":70,\"1^system.disk.used.percent^avg^upperbound\":70,\"1^system.disk.used.percent^avg^lowerbound\":70,\"2^system.disk.used.percent^avg\":95,\"2^system.disk.used.percent^avg^upperbound\":70,\"2^system.disk.used.percent^avg^lowerbound\":70},{\"Timestamp\":1725408000000,\"1^system.disk.used.percent^avg\":70,\"1^system.disk.used.percent^avg^upperbound\":70,\"1^system.disk.used.percent^avg^lowerbound\":70,\"2^system.disk.used.percent^avg\":95,\"2^system.disk.used.percent^avg^upperbound\":70,\"2^system.disk.used.percent^avg^lowerbound\":70},{\"Timestamp\":1725451200000,\"1^system.disk.used.percent^avg\":75,\"1^system.disk.used.percent^avg^upperbound\":70,\"1^system.disk.used.percent^avg^lowerbound\":70,\"2^system.disk.used.percent^avg\":95,\"2^system.disk.used.percent^avg^upperbound\":70,\"2^system.disk.used.percent^avg^lowerbound\":70},{\"Timestamp\":1725494400000,\"1^system.disk.used.percent^avg\":75,\"1^system.disk.used.percent^avg^upperbound\":70,\"1^system.disk.used.percent^avg^lowerbound\":70,\"2^system.disk.used.percent^avg\":95,\"2^system.disk.used.percent^avg^upperbound\":70,\"2^system.disk.used.percent^avg^lowerbound\":70},{\"Timestamp\":1725451200000,\"1^system.disk.used.percent^avg\":75,\"1^system.disk.used.percent^avg^upperbound\":70,\"1^system.disk.used.percent^avg^lowerbound\":70,\"2^system.disk.used.percent^avg\":95,\"2^system.disk.used.percent^avg^upperbound\":70,\"2^system.disk.used.percent^avg^lowerbound\":70},{\"Timestamp\":1725494400000,\"1^system.disk.used.percent^avg\":78,\"1^system.disk.used.percent^avg^upperbound\":70,\"1^system.disk.used.percent^avg^lowerbound\":70,\"2^system.disk.used.percent^avg\":95,\"2^system.disk.used.percent^avg^upperbound\":70,\"2^system.disk.used.percent^avg^lowerbound\":70}],\"instance\":\"\"}"))
            .put("testVMwareVMsForecastResponse", new JsonObject("{\"prediction.index\":2,\"result\":[{\"Timestamp\":1725408000000,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg\":70,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^lowerbound\":70,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg\":95,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^lowerbound\":70},{\"Timestamp\":1725408000000,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg\":70,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^lowerbound\":70,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg\":95,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^lowerbound\":70},{\"Timestamp\":1725451200000,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg\":75,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^lowerbound\":70,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg\":95,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^lowerbound\":70},{\"Timestamp\":1725494400000,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg\":75,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^lowerbound\":70,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg\":95,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^lowerbound\":70},{\"Timestamp\":1725451200000,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg\":75,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^lowerbound\":70,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg\":95,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^lowerbound\":70},{\"Timestamp\":1725494400000,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg\":78,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^lowerbound\":70,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg\":95,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^lowerbound\":70}],\"instance\":\"\"}"))
            .put("testVMwareVMsForecastResponse1", new JsonObject("{\"prediction.index\":2,\"result\":[{\"Timestamp\":1725408000000,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg\":70,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^lowerbound\":70},{\"Timestamp\":1725408000000,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg\":70,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^lowerbound\":70},{\"Timestamp\":1725451200000,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg\":75,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^lowerbound\":70},{\"Timestamp\":1725494400000,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg\":75,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^lowerbound\":70},{\"Timestamp\":1725451200000,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg\":75,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^lowerbound\":70},{\"Timestamp\":1725494400000,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg\":78,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Ubuntu14-10.183^esxi.vm~cpu.percent^avg^lowerbound\":70}],\"instance\":\"\"}"))
            .put("testCustomAvailabilityFlapSummary", new JsonObject("{\"result\":[{\"duration\":1740,\"status.flap.history\":\"\",\"object.id\":7,\"Timestamp\":1725408000000}]}"))
            .put("testVMwareVMsForecastResponse2", new JsonObject("{\"prediction.index\":2,\"result\":[{\"Timestamp\":1725408000000,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg\":95,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^lowerbound\":70},{\"Timestamp\":1725408000000,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg\":95,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^lowerbound\":70},{\"Timestamp\":1725451200000,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg\":95,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^lowerbound\":70},{\"Timestamp\":1725494400000,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg\":95,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^lowerbound\":70},{\"Timestamp\":1725451200000,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg\":95,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^lowerbound\":70},{\"Timestamp\":1725494400000,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg\":95,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^upperbound\":70,\"2###Latest-Windows-10-11.216^esxi.vm~cpu.percent^avg^lowerbound\":70}],\"instance\":\"\"}"))
            .put("testAvailabilityResponse", new JsonObject("{\"result\": [{\"monitor\": 4,\"interface\":\"Booking-System\",\"interface~uptime.percent^avg\":0.0},{\"monitor\": 4,\"interface\":\"Dev_sankalp_aiops-8.168\",\"interface~uptime.percent^avg\":0.0},{\"monitor\": 4,\"interface\":\"AIOPS_DOcker_\",\"interface~uptime.percent^avg\":50.0}]}"));

    private static MessageConsumer<JsonObject> messageConsumer;

    private static boolean abortQuery = false;

    private static boolean capacityPlanning = false;

    private static boolean forecastCPU = false;

    private static boolean forecastDisk = false;

    private static boolean forecastVMwareVMs = false;

    private static boolean forecastVMwareVMsBatch = false;

    private static boolean multiAvailability = false;

    private static boolean customScript = false;

    private static boolean multiTopN = false;

    private static boolean availabilityFlapSummary = false;

    private static boolean availability = false;

    private static long queryId = -1L;

    private static boolean eventQuery = false; // for event based test cases we will require this flag...

    private static String testCase = EMPTY_VALUE;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext) throws InterruptedException
    {
        TestUtil.vertx().setPeriodic(2 * 1000, timer ->
        {
            var object = RemoteEventProcessorCacheStore.getStore().getItem(RemoteEventProcessorConfigStore.getStore().getItemIdByUUID("primary_db_uuid", GlobalConstants.BootstrapType.DATASTORE.name(), GlobalConstants.InstallationMode.PRIMARY.name()));

            RemoteEventProcessorCacheStore.getStore().updateItem(RemoteEventProcessorConfigStore.getStore().getItemIdByUUID("primary_db_uuid", GlobalConstants.BootstrapType.DATASTORE.name(), GlobalConstants.InstallationMode.PRIMARY.name()), object.put(STATUS, STATUS_UP).put(HEARTBEAT_STATE, STATE_RUNNING));
        });

        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        subscribe(MotadataConfigUtil.getDatastoreReaderPort());

        packDBResponse();

        testContext.completeNow();
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        HAS_MORE_EVENT.set(false);

        SUBSCRIBER.close();

        testContext.completeNow();
    }

    private static void subscribe(int port)
    {
        SUBSCRIBER.setHWM(MotadataConfigUtil.getEventBacklogSize());

        SUBSCRIBER.setRcvHWM(MotadataConfigUtil.getEventBacklogSize());

        SUBSCRIBER.connect("tcp://*:" + port);

        SUBSCRIBER.subscribe("primary_db_uuid" + DOT_SEPARATOR + DATASTORE_QUERY_TOPIC);

        new Thread(() ->
        {
            while (HAS_MORE_EVENT.get())
            {
                try
                {
                    var topicBytes = SUBSCRIBER.recv();

                    LOGGER.debug("query context received on topic::" + new String(topicBytes));

                    var bufferBytes = SUBSCRIBER.recv();

                    if (bufferBytes.length > 0)
                    {
                        var buffer = Buffer.buffer(bufferBytes);

                        if (!(buffer.length() == 1 && buffer.getBytes()[0] == 18))
                        {
                            var event = new JsonObject(new String(buffer.getBytes(1, buffer.length())));

                            if (event.containsKey(VisualizationConstants.QUERY_ID) && (((availability || availabilityFlapSummary || capacityPlanning || multiAvailability || multiTopN || forecastCPU || forecastDisk || forecastVMwareVMs || forecastVMwareVMsBatch) && QUERY_IDS.contains(event.getLong(VisualizationConstants.QUERY_ID))) || queryId == event.getLong(VisualizationConstants.QUERY_ID)))
                            {
                                var queryId = event.getLong(VisualizationConstants.QUERY_ID);

                                LOGGER.debug("query context received for query Id::" + queryId + " with context::" + event);

                                if (queryId == 1234567 && abortQuery)
                                {
                                    QUERY_CONTEXTS.get(queryId).completeNow();
                                }
                                else
                                {
                                    var valid = true;

                                    var visualizationDataSource = event.getJsonObject(VisualizationConstants.VISUALIZATION_DATA_SOURCES);

                                    assertNotNull(visualizationDataSource.getValue(VisualizationConstants.DATA_POINTS));

                                    assertNotNull(visualizationDataSource.getValue(ENTITIES));

                                    assertNotNull(visualizationDataSource.getValue(VisualizationConstants.PLUGINS));

                                    if ((visualizationDataSource.containsKey(VisualizationConstants.VISUALIZATION_RESULT_BY) && !visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).isEmpty())
                                            && (visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).getString(0).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.GROUP.getName()) || visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).getString(0).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.TAG.getName())))
                                    {
                                        assertNotNull(visualizationDataSource.getValue(visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).getString(0)));
                                    }

                                    if (!(visualizationDataSource.containsKey("test.plugins") && visualizationDataSource.getString("test.plugins").equalsIgnoreCase("no")))
                                    {
                                        for (var dataPoint : visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS))
                                        {
                                            var context = JsonObject.mapFrom(dataPoint);

                                            var plugins = context.getJsonArray(VisualizationConstants.PLUGINS);

                                            if (eventQuery)
                                            {
                                                assertEquals(1, plugins.size());
                                            }

                                            var pluginIds = new ArrayList<String>();

                                            for (var index = 0; index < plugins.size(); index++)
                                            {
                                                var pluginId = plugins.getString(index);

                                                if (pluginIds.contains(pluginId))
                                                {
                                                    valid = false;
                                                }
                                                else
                                                {
                                                    pluginIds.add(pluginId);
                                                }
                                            }

                                            if (!context.containsKey(ENTITIES))
                                            {
                                                valid = false;

                                                break;
                                            }
                                        }
                                    }

                                    if (multiAvailability)
                                    {
                                        for (var i = 0; i < 3; i++)
                                        {
                                            LOGGER.info("Sending Response of multiAvailability | Query ID : " + queryId + " | Sub query ID : " + event.getLong(VisualizationConstants.SUB_QUERY_ID));

                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject(i == 0 ? "testMultiAvailability" : "testMultiAvailability" + i),
                                                            VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).
                                                    getBytes()));
                                        }

                                        QUERY_CONTEXTS.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                        multiAvailability = false;
                                    }

                                    if (multiTopN)
                                    {
                                        for (var i = 0; i < 7; i++)
                                        {
                                            LOGGER.info("Sending Response of multiTopN | Query ID : " + queryId + " | Sub query ID : " + event.getLong(VisualizationConstants.SUB_QUERY_ID));

                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject(i == 0 ? "testMultiTopNResponse" : "testMultiTopNResponse" + i), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));
                                        }

                                        QUERY_CONTEXTS.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                        multiTopN = false;
                                    }

                                    if (capacityPlanning)
                                    {
                                        LOGGER.info("Size of Batch Response : " + BATCH_RESPONSES.getJsonArray("testCapacityPlanReport").size());

                                        if (!BATCH_RESPONSES.getJsonArray("testCapacityPlanReport").isEmpty())
                                        {
                                            LOGGER.info("Sending Response of capacity planning | Query ID : " + queryId + " | Sub query ID : " + event.getLong(VisualizationConstants.SUB_QUERY_ID));

                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult((JsonObject) BATCH_RESPONSES.getJsonArray("testCapacityPlanReport").remove(BATCH_RESPONSES.getJsonArray("testCapacityPlanReport").size() - 1), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));
                                        }

                                        if (BATCH_RESPONSES.getJsonArray("testCapacityPlanReport").isEmpty())
                                        {
                                            capacityPlanning = false;

                                            QUERY_IDS.clear();
                                        }
                                    }

                                    if (forecastCPU)
                                    {
                                        LOGGER.info("Sending Response of forecastCPU | Query ID : " + queryId + " | Sub query ID : " + event.getLong(VisualizationConstants.SUB_QUERY_ID));

                                        Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(packGridResult(RESPONSES.getJsonObject("testCPUForecastResponse"), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                        COUNTER.incrementAndGet();

                                        forecastCPU = false;

                                        QUERY_IDS.clear();
                                    }

                                    if (forecastDisk)
                                    {
                                        LOGGER.info("Sending Response of forecastDisk | Query ID : " + queryId + " | Sub query ID : " + event.getLong(VisualizationConstants.SUB_QUERY_ID));

                                        Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(packGridResult(RESPONSES.getJsonObject("testDiskForecastResponse"), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                        COUNTER.incrementAndGet();

                                        forecastDisk = false;

                                        QUERY_IDS.clear();
                                    }

                                    if (forecastVMwareVMs)
                                    {
                                        LOGGER.info("Sending Response of forecastVMWare | Query ID : " + queryId + " | Sub query ID : " + event.getLong(VisualizationConstants.SUB_QUERY_ID));

                                        Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(packGridResult(RESPONSES.getJsonObject("testVMwareVMsForecastResponse"), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                        forecastVMwareVMs = false;

                                        QUERY_IDS.clear();
                                    }

                                    if (forecastVMwareVMsBatch)
                                    {
                                        LOGGER.info("Sending Response of forecastVMWareVMsBatch | Query ID : " + queryId + " | Sub query ID : " + event.getLong(VisualizationConstants.SUB_QUERY_ID));

                                        COUNTER.incrementAndGet();

                                        Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(packGridResult(RESPONSES.getJsonObject("testVMwareVMsForecastResponse" + COUNTER.get()), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                        if (COUNTER.get() >= 2)
                                        {
                                            forecastVMwareVMsBatch = false;

                                            QUERY_IDS.clear();
                                        }

                                    }

                                    if (customScript)
                                    {
                                        LOGGER.info("Sending Response of customScript | Query ID : " + queryId + " | Sub query ID : " + event.getLong(VisualizationConstants.SUB_QUERY_ID));

                                        Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testExecuteCustomScript"), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                        QUERY_CONTEXTS.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                        customScript = false;
                                    }

                                    if (availabilityFlapSummary)
                                    {
                                        LOGGER.info("Sending Response of availabilityFlapSummary | Query ID : " + queryId + " | Sub query ID : " + event.getLong(VisualizationConstants.SUB_QUERY_ID));

                                        Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testCustomAvailabilityFlapSummary"), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                        QUERY_CONTEXTS.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                        availabilityFlapSummary = false;
                                    }

                                    if (availability)
                                    {
                                        LOGGER.info("Sending Response of availability | Query ID : " + queryId + " | Sub query ID : " + event.getLong(VisualizationConstants.SUB_QUERY_ID));

                                        Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testAvailabilityResponse"), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                        QUERY_CONTEXTS.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                        availability = false;
                                    }


                                    if (valid && QUERY_CONTEXTS.containsKey(queryId))
                                    {
                                        QUERY_CONTEXTS.get(queryId).completeNow();

                                        QUERY_CONTEXTS.remove(queryId);
                                    }
                                    else
                                    {

                                        if (QUERY_CONTEXTS.containsKey(queryId))
                                        {
                                            QUERY_CONTEXTS.get(queryId).failNow("Invalid Context received::" + event);

                                            QUERY_CONTEXTS.remove(queryId);
                                        }
                                        else
                                        {
                                            LOGGER.warn("Query Id Widget Context not received");
                                        }

                                        LOGGER.warn("Failed context::" + event);
                                    }
                                }
                            }
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.trace(testCase + " failed with queryId " + queryId);

                    LOGGER.error(exception);
                }
            }
        }, "DB Subscriber").start();
    }

    private static void packDBResponse()
    {
        RESPONSES_BY_TYPE.getMap().forEach((key, value) ->
        {
            var result = new JsonObject();

            try
            {
                var rows = JsonObject.mapFrom(value).getJsonArray(RESULT);

                if (key.contains("Forecast"))
                {
                    result.put("prediction.index", JsonObject.mapFrom(value).getInteger("prediction.index"));

                    rows = prepareForecastContext(rows);
                }

                for (var i = 0; i < rows.size(); i++)
                {
                    rows.getJsonObject(i).forEach(row -> ((JsonArray) result.getMap().computeIfAbsent(row.getKey(), item -> new JsonArray())).add(row.getValue()));
                }

                RESPONSES.put(key, result);
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });
    }

    public static Buffer packGridResult(JsonObject result, int visualizationType, long queryId, long subQueryId)
    {
        var predictionIndex = result.remove("prediction.index");

        var buffer = Buffer.buffer();

        packHeaders(buffer, queryId, subQueryId, CommonUtil.getShort(result.containsKey(QUERY_PROGRESS) ? result.remove(QUERY_PROGRESS) : 100), CommonUtil.getShort(1));

        buffer.setUnsignedByte(34, CommonUtil.getShort(visualizationType));

        buffer.setUnsignedByte(35, CommonUtil.getShort(result.size()));

        result.getMap().forEach((key, value) ->
        {
            var records = (JsonArray) value;

            if (!records.isEmpty())
            {
                var dataType = new AtomicReference<>(STRING);

                var sparkline = key.contains("sparkline");

                var category = DatastoreConstants.getDataCategory(true, key.split(CARET_SEPARATOR_WITH_ESCAPE)[0], CommonUtil.getString(records.getValue(0)));

                if (sparkline)
                {
                    //For Sparkline, set data as per values...
                    var row = records.getJsonObject(0);

                    for (var entry : row.getMap().entrySet())
                    {
                        var column = row.getValue(CommonUtil.getString(entry.getKey()));

                        if (column instanceof Float || column instanceof Double)
                        {
                            dataType.set(FLOAT32);

                            break;
                        }
                        // in case of result received from custom script (GoLang), value converted from Long to BigInteger
                        else if (column instanceof Long || column instanceof BigInteger)
                        {
                            dataType.set(INT64);

                            break;
                        }
                        else if (column instanceof Integer)
                        {
                            dataType.set(INT32);

                            break;
                        }
                    }
                }
                else
                {
                    if (category == DatastoreConstants.DataCategory.FLOAT.getName() || records.getValue(0) instanceof Double || records.getValue(0) instanceof Float)
                    {
                        dataType.set(FLOAT32);
                    }

                    // in case of result received from custom script (GoLang), value converted from Long to BigInteger
                    else if (records.getValue(0) instanceof Long || records.getValue(0) instanceof BigInteger)
                    {
                        dataType.set(INT64);
                    }

                    else if (records.getValue(0) instanceof Integer)
                    {
                        dataType.set(INT32);

                        // In the custom script report when reading response from script (python, golang, node js), getting mix array of integer and Long like [12,4,6,3,5,-9223372036854775808]. So, It is
                        // throwing ClassCastException so, have to assign category by checking each value
                        for (var index = 0; index < records.size(); index++)
                        {
                            if (records.getValue(index) instanceof Long || CommonUtil.getLong(records.getValue(index)) == Long.MIN_VALUE)
                            {
                                dataType.set(INT64);

                                break;
                            }
                        }
                    }

                }

                buffer.setIntLE(36, records.size());

                buffer.setShortLE(40, CommonUtil.getShort(predictionIndex));

                buffer.setIntLE(buffer.length(), 0);//extra bytes

                if (sparkline)
                {
                    //Adding Aggregation Ordinal
                    buffer.setUnsignedByte(buffer.length(), CommonUtil.getShort(DatastoreConstants.AggregationType.SPARKLINE.ordinal()));
                }
                else
                {
                    buffer.setUnsignedByte(buffer.length(), STRING.equals(dataType.get()) ? CommonUtil.getShort(DatastoreConstants.AggregationType.COUNT.ordinal()) : CommonUtil.getShort(DatastoreConstants.AggregationType.LAST.ordinal()));
                }

                buffer.setUnsignedByte(buffer.length(), dataType.get().getSize());

                buffer.setShortLE(buffer.length(), CommonUtil.getShort(key.length()));

                buffer.setString(buffer.length(), key);

                buffer.setShortLE(buffer.length(), CommonUtil.getShort(key.length()));

                buffer.setString(buffer.length(), key);

                if (sparkline)
                {
                    //Adding length of the whole rows to buffer...
                    buffer.setShortLE(buffer.length(), CommonUtil.getShort(records.getJsonObject(0).size()));
                }

                for (var i = 0; i < records.size(); i++)
                {
                    switch (dataType.get())
                    {
                        case INT32 ->
                        {
                            if (sparkline)
                            {
                                for (var row : records.getJsonObject(i).getMap().entrySet())
                                {
                                    buffer.setLongLE(buffer.length(), CommonUtil.getLong(row.getKey()));
                                }

                                for (var row : records.getJsonObject(i).getMap().entrySet())
                                {
                                    buffer.setIntLE(buffer.length(), CommonUtil.getInteger(row.getValue()));
                                }
                            }
                            else
                            {
                                buffer.setIntLE(buffer.length(), CommonUtil.getInteger(records.getValue(i)));
                            }
                        }

                        case INT64 ->
                        {
                            if (sparkline)
                            {
                                for (var row : records.getJsonObject(i).getMap().entrySet())
                                {
                                    buffer.setLongLE(buffer.length(), CommonUtil.getLong(row.getKey()));
                                }

                                for (var row : records.getJsonObject(i).getMap().entrySet())
                                {
                                    buffer.setLongLE(buffer.length(), CommonUtil.getLong(row.getValue()));
                                }
                            }
                            else
                            {
                                buffer.setLongLE(buffer.length(), CommonUtil.getLong(records.getValue(i)));
                            }
                        }


                        case FLOAT32 ->
                        {
                            if (sparkline)
                            {

                                for (var row : records.getJsonObject(i).getMap().entrySet())
                                {
                                    buffer.setLongLE(buffer.length(), CommonUtil.getLong(row.getKey()));
                                }

                                for (var row : records.getJsonObject(i).getMap().entrySet())
                                {
                                    var bytes = ByteBuffer.allocate(8).putDouble(CommonUtil.getFloat(row.getValue())).array();

                                    for (var index = bytes.length - 1; index >= 0; index--)
                                    {
                                        buffer.setByte(buffer.length(), bytes[index]);
                                    }
                                }

                            }
                            else
                            {
                                var bytes = ByteBuffer.allocate(8).putDouble(CommonUtil.getFloat(records.getValue(i))).array();

                                for (var index = bytes.length - 1; index >= 0; index--)
                                {
                                    buffer.setByte(buffer.length(), bytes[index]);
                                }
                            }
                        }

                        case STRING ->
                        {
                            buffer.setIntLE(buffer.length(), CommonUtil.getInteger(String.valueOf(records.getValue(i)).getBytes(StandardCharsets.UTF_8).length));

                            buffer.setString(buffer.length(), String.valueOf(records.getValue(i)));
                        }
                    }
                }
            }
        });

        return buffer;
    }

    static void packHeaders(Buffer buffer, long queryId, long subQueryId, short progress, short status)
    {
        buffer.setLongLE(0, queryId);//queryId

        buffer.setLongLE(8, subQueryId);//subqueryId

        buffer.setUnsignedByte(16, progress);//progress of query

        buffer.setLongLE(17, 0);//records of query

        buffer.setLongLE(25, 0);//time of query

        buffer.setUnsignedByte(33, status);//status 0 means fail 1 success
    }

    public static JsonArray prepareForecastContext(JsonArray result)
    {

        for (var i = 0; i < result.size(); i++)
        {
            result.getJsonObject(i).put("Timestamp", System.currentTimeMillis() + (i * (12 * 60 * 60 * 1000))); // need to update timestamp according current timestamp
        }

        return result;
    }

    public static void prepareContext()
    {
        BATCH_RESPONSES.put("testCapacityPlanReport", new JsonArray());

        RESPONSES.getMap().forEach((key, value) ->
        {
            if (key.contains("Capacity"))
            {
                for (var i = 0; i < 4; i++)
                {
                    BATCH_RESPONSES.put("testCapacityPlanReport", BATCH_RESPONSES.getJsonArray("testCapacityPlanReport").add(value));
                }
            }
        });
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            testContext.awaitCompletion(5, TimeUnit.SECONDS);

            LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));
        }
        catch (Exception ignored)
        {
            // ignored
        }

        queryId = -1;

        testCase = EMPTY_VALUE;

        eventQuery = false;

        abortQuery = false;

        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    @Timeout(360 * 1000)
    void testCustomAvailabilityFlapSummary(VertxTestContext testContext, TestInfo testInfo)
    {
        var reportName = "Test Status Flap " + System.currentTimeMillis();

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if ((message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null) && message.body().getJsonObject(EVENT_CONTEXT).getString(VISUALIZATION_NAME).equalsIgnoreCase(reportName))
            {
                LOGGER.info(testInfo.getTestMethod().get().getName() + " - message: " + message.body());

                var context = message.body().getJsonObject(EVENT_CONTEXT);

                Assertions.assertNotNull(VISUALIZATION_NAME);

                Assertions.assertNotNull(VisualizationConstants.VISUALIZATION_DATA_SOURCES);

                assertEquals(reportName, context.getString(VISUALIZATION_NAME));

                Assertions.assertNotNull(VisualizationConstants.VISUALIZATION_TYPE);

                Assertions.assertNotNull(VisualizationConstants.VISUALIZATION_CATEGORY);

                var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true).getJsonArray(RESULT);

                LOGGER.info(testInfo.getTestMethod().get().getName() + " - result: " + result);

                for (var index = 0; index < result.size(); index++)
                {
                    var item = result.getJsonObject(index);

                    assertTrue(item.containsKey("duration"));

                    assertTrue(item.containsKey("status.flap.history"));
                }

                testContext.completeNow();
            }
        });

        var report = new JsonObject("{\"report.name\":\"Test Status Flap\",\"report.widgets\":[{\"id\":-1,\"container.type\":\"report\",\"visualization.name\":\"Test Status Flap\",\"visualization.timeline\":{\"relative.timeline\":\"today\",\"visualization.time.range.inclusive\":\"no\"},\"visualization.category\":\"Chart\",\"visualization.type\":\"Grid\",\"visualization.data.sources\":[{\"type\":\"hourly.status.flap\",\"status\":[],\"filters\":{\"data.filter\":{},\"result.filter\":{},\"drill.down.filter\":{}},\"visualization.result.by\":[\"monitor\"],\"data.points\":[{\"data.point\":\"monitor\",\"aggregator\":\"avg\",\"entity.type\":\"Monitor\",\"entities\":[]}]}],\"visualization.properties\":{\"grid\":{\"visualization.grid.properties.required\":\"no\",\"searchable\":\"yes\",\"column.selection\":\"no\",\"header\":\"yes\",\"style\":{\"header.font.size\":\"small\"},\"columns\":[{\"name\":\"instance\",\"show\":\"yes\",\"sortable\":\"yes\",\"disable\":\"no\",\"resizable\":\"yes\",\"selectable\":\"yes\",\"orderable\":\"yes\",\"position\":0,\"style\":{}},{\"name\":\"monitor\",\"show\":\"yes\",\"sortable\":\"yes\",\"disable\":\"no\",\"resizable\":\"yes\",\"selectable\":\"yes\",\"orderable\":\"yes\",\"position\":1,\"style\":{}},{\"name\":\"status.flap.history\",\"show\":\"yes\",\"sortable\":\"yes\",\"disable\":\"no\",\"resizable\":\"yes\",\"selectable\":\"yes\",\"orderable\":\"yes\",\"position\":2,\"style\":{}},{\"name\":\"interface~status.flap.history\",\"show\":\"yes\",\"sortable\":\"yes\",\"disable\":\"no\",\"resizable\":\"yes\",\"selectable\":\"yes\",\"orderable\":\"yes\",\"position\":3,\"style\":{}},{\"name\":\"start.time\",\"show\":\"yes\",\"sortable\":\"no\",\"disable\":\"no\",\"resizable\":\"yes\",\"selectable\":\"yes\",\"orderable\":\"yes\",\"position\":4,\"style\":{}},{\"name\":\"end.time\",\"show\":\"yes\",\"sortable\":\"no\",\"disable\":\"no\",\"resizable\":\"yes\",\"selectable\":\"yes\",\"orderable\":\"yes\",\"position\":5,\"style\":{}},{\"name\":\"interface~duration\",\"show\":\"yes\",\"sortable\":\"yes\",\"disable\":\"no\",\"resizable\":\"yes\",\"selectable\":\"yes\",\"orderable\":\"yes\",\"position\":6,\"style\":{}},{\"name\":\"duration\",\"show\":\"yes\",\"sortable\":\"yes\",\"disable\":\"no\",\"resizable\":\"yes\",\"selectable\":\"yes\",\"orderable\":\"yes\",\"position\":7,\"style\":{}},{\"name\":\"object.type\",\"show\":\"no\",\"sortable\":\"yes\",\"disable\":\"no\",\"resizable\":\"yes\",\"selectable\":\"yes\",\"orderable\":\"yes\",\"position\":8,\"style\":{}},{\"name\":\"object.vendor\",\"show\":\"no\",\"sortable\":\"yes\",\"disable\":\"no\",\"resizable\":\"yes\",\"selectable\":\"yes\",\"orderable\":\"yes\",\"position\":9,\"style\":{}},{\"name\":\"object.ip\",\"show\":\"no\",\"sortable\":\"yes\",\"disable\":\"no\",\"resizable\":\"yes\",\"selectable\":\"yes\",\"orderable\":\"yes\",\"position\":10,\"style\":{}}]}},\"visualization.result.by\":[\"monitor\"],\"granularity\":{\"value\":5,\"unit\":\"m\"}}],\"report.scheduler\":\"no\",\"report.context\":{\"enable.grid.view\":\"yes\"},\"report.type\":\"availability.flap.summary\",\"report.layout\":\"auto\",\"report.format\":\"pdf\",\"report.category\":\"Performance\"}");

        report.put("report.name", reportName).getJsonArray("report.widgets").getJsonObject(0).put("visualization.name", reportName);

        report.getJsonArray(Report.REPORT_WIDGETS).getJsonObject(0).getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonArray(VisualizationConstants.DATA_POINTS).getJsonObject(0).getJsonArray(ENTITIES).add(ObjectConfigStore.getStore().getItemsByCategory(NMSConstants.Category.NETWORK).getLong(0));

        TestAPIUtil.post(REPORT_API_ENDPOINT, report, testContext.succeeding(response -> testContext.verify(() ->
        {
            var body = response.bodyAsJsonObject();

            LOGGER.debug("testCustomAvailabilityFlapSummary - body: " + body);

            Assertions.assertEquals(SC_OK, response.statusCode());

            Assertions.assertEquals(GlobalConstants.STATUS_SUCCEED, body.getString(GlobalConstants.STATUS));

            Assertions.assertNotNull(body.getLong(GlobalConstants.ID));

            var item = ReportConfigStore.getStore().getItem(body.getLong(GlobalConstants.ID));

            Assertions.assertNotNull(item);

            Assertions.assertEquals(report.getString(Report.REPORT_NAME), item.getString(Report.REPORT_NAME));

            Bootstrap.vertx().eventBus().send(UI_ACTION_VISUALIZATION_RENDER, new JsonObject().put(ReportConstants.REPORT_ID, body.getLong(ID))
                    .put(ID, -1)
                    .put(SESSION_ID, TestUtil.getSessionId())
                    .put(USER_NAME, "admin")
                    .put("test.request.batch.size", 10));

            availabilityFlapSummary = true;

        })));

        testCase = testInfo.getTestMethod().get().getName();

        try
        {
            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EventBusConstants.EVENT_VISUALIZATION_TEST, message ->
            {
                try
                {
                    message.body().getMap().forEach((key, value) ->
                    {
                        try
                        {
                            if (value instanceof Long && -1 == CommonUtil.getLong(key))
                            {
                                LOGGER.debug("query id received for widget::" + key + " with query Id::" + CommonUtil.getLong(value));

                                QUERY_IDS.add(CommonUtil.getLong(value));

                                QUERY_CONTEXTS.put(queryId, testContext);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            LOGGER.debug("Inside SetQueryId : " + message.body());
                        }
                    });
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister();

                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    public void testExecuteCustomScript(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put("report.id", 0)
                .put("report.type", "custom.script")
                .put("visualization.type", "custom.script")
                .put("report.script.type", "go")
                .put("report.script", "package main\n\nimport (\n\t\"bufio\"\n\tjson \"encoding/json\"\n\t\"fmt\"\n\t. \"motadatasdk/motadatatypes\"\n\t\"os\"\n\t\"strings\"\n\t\"time\"\n)\n\nvar (\n\tMotadataSeparator = \"_|@#|_\"\n\n\tMotadataEscapeSeparator = \"_\\\\|@#\\\\|_\"\n)\n\nfunc main() {\n\n\tvar request = \"{\\\"id\\\":-1,\\\"container.type\\\":\\\"dashboard\\\",\\\"visualization.name\\\":\\\"testExecuteCustomScript\\\",\\\"visualization.timeline\\\":{\\\"relative.timeline\\\":\\\"today\\\",\\\"visualization.time.range.inclusive\\\":\\\"no\\\"},\\\"visualization.category\\\":\\\"Grid\\\",\\\"visualization.type\\\":\\\"Grid\\\",\\\"visualization.data.sources\\\":[{\\\"type\\\":\\\"metric\\\",\\\"filters\\\":{\\\"data.filter\\\":{},\\\"result.filter\\\":{},\\\"drill.down.filter\\\":{}},\\\"visualization.result.by\\\":[\\\"monitor\\\"],\\\"data.points\\\":[{\\\"data.point\\\":\\\"system.cpu.percent\\\",\\\"aggregator\\\":\\\"avg\\\"}]}],\\\"visualization.properties\\\":{\\\"grid\\\":{\\\"visualization.grid.properties.required\\\":\\\"no\\\",\\\"searchable\\\":\\\"yes\\\",\\\"column.selection\\\":\\\"no\\\",\\\"header\\\":\\\"yes\\\",\\\"style\\\":{\\\"header.font.size\\\":\\\"small\\\"},\\\"columns\\\":[{\\\"name\\\":\\\"monitor\\\",\\\"show\\\":\\\"yes\\\",\\\"sortable\\\":\\\"yes\\\",\\\"disable\\\":\\\"no\\\",\\\"resizable\\\":\\\"yes\\\",\\\"selectable\\\":\\\"yes\\\",\\\"orderable\\\":\\\"yes\\\",\\\"position\\\":1,\\\"style\\\":{}},{\\\"name\\\":\\\"system.cpu.percent.avg\\\",\\\"show\\\":\\\"yes\\\",\\\"sortable\\\":\\\"yes\\\",\\\"disable\\\":\\\"no\\\",\\\"resizable\\\":\\\"yes\\\",\\\"selectable\\\":\\\"yes\\\",\\\"orderable\\\":\\\"yes\\\",\\\"position\\\":2,\\\"style\\\":{}},{\\\"name\\\":\\\"object.type\\\",\\\"show\\\":\\\"no\\\",\\\"sortable\\\":\\\"yes\\\",\\\"disable\\\":\\\"no\\\",\\\"resizable\\\":\\\"yes\\\",\\\"selectable\\\":\\\"yes\\\",\\\"orderable\\\":\\\"yes\\\",\\\"position\\\":3,\\\"style\\\":{}},{\\\"name\\\":\\\"object.vendor\\\",\\\"show\\\":\\\"no\\\",\\\"sortable\\\":\\\"yes\\\",\\\"disable\\\":\\\"no\\\",\\\"resizable\\\":\\\"yes\\\",\\\"selectable\\\":\\\"yes\\\",\\\"orderable\\\":\\\"yes\\\",\\\"position\\\":4,\\\"style\\\":{}},{\\\"name\\\":\\\"object.ip\\\",\\\"show\\\":\\\"no\\\",\\\"sortable\\\":\\\"yes\\\",\\\"disable\\\":\\\"no\\\",\\\"resizable\\\":\\\"yes\\\",\\\"selectable\\\":\\\"yes\\\",\\\"orderable\\\":\\\"yes\\\",\\\"position\\\":5,\\\"style\\\":{}}]}},\\\"visualization.result.by\\\":[\\\"monitor\\\"]}\"\n\t\n\targs := os.Args[1:]\n\n\targuments := make(MotadataMap)\n\n\tjson.Unmarshal([]byte(args[1]), &arguments)\n\n\tdecodedValue := make(MotadataMap)\n\n\tjson.Unmarshal([]byte(request), &decodedValue)\n\n\tfor key, value := range arguments {\n\n\t\tdecodedValue[key] = value\n\t}\n\n\tfmt.Println(fmt.Sprintf(\"%s%s\", decodedValue.ToJSON().ToString(), MotadataSeparator))\n\n\treader := bufio.NewReaderSize(os.Stdin, 204800)\n\n\tin := \"\"\n\n\tvar timeout = time.Second * time.Duration(120)\n\n\tgo func() {\n\n\t\t<-time.After(timeout)\n\n\t\tfmt.Println(\"Timeout reached! Exiting...\")\n\n\t\tos.Exit(0)\n\t}()\n\n\tfor true {\n\n\t\ttime.Sleep(time.Duration(10) * time.Millisecond)\n\n\t\tbytes := make([]byte, 204800)\n\n\t\tn, _ := reader.Read(bytes)\n\n\t\tin = in + string(bytes[:n])\n\n\t\tif strings.Contains(in, MotadataSeparator) {\n\n\t\t\tin = strings.Split(in, MotadataSeparator)[0]\n\n\t\t\tfmt.Println(fmt.Sprintf(\"%s%s\", in, MotadataSeparator))\n\n\t\t\tos.Exit(0)\n\t\t}\n\t}\n}")
                .put("id", -1)
                .put("ui.event.uuid", "3f44bb50-2131-46e5-9811-c2acd078d6ce")
                .put("session-id", TestUtil.getSessionId())
                .put("user.name", "admin");

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            var event = message.body();

            try
            {
                if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && event.containsKey(EVENT_CONTEXT) && event.getJsonObject(EVENT_CONTEXT) != null && !event.getJsonObject(EVENT_CONTEXT).isEmpty() && event.getJsonObject(EVENT_CONTEXT).containsKey(VISUALIZATION_NAME) && event.getJsonObject(EVENT_CONTEXT).getString(VISUALIZATION_NAME).equalsIgnoreCase("testExecuteCustomScript"))
                {
                    var result = VisualizationConstants.unpack(Buffer.buffer(event.getJsonObject(EVENT_CONTEXT).getBinary(RESULT)), LOGGER, false, null, false, true).getJsonArray(RESULT);

                    LOGGER.info(String.format("%s result: %s", testInfo.getTestMethod().get().getName(), event));

                    assertEquals(2, result.getJsonObject(0).size());

                    assertEquals("1", result.getJsonObject(0).getString("monitor"));

                    assertEquals(10.0, result.getJsonObject(0).getValue("system.cpu.percent^avg"));

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        try
        {
            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EventBusConstants.EVENT_VISUALIZATION_TEST, message ->
            {
                try
                {
                    message.body().getMap().forEach((key, value) ->
                    {
                        try
                        {
                            if (value instanceof Long && -1 == CommonUtil.getLong(key))
                            {
                                LOGGER.debug("query id received for widget::" + key + " with query Id::" + CommonUtil.getLong(value));

                                queryId = CommonUtil.getLong(value);

                                QUERY_CONTEXTS.put(queryId, testContext);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            LOGGER.debug("Inside SetQueryId : " + message.body());
                        }
                    });
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister();

                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        customScript = true;

        TestUtil.vertx().eventBus().send(UI_ACTION_VISUALIZATION_RENDER, context);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    @io.vertx.junit5.Timeout(value = 60, timeUnit = TimeUnit.SECONDS)
    public void testExecuteCustomScriptTimeOut(VertxTestContext testContext)
    {
        var context = new JsonObject()
                .put("report.id", 0)
                .put("report.type", "custom.script")
                .put("visualization.type", "custom.script")
                .put("report.script.type", "go")
                .put("report.script", """
                        package main
                        
                        import (
                        \t"bufio"
                        \tjson "encoding/json"
                        \t"fmt"
                        \t. "motadatasdk/motadatatypes"
                        \t"os"
                        \t"strings"
                        \t"time"
                        )
                        
                        var (
                        \tMotadataSeparator = "_|@#|_"
                        
                        \tMotadataEscapeSeparator = "_\\\\|@#\\\\|_"
                        )
                        
                        func main() {
                        
                        \tvar request = "{\\"id\\":-1,\\"container.type\\":\\"dashboard\\",\\"visualization.name\\":\\"Date:-01/09/2024\\",\\"visualization.timeline\\":{\\"relative.timeline\\":\\"custom\\",\\"from.date\\":\\"2024/09/22\\",\\"from.time\\":\\"00:00:00\\",\\"to.date\\":\\"2024/09/23\\",\\"to.time\\":\\"23:59:59\\",\\"visualization.time.range.inclusive\\":\\"no\\"},\\"visualization.category\\":\\"Grid\\",\\"visualization.type\\":\\"Grid\\",\\"visualization.data.sources\\":[{\\"type\\":\\"metric\\",\\"filters\\":{\\"data.filter\\":{},\\"result.filter\\":{},\\"drill.down.filter\\":{}},\\"visualization.result.by\\":[\\"monitor\\"],\\"data.points\\":[{\\"data.point\\":\\"system.cpu.percent\\",\\"aggregator\\":\\"avg\\"}]}],\\"visualization.properties\\":{\\"grid\\":{\\"visualization.grid.properties.required\\":\\"no\\",\\"searchable\\":\\"yes\\",\\"column.selection\\":\\"no\\",\\"header\\":\\"yes\\",\\"style\\":{\\"header.font.size\\":\\"small\\"},\\"columns\\":[{\\"name\\":\\"monitor\\",\\"show\\":\\"yes\\",\\"sortable\\":\\"yes\\",\\"disable\\":\\"no\\",\\"resizable\\":\\"yes\\",\\"selectable\\":\\"yes\\",\\"orderable\\":\\"yes\\",\\"position\\":1,\\"style\\":{}},{\\"name\\":\\"system.cpu.percent.avg\\",\\"show\\":\\"yes\\",\\"sortable\\":\\"yes\\",\\"disable\\":\\"no\\",\\"resizable\\":\\"yes\\",\\"selectable\\":\\"yes\\",\\"orderable\\":\\"yes\\",\\"position\\":2,\\"style\\":{}},{\\"name\\":\\"object.type\\",\\"show\\":\\"no\\",\\"sortable\\":\\"yes\\",\\"disable\\":\\"no\\",\\"resizable\\":\\"yes\\",\\"selectable\\":\\"yes\\",\\"orderable\\":\\"yes\\",\\"position\\":3,\\"style\\":{}},{\\"name\\":\\"object.vendor\\",\\"show\\":\\"no\\",\\"sortable\\":\\"yes\\",\\"disable\\":\\"no\\",\\"resizable\\":\\"yes\\",\\"selectable\\":\\"yes\\",\\"orderable\\":\\"yes\\",\\"position\\":4,\\"style\\":{}},{\\"name\\":\\"object.ip\\",\\"show\\":\\"no\\",\\"sortable\\":\\"yes\\",\\"disable\\":\\"no\\",\\"resizable\\":\\"yes\\",\\"selectable\\":\\"yes\\",\\"orderable\\":\\"yes\\",\\"position\\":5,\\"style\\":{}},{\\"name\\":\\"from.datetime\\",\\"title\\":\\"Date\\",\\"show\\":\\"yes\\",\\"sortable\\":\\"yes\\",\\"disable\\":\\"no\\",\\"resizable\\":\\"yes\\",\\"selectable\\":\\"yes\\",\\"orderable\\":\\"yes\\",\\"position\\":6,\\"style\\":{}}]}},\\"visualization.result.by\\":[\\"monitor\\"]}"
                        
                        \targs := os.Args[1:]
                        
                        \targuments := make(MotadataMap)
                        
                        \tjson.Unmarshal([]byte(args[1]), &arguments)
                        
                        \tdecodedValue := make(MotadataMap)
                        
                        \tjson.Unmarshal([]byte(request), &decodedValue)
                        
                        \tfor key, value := range arguments {
                        
                        \t\tdecodedValue[key] = value
                        \t}
                        
                        \tfmt.Println(fmt.Sprintf("%s%s", decodedValue.ToJSON().ToString(), MotadataSeparator))
                        
                        \treader := bufio.NewReaderSize(os.Stdin, 204800)
                        
                        \tin := ""
                        
                        \tvar timeout = time.Second * time.Duration(120)
                        
                        \tgo func() {
                        
                        \t\t<-time.After(timeout)
                        
                        \t\tfmt.Println("Timeout reached! Exiting...")
                        
                        \t\tos.Exit(0)
                        \t}()
                        
                        \tfor {
                        
                        \t\t
                        \t}
                        }""")
                .put("id", -1)
                .put("ui.event.uuid", "3f44bb50-2131-46e5-9811-c2acd078d6ce")
                .put("session-id", TestUtil.getSessionId())
                .put("user.name", "admin");

        messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.report.timeout", message ->
        {
            var event = message.body();

            if (event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
            {
                testContext.completeNow();
            }
        });


        TestUtil.vertx().eventBus().send(UI_ACTION_VISUALIZATION_RENDER, context);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testCapacityPlanReport(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null && message.body().getJsonObject(EVENT_CONTEXT).getString(VISUALIZATION_NAME).equalsIgnoreCase("Idle Servers"))
            {
                LOGGER.info(testInfo.getTestMethod().get().getName() + " - message: " + message.body());

                var context = message.body().getJsonObject(EVENT_CONTEXT);

                Assertions.assertNotNull(VISUALIZATION_NAME);

                Assertions.assertNotNull(VisualizationConstants.VISUALIZATION_DATA_SOURCES);

                assertEquals("Idle Servers", context.getString(VISUALIZATION_NAME));

                Assertions.assertNotNull(VisualizationConstants.VISUALIZATION_TYPE);

                Assertions.assertNotNull(VisualizationConstants.VISUALIZATION_CATEGORY);

                var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true).getJsonArray(RESULT).getJsonObject(0);

                LOGGER.info(testInfo.getTestMethod().get().getName() + " - result: " + result);

                assertEquals(2, result.getInteger("monitor"));

                assertFalse(result.getString("cpu.utilization").isEmpty());

                assertFalse(result.getString("memory.utilization").isEmpty());

                assertFalse(result.getString("disk.utilization").isEmpty());

                testContext.completeNow();
            }
        });

        testCase = testInfo.getTestMethod().get().getName();

        prepareContext();

        Bootstrap.vertx().eventBus().send(UI_ACTION_VISUALIZATION_RENDER, new JsonObject().put(ReportConstants.REPORT_ID, 1000000001031014L)
                .put(ID, -1)
                .put(SESSION_ID, TestUtil.getSessionId())
                .put(USER_NAME, "admin")
                .put("test.request.batch.size", 100));

        Bootstrap.vertx().eventBus().send(UI_ACTION_VISUALIZATION_RENDER, new JsonObject().put(ReportConstants.REPORT_ID, 1000000001031014L)
                .put(ID, -1)
                .put(SESSION_ID, TestUtil.getSessionId())
                .put(USER_NAME, "admin")
                .put("test.request.batch.size", 10));

        capacityPlanning = true;

        try
        {
            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EventBusConstants.EVENT_VISUALIZATION_TEST, message ->
            {
                try
                {
                    message.body().getMap().forEach((key, value) ->
                    {
                        try
                        {
                            if (value instanceof Long && -1 == CommonUtil.getLong(key))
                            {
                                LOGGER.debug("query id received for widget::" + key + " with query Id::" + CommonUtil.getLong(value));

                                QUERY_IDS.add(CommonUtil.getLong(value));

                                QUERY_CONTEXTS.put(queryId, testContext);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            LOGGER.debug("Inside SetQueryId : " + message.body());
                        }
                    });
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister();

                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testCPUForecastReport(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null && message.body().getJsonObject(EVENT_CONTEXT).getString(VISUALIZATION_NAME).equalsIgnoreCase("Servers - CPU Utilization Forecast Report"))
            {
                LOGGER.info(testInfo.getTestMethod().get().getName() + " - message: " + message.body());

                var context = message.body().getJsonObject(EVENT_CONTEXT);

                Assertions.assertNotNull(context.getValue(VISUALIZATION_NAME));

                Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                assertEquals("Servers - CPU Utilization Forecast Report", context.getString(VISUALIZATION_NAME));

                Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_TYPE));

                Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_CATEGORY));

                var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true).getJsonArray(RESULT).getJsonObject(0);

                LOGGER.info(testInfo.getTestMethod().get().getName() + " - result: " + result);

                assertEquals("1", result.getString("monitor"));

                assertFalse(result.getString("system.cpu.percent.80").isEmpty());

                assertFalse(result.getString("system.cpu.percent.90").isEmpty());

                assertFalse(result.getString("system.cpu.percent.100").isEmpty());

                testContext.completeNow();
            }
        });

        testCase = testInfo.getTestMethod().get().getName();

        Bootstrap.vertx().eventBus().send(UI_ACTION_VISUALIZATION_RENDER, new JsonObject().put(ReportConstants.REPORT_ID, 1000000001031033L)
                .put(ID, -1)
                .put(SESSION_ID, TestUtil.getSessionId())
                .put(USER_NAME, "admin")
                .put("test.request.batch.size", 100));

        forecastCPU = true;

        try
        {
            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EventBusConstants.EVENT_VISUALIZATION_TEST, message ->
            {
                try
                {
                    message.body().getMap().forEach((key, value) ->
                    {
                        try
                        {
                            if (value instanceof Long && -1 == CommonUtil.getLong(key))
                            {
                                LOGGER.debug("query id received for widget::" + key + " with query Id::" + CommonUtil.getLong(value));

                                QUERY_IDS.add(CommonUtil.getLong(value));

                                QUERY_CONTEXTS.put(queryId, testContext);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            LOGGER.debug("Inside SetQueryId : " + message.body());
                        }
                    });
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister();

                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testDiskForecastReport(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null && message.body().getJsonObject(EVENT_CONTEXT).getString(VISUALIZATION_NAME).equalsIgnoreCase("Servers - Disk Utilization Forecast Report"))
            {
                LOGGER.info(testInfo.getTestMethod().get().getName() + " - message: " + message.body());

                var context = message.body().getJsonObject(EVENT_CONTEXT);

                Assertions.assertNotNull(context.getValue(VISUALIZATION_NAME));

                Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                assertEquals("Servers - Disk Utilization Forecast Report", context.getString(VISUALIZATION_NAME));

                Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_TYPE));

                Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_CATEGORY));

                var rows = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true).getJsonArray(RESULT);

                var result = rows.getJsonObject(0);

                LOGGER.info(testInfo.getTestMethod().get().getName() + " - result: " + rows.encode());

                assertEquals(2, rows.size());

                assertEquals("1", result.getString("monitor"));

                assertTrue(result.getString("system.disk.used.percent.80").equalsIgnoreCase("Stable Growth"));

                assertTrue(result.getString("system.disk.used.percent.90").equalsIgnoreCase("Stable Growth"));

                assertTrue(result.getString("system.disk.used.percent.100").equalsIgnoreCase("Stable Growth"));

                testContext.completeNow();
            }
        });

        testCase = testInfo.getTestMethod().get().getName();

        Bootstrap.vertx().eventBus().send(UI_ACTION_VISUALIZATION_RENDER, new JsonObject().put(ReportConstants.REPORT_ID, 1000000001031035L)
                .put(ID, -1)
                .put(SESSION_ID, TestUtil.getSessionId())
                .put(USER_NAME, "admin")
                .put("test.request.batch.size", 100));

        forecastDisk = true;

        try
        {
            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EventBusConstants.EVENT_VISUALIZATION_TEST, message ->
            {
                try
                {
                    message.body().getMap().forEach((key, value) ->
                    {
                        try
                        {
                            if (value instanceof Long && -1 == CommonUtil.getLong(key))
                            {
                                LOGGER.debug("query id received for widget::" + key + " with query Id::" + CommonUtil.getLong(value));

                                QUERY_IDS.add(CommonUtil.getLong(value));

                                QUERY_CONTEXTS.put(queryId, testContext);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            LOGGER.debug("Inside SetQueryId : " + message.body());
                        }
                    });
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister();

                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testVMwareESXiVMsForecastReport(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null && message.body().getJsonObject(EVENT_CONTEXT).getString(VISUALIZATION_NAME).equalsIgnoreCase("VMware ESXi VMs - CPU Utilization Forecast Report"))
            {
                LOGGER.info(testInfo.getTestMethod().get().getName() + " - message: " + message.body());

                var context = message.body().getJsonObject(EVENT_CONTEXT);

                Assertions.assertNotNull(context.getValue(VISUALIZATION_NAME));

                Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                assertEquals("VMware ESXi VMs - CPU Utilization Forecast Report", context.getString(VISUALIZATION_NAME));

                Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_TYPE));

                Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_CATEGORY));

                var rows = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true).getJsonArray(RESULT);

                var result = rows.getJsonObject(0);

                LOGGER.info(testInfo.getTestMethod().get().getName() + " - result: " + rows.encode());

                assertEquals(2, rows.size());

                assertEquals("2", result.getString("monitor"));

                assertFalse(result.getString("esxi.vm~cpu.percent.80").isEmpty());

                assertFalse(result.getString("esxi.vm~cpu.percent.90").isEmpty());

                assertFalse(result.getString("esxi.vm~cpu.percent.100").isEmpty());

                testContext.completeNow();
            }
        });

        testCase = testInfo.getTestMethod().get().getName();

        Bootstrap.vertx().eventBus().send(UI_ACTION_VISUALIZATION_RENDER, new JsonObject().put(ReportConstants.REPORT_ID, 1000000001031049L)
                .put(ID, -1)
                .put(SESSION_ID, TestUtil.getSessionId())
                .put(USER_NAME, "admin")
                .put("test.request.batch.size", 100));

        forecastVMwareVMs = true;

        try
        {
            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EventBusConstants.EVENT_VISUALIZATION_TEST, message ->
            {
                try
                {
                    message.body().getMap().forEach((key, value) ->
                    {
                        try
                        {
                            if (value instanceof Long && -1 == CommonUtil.getLong(key))
                            {
                                LOGGER.debug("query id received for widget::" + key + " with query Id::" + CommonUtil.getLong(value));

                                QUERY_IDS.add(CommonUtil.getLong(value));

                                QUERY_CONTEXTS.put(queryId, testContext);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            LOGGER.debug("Inside SetQueryId : " + message.body());
                        }
                    });
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister();

                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testVMwareESXiVMsMultipleBatchForecastReport(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null && message.body().getJsonObject(EVENT_CONTEXT).getString(VISUALIZATION_NAME).equalsIgnoreCase("VMware ESXi VMs - CPU Utilization Forecast Report"))
            {
                LOGGER.info(testInfo.getTestMethod().get().getName() + " - message: " + message.body());

                var context = message.body().getJsonObject(EVENT_CONTEXT);

                Assertions.assertNotNull(context.getValue(VISUALIZATION_NAME));

                Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                assertEquals("VMware ESXi VMs - CPU Utilization Forecast Report", context.getString(VISUALIZATION_NAME));

                Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_TYPE));

                Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_CATEGORY));

                var rows = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true).getJsonArray(RESULT);

                var result = rows.getJsonObject(0);

                LOGGER.info(testInfo.getTestMethod().get().getName() + " - result: " + rows.encode());

                assertEquals(2, rows.size());

                assertEquals("2", result.getString("monitor"));

                assertFalse(result.getString("esxi.vm~cpu.percent.80").isEmpty());

                assertFalse(result.getString("esxi.vm~cpu.percent.90").isEmpty());

                assertFalse(result.getString("esxi.vm~cpu.percent.100").isEmpty());

                testContext.completeNow();
            }
        });

        testCase = testInfo.getTestMethod().get().getName();

        COUNTER.set(0);

        Bootstrap.vertx().eventBus().send(UI_ACTION_VISUALIZATION_RENDER, new JsonObject().put(ReportConstants.REPORT_ID, 1000000001031049L)
                .put(ID, -1)
                .put(SESSION_ID, TestUtil.getSessionId())
                .put(USER_NAME, "admin")
                .put("test.request.batch.size", 10));

        forecastVMwareVMsBatch = true;

        try
        {
            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EventBusConstants.EVENT_VISUALIZATION_TEST, message ->
            {
                try
                {
                    message.body().getMap().forEach((key, value) ->
                    {
                        try
                        {
                            if (value instanceof Long && -1 == CommonUtil.getLong(key))
                            {
                                LOGGER.debug("query id received for widget::" + key + " with query Id::" + CommonUtil.getLong(value));

                                QUERY_IDS.add(CommonUtil.getLong(value));

                                QUERY_CONTEXTS.put(queryId, testContext);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            LOGGER.debug("Inside SetQueryId : " + message.body());
                        }
                    });
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister();

                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    @Timeout(360 * 1000)
    void testRenderAvailabilityReportHavingInstanceIP(VertxTestContext testContext, TestInfo testInfo)
    {

        var reportContext = new JsonObject("{\"id\": -1,\"container.type\": \"report\",\"visualization.timeline\": {\"relative.timeline\": \"today\",\"visualization.time.range.inclusive\": \"no\"},\"visualization.category\": \"Grid\",\"visualization.type\": \"Grid\",\"visualization.data.sources\": [{\"type\": \"availability\",\"filters\": {\"data.filter\": {},\"result.filter\": {},\"drill.down.filter\": {}},\"visualization.result.by\": [\"interface\"],\"data.points\": [{\"data.point\": \"interface~uptime.percent\",\"aggregator\": \"avg\",\"entity.type\": \"Group\",\"entities\": [10000000000002]}],\"availability.by\": [{\"data.point\": \"interface\",\"aggregator\": \"avg\",\"entity.type\": \"Group\",\"entities\": [10000000000002]}],\"join.type\": \"custom\"}],\"visualization.properties\": {\"grid\": {\"visualization.grid.properties.required\": \"no\",\"searchable\": \"yes\",\"column.selection\": \"no\",\"header\": \"yes\",\"style\": {\"header.font.size\": \"small\"},\"columns\": [{\"name\": \"monitor\",\"show\": \"yes\",\"sortable\": \"yes\",\"disable\": \"no\",\"resizable\": \"yes\",\"selectable\": \"yes\",\"orderable\": \"yes\",\"position\": 0,\"style\": {}},{\"name\": \"object.type\",\"show\": \"no\",\"sortable\": \"yes\",\"disable\": \"no\",\"resizable\": \"yes\",\"selectable\": \"yes\",\"orderable\": \"yes\",\"position\": 3,\"style\": {}},{\"name\": \"object.vendor\",\"show\": \"no\",\"sortable\": \"yes\",\"disable\": \"no\",\"resizable\": \"yes\",\"selectable\": \"yes\",\"orderable\": \"yes\",\"position\": 4,\"style\": {}},{\"name\": \"interface\",\"show\": \"yes\",\"sortable\": \"yes\",\"disable\": \"no\",\"resizable\": \"yes\",\"selectable\": \"yes\",\"orderable\": \"yes\",\"position\": 5,\"style\": {}},{\"name\": \"object.ip\",\"show\": \"no\",\"sortable\": \"yes\",\"disable\": \"no\",\"resizable\": \"yes\",\"selectable\": \"yes\",\"orderable\": \"yes\",\"position\": 5,\"style\": {}},{\"name\": \"interface~uptime.percent.avg\",\"show\": \"yes\",\"sortable\": \"yes\",\"disable\": \"no\",\"resizable\": \"yes\",\"selectable\": \"yes\",\"orderable\": \"yes\",\"position\": 6,\"style\": {}}]}},\"visualization.result.by\": [\"interface\"],\"granularity\": {\"value\": 5,\"unit\": \"m\"},\"join.type\": \"custom\",\"join.result\": \"instance.ip\",\"join.columns\": [\"monitor\"],\"ui.event.uuid\": \"c22dccaa-2277-4014-8aff-2da81888c42d\",\"session-id\": \"9aed8fda-c363-4aaf-8ac8-6606af5c0f2d\",\"user.name\": \"admin\"}");

        reportContext.put(VISUALIZATION_NAME, "Availability Report With Instance IP");

        try
        {
            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
            {
                if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null && message.body().getJsonObject(EVENT_CONTEXT).getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase("Availability Report With Instance IP"))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + " - message: " + message.body());

                    var context = message.body().getJsonObject(EVENT_CONTEXT);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_NAME));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_TYPE));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_CATEGORY));

                    var rows = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true).getJsonArray(RESULT);

                    Assertions.assertFalse(rows.isEmpty());

                    var result = rows.getJsonObject(0);

                    LOGGER.info(testInfo.getTestMethod().get().getName() + " - result: " + rows.encode());

                    Assertions.assertNotNull(result);

                    Assertions.assertTrue(result.containsKey(INSTANCE_IP));

                    Assertions.assertTrue(result.containsKey(AIOpsObject.OBJECT_GROUPS));

                    testContext.completeNow();

                }
            });

            LOGGER.info("Running testcase : " + testInfo.getTestMethod().get().getName());

            Bootstrap.vertx().eventBus().send("test.visualization.render", reportContext.put(SESSION_ID, TestUtil.getSessionId()));

            availability = true;

            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EventBusConstants.EVENT_VISUALIZATION_TEST, message ->
            {
                try
                {
                    message.body().getMap().forEach((key, value) ->
                    {
                        try
                        {
                            if (value instanceof Long && -1 == CommonUtil.getLong(key))
                            {
                                LOGGER.debug("query id received for widget::" + key + " with query Id::" + CommonUtil.getLong(value));

                                QUERY_IDS.add(CommonUtil.getLong(value));

                                QUERY_CONTEXTS.put(queryId, testContext);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            LOGGER.debug("Inside SetQueryId : " + message.body());
                        }
                    });
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister();

                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            messageConsumer.unregister();

            testContext.failNow(exception);
        }

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    @Timeout(360 * 1000)
    void testMultiAvailabilityReport(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if ((message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null) && message.body().getJsonObject(EVENT_CONTEXT).getString(VISUALIZATION_NAME).equalsIgnoreCase("All Virtual Machine Availability - This Month"))
            {
                LOGGER.info(testInfo.getTestMethod().get().getName() + " - message: " + message.body());

                var context = message.body().getJsonObject(EVENT_CONTEXT);

                Assertions.assertNotNull(VISUALIZATION_NAME);

                Assertions.assertNotNull(VisualizationConstants.VISUALIZATION_DATA_SOURCES);

                assertEquals("All Virtual Machine Availability - This Month", context.getString(VISUALIZATION_NAME));

                Assertions.assertNotNull(VisualizationConstants.VISUALIZATION_TYPE);

                Assertions.assertNotNull(VisualizationConstants.VISUALIZATION_CATEGORY);

                var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true).getJsonArray(RESULT);

                LOGGER.info(testInfo.getTestMethod().get().getName() + " - result: " + result);

                for (var index = 0; index < result.size(); index++)
                {
                    var item = result.getJsonObject(index);

                    assertTrue(item.containsKey("vm.uptime.seconds.sum"));

                    assertTrue(item.containsKey("vm.uptime.percent.avg"));

                    assertTrue(item.containsKey("entity.id"));
                }

                testContext.completeNow();
            }
        });

        testCase = testInfo.getTestMethod().get().getName();

        Bootstrap.vertx().eventBus().send(UI_ACTION_VISUALIZATION_RENDER, new JsonObject().put(ReportConstants.REPORT_ID, 10000000010358L)
                .put(ID, -1)
                .put(SESSION_ID, TestUtil.getSessionId())
                .put(USER_NAME, "admin"));

        multiAvailability = true;

        try
        {
            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EventBusConstants.EVENT_VISUALIZATION_TEST, message ->
            {
                try
                {
                    message.body().getMap().forEach((key, value) ->
                    {
                        try
                        {
                            if (value instanceof Long && -1 == CommonUtil.getLong(key))
                            {
                                LOGGER.debug("query id received for widget::" + key + " with query Id::" + CommonUtil.getLong(value));

                                QUERY_IDS.add(CommonUtil.getLong(value));

                                QUERY_CONTEXTS.put(queryId, testContext);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            LOGGER.debug("Inside SetQueryId : " + message.body());
                        }
                    });
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister();

                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    @Timeout(360 * 1000)
    void testMultiTopNResponse(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if ((message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null) && message.body().getJsonObject(EVENT_CONTEXT).getString(VISUALIZATION_NAME).equalsIgnoreCase("Top 25 Wireless Monitors by CPU Utilization - Last Day"))
            {
                LOGGER.info(testInfo.getTestMethod().get().getName() + " - message: " + message.body());

                var context = message.body().getJsonObject(EVENT_CONTEXT);

                Assertions.assertNotNull(VISUALIZATION_NAME);

                Assertions.assertNotNull(VisualizationConstants.VISUALIZATION_DATA_SOURCES);

                assertEquals("Top 25 Wireless Monitors by CPU Utilization - Last Day", context.getString(VISUALIZATION_NAME));

                Assertions.assertNotNull(VisualizationConstants.VISUALIZATION_TYPE);

                Assertions.assertNotNull(VisualizationConstants.VISUALIZATION_CATEGORY);

                var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true).getJsonArray(RESULT);

                LOGGER.info(testInfo.getTestMethod().get().getName() + " - result: " + result);

                for (var index = 0; index < result.size(); index++)
                {
                    var item = result.getJsonObject(index);

                    assertTrue(item.containsKey("object.ip"));

                    assertTrue(item.containsKey("citrix.xen.vm.uptime.percent.avg"));

                    assertTrue(item.containsKey("citrix.xen.vm.uptime.percent.avg.formatted"));

                    assertTrue(item.containsKey("citrix.xen.vm.uptime.seconds.sum.formatted"));

                    assertTrue(item.containsKey("citrix.xen.vm"));

                    assertTrue(item.containsKey("citrix.xen.vm.uptime.seconds.sum"));
                }

                testContext.completeNow();
            }
        });

        testCase = testInfo.getTestMethod().get().getName();

        Bootstrap.vertx().eventBus().send(UI_ACTION_VISUALIZATION_RENDER, new JsonObject().put(ReportConstants.REPORT_ID, 10000000010292L)
                .put(ID, -1)
                .put(SESSION_ID, TestUtil.getSessionId())
                .put(USER_NAME, "admin"));

        multiTopN = true;

        try
        {
            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EventBusConstants.EVENT_VISUALIZATION_TEST, message ->
            {
                try
                {
                    message.body().getMap().forEach((key, value) ->
                    {
                        try
                        {
                            if (value instanceof Long && -1 == CommonUtil.getLong(key))
                            {
                                LOGGER.debug("query id received for widget::" + key + " with query Id::" + CommonUtil.getLong(value));

                                QUERY_IDS.add(CommonUtil.getLong(value));

                                QUERY_CONTEXTS.put(queryId, testContext);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            LOGGER.debug("Inside SetQueryId : " + message.body());
                        }
                    });
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister();

                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    public void testExecuteCustomScriptBatching(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put("report.id", 0)
                .put("report.type", "custom.script")
                .put("visualization.type", "custom.script")
                .put("report.script.type", "go")
                .put("report.script", """
                        package main
                        
                        import (
                        \t"bufio"
                        \tjson "encoding/json"
                        \t"fmt"
                        \t. "motadatasdk/motadatatypes"
                        \t"os"
                        \t"strings"
                        \t"time"
                        )
                        
                        var (
                        \tMotadataSeparator = "_|@#|_"
                        
                        \tMotadataEscapeSeparator = "_\\\\|@#\\\\|_"
                        )
                        
                        func main() {
                        
                        \tvar request = [2]string{}
                        
                        \trequest[0] = "{\\"id\\":-1,\\"container.type\\":\\"dashboard\\",\\"visualization.name\\":\\"testExecuteCustomScriptBatching\\",\\"visualization.timeline\\":{\\"relative.timeline\\":\\"today\\",\\"visualization.time.range.inclusive\\":\\"no\\"},\\"visualization.category\\":\\"Grid\\",\\"visualization.type\\":\\"Grid\\",\\"visualization.data.sources\\":[{\\"type\\":\\"metric\\",\\"filters\\":{\\"data.filter\\":{},\\"result.filter\\":{},\\"drill.down.filter\\":{}},\\"visualization.result.by\\":[\\"monitor\\"],\\"data.points\\":[{\\"data.point\\":\\"system.cpu.percent\\",\\"aggregator\\":\\"avg\\"}]}],\\"visualization.properties\\":{\\"grid\\":{\\"visualization.grid.properties.required\\":\\"no\\",\\"searchable\\":\\"yes\\",\\"column.selection\\":\\"no\\",\\"header\\":\\"yes\\",\\"style\\":{\\"header.font.size\\":\\"small\\"},\\"columns\\":[{\\"name\\":\\"monitor\\",\\"show\\":\\"yes\\",\\"sortable\\":\\"yes\\",\\"disable\\":\\"no\\",\\"resizable\\":\\"yes\\",\\"selectable\\":\\"yes\\",\\"orderable\\":\\"yes\\",\\"position\\":1,\\"style\\":{}},{\\"name\\":\\"system.cpu.percent.avg\\",\\"show\\":\\"yes\\",\\"sortable\\":\\"yes\\",\\"disable\\":\\"no\\",\\"resizable\\":\\"yes\\",\\"selectable\\":\\"yes\\",\\"orderable\\":\\"yes\\",\\"position\\":2,\\"style\\":{}},{\\"name\\":\\"object.type\\",\\"show\\":\\"no\\",\\"sortable\\":\\"yes\\",\\"disable\\":\\"no\\",\\"resizable\\":\\"yes\\",\\"selectable\\":\\"yes\\",\\"orderable\\":\\"yes\\",\\"position\\":3,\\"style\\":{}},{\\"name\\":\\"object.vendor\\",\\"show\\":\\"no\\",\\"sortable\\":\\"yes\\",\\"disable\\":\\"no\\",\\"resizable\\":\\"yes\\",\\"selectable\\":\\"yes\\",\\"orderable\\":\\"yes\\",\\"position\\":4,\\"style\\":{}},{\\"name\\":\\"object.ip\\",\\"show\\":\\"no\\",\\"sortable\\":\\"yes\\",\\"disable\\":\\"no\\",\\"resizable\\":\\"yes\\",\\"selectable\\":\\"yes\\",\\"orderable\\":\\"yes\\",\\"position\\":5,\\"style\\":{}}]}},\\"visualization.result.by\\":[\\"monitor\\"]}"
                        
                        \trequest[1] = "{\\"id\\":-1,\\"container.type\\":\\"dashboard\\",\\"visualization.name\\":\\"testExecuteCustomScriptBatching\\",\\"visualization.timeline\\":{\\"relative.timeline\\":\\"today\\",\\"visualization.time.range.inclusive\\":\\"no\\"},\\"visualization.category\\":\\"Grid\\",\\"visualization.type\\":\\"Grid\\",\\"visualization.data.sources\\":[{\\"type\\":\\"metric\\",\\"filters\\":{\\"data.filter\\":{},\\"result.filter\\":{},\\"drill.down.filter\\":{}},\\"visualization.result.by\\":[\\"monitor\\"],\\"data.points\\":[{\\"data.point\\":\\"system.cpu.percent\\",\\"aggregator\\":\\"avg\\"}]}],\\"visualization.properties\\":{\\"grid\\":{\\"visualization.grid.properties.required\\":\\"no\\",\\"searchable\\":\\"yes\\",\\"column.selection\\":\\"no\\",\\"header\\":\\"yes\\",\\"style\\":{\\"header.font.size\\":\\"small\\"},\\"columns\\":[{\\"name\\":\\"monitor\\",\\"show\\":\\"yes\\",\\"sortable\\":\\"yes\\",\\"disable\\":\\"no\\",\\"resizable\\":\\"yes\\",\\"selectable\\":\\"yes\\",\\"orderable\\":\\"yes\\",\\"position\\":1,\\"style\\":{}},{\\"name\\":\\"system.cpu.percent.avg\\",\\"show\\":\\"yes\\",\\"sortable\\":\\"yes\\",\\"disable\\":\\"no\\",\\"resizable\\":\\"yes\\",\\"selectable\\":\\"yes\\",\\"orderable\\":\\"yes\\",\\"position\\":2,\\"style\\":{}},{\\"name\\":\\"object.type\\",\\"show\\":\\"no\\",\\"sortable\\":\\"yes\\",\\"disable\\":\\"no\\",\\"resizable\\":\\"yes\\",\\"selectable\\":\\"yes\\",\\"orderable\\":\\"yes\\",\\"position\\":3,\\"style\\":{}},{\\"name\\":\\"object.vendor\\",\\"show\\":\\"no\\",\\"sortable\\":\\"yes\\",\\"disable\\":\\"no\\",\\"resizable\\":\\"yes\\",\\"selectable\\":\\"yes\\",\\"orderable\\":\\"yes\\",\\"position\\":4,\\"style\\":{}},{\\"name\\":\\"object.ip\\",\\"show\\":\\"no\\",\\"sortable\\":\\"yes\\",\\"disable\\":\\"no\\",\\"resizable\\":\\"yes\\",\\"selectable\\":\\"yes\\",\\"orderable\\":\\"yes\\",\\"position\\":5,\\"style\\":{}}]}},\\"visualization.result.by\\":[\\"monitor\\"]}"
                        
                        \targs := os.Args[1:]
                        
                        \targuments := make(MotadataMap)
                        
                        \tjson.Unmarshal([]byte(args[1]), &arguments)
                        
                        \tfor i := 0; i < 2; i++ {
                        
                        \t\tdecodedValue := make(MotadataMap)
                        
                        \t\tif i == 0 {
                        
                        \t\t\tdecodedValue["batches"] = 2
                        \t\t}
                        
                        \t\tjson.Unmarshal([]byte(request[i]), &decodedValue)
                        
                        \t\tfor key, value := range arguments {
                        
                        \t\t\tdecodedValue[key] = value
                        \t\t}
                        
                        \t\tdecodedValue["acknowledge"] = true
                        
                        \t\tfmt.Println(fmt.Sprintf("%s%s", decodedValue.ToJSON().ToString(), MotadataSeparator))
                        \t}
                        
                        \treader := bufio.NewReaderSize(os.Stdin, 204800)
                        
                        \tin := ""
                        
                        \tvar timeout = time.Second * time.Duration(120)
                        
                        \tgo func() {
                        
                        \t\t<-time.After(timeout)
                        
                        \t\tfmt.Println("Timeout reached! Exiting...")
                        
                        \t\tos.Exit(0)
                        \t}()
                        
                        \tfor {
                        
                        \t\ttime.Sleep(time.Duration(10) * time.Millisecond)
                        
                        \t\tbytes := make([]byte, 204800)
                        
                        \t\tn, _ := reader.Read(bytes)
                        
                        \t\tin = in + string(bytes[:n])
                        
                        \t\tif strings.Contains(in, MotadataSeparator) {
                        
                        \t\t\tin = strings.Split(in, MotadataSeparator)[0]
                        
                        \t\t\tfmt.Println(fmt.Sprintf("%s%s", in, MotadataSeparator))
                        
                        \t\t\tos.Exit(0)
                        \t\t}
                        \t}
                        }""")
                .put("id", -1)
                .put("ui.event.uuid", "3f44bb50-2131-46e5-9811-c2acd078d6ce")
                .put("session-id", TestUtil.getSessionId())
                .put("user.name", "admin");

        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            var event = message.body();

            try
            {
                if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && event.containsKey(EVENT_CONTEXT) && event.getJsonObject(EVENT_CONTEXT) != null && !event.getJsonObject(EVENT_CONTEXT).isEmpty() && event.getJsonObject(EVENT_CONTEXT).containsKey(VISUALIZATION_NAME) && event.getJsonObject(EVENT_CONTEXT).getString(VISUALIZATION_NAME).equalsIgnoreCase("testExecuteCustomScriptBatching"))
                {
                    var result = VisualizationConstants.unpack(Buffer.buffer(event.getJsonObject(EVENT_CONTEXT).getBinary(RESULT)), LOGGER, false, null, false, true).getJsonArray(RESULT);

                    LOGGER.info(String.format("%s event: %s", testInfo.getTestMethod().get().getName(), event));

                    LOGGER.info(String.format("%s result: %s", testInfo.getTestMethod().get().getName(), result));

                    assertFalse(result.isEmpty());

                    assertNotNull(result.getJsonObject(0));

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        try
        {
            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EventBusConstants.EVENT_VISUALIZATION_TEST, message ->
            {
                try
                {
                    message.body().getMap().forEach((key, value) ->
                    {
                        try
                        {
                            if (value instanceof Long && -1 == CommonUtil.getLong(key))
                            {
                                LOGGER.debug("query id received for widget::" + key + " with query Id::" + CommonUtil.getLong(value));

                                queryId = CommonUtil.getLong(value);

                                QUERY_CONTEXTS.put(queryId, testContext);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            LOGGER.debug("Inside SetQueryId : " + message.body());
                        }
                    });
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister();

                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        customScript = true;

        TestUtil.vertx().eventBus().send(UI_ACTION_VISUALIZATION_RENDER, context);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    @Timeout(360 * 1000)
    void testMultiAvailabilityReportFail(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if ((message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null) && message.body().getJsonObject(EVENT_CONTEXT).getString(VISUALIZATION_NAME).equalsIgnoreCase("All Virtual Machine Availability - This Month"))
            {
                LOGGER.info(testInfo.getTestMethod().get().getName() + " - message: " + message.body());

                var context = message.body().getJsonObject(EVENT_CONTEXT);

                Assertions.assertNotNull(VISUALIZATION_NAME);

                Assertions.assertNotNull(VisualizationConstants.VISUALIZATION_DATA_SOURCES);

                assertEquals("All Virtual Machine Availability - This Month", context.getString(VISUALIZATION_NAME));

                Assertions.assertNotNull(VisualizationConstants.VISUALIZATION_TYPE);

                Assertions.assertNotNull(VisualizationConstants.VISUALIZATION_CATEGORY);

                var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true).getJsonArray(RESULT);

                LOGGER.info(testInfo.getTestMethod().get().getName() + " - result: " + result);

                for (var index = 0; index < result.size(); index++)
                {
                    var item = result.getJsonObject(index);

                    assertTrue(item.containsKey("vm.uptime.seconds.sum"));

                    assertTrue(item.containsKey("vm.uptime.percent.avg"));

                    assertTrue(item.containsKey("entity.id"));
                }

                testContext.completeNow();
            }
        });

        testCase = testInfo.getTestMethod().get().getName();

        Bootstrap.vertx().eventBus().send(UI_ACTION_VISUALIZATION_RENDER, new JsonObject().put(ReportConstants.REPORT_ID, 10000000010358L)
                .put(ID, -1)
                .put(SESSION_ID, TestUtil.getSessionId())
                .put(USER_NAME, "admin"));

        multiAvailability = true;

        try
        {
            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EventBusConstants.EVENT_VISUALIZATION_TEST, message ->
            {
                try
                {
                    message.body().getMap().forEach((key, value) ->
                    {
                        try
                        {
                            if (value instanceof Long && -1 == CommonUtil.getLong(key))
                            {
                                LOGGER.debug("query id received for widget::" + key + " with query Id::" + CommonUtil.getLong(value));

                                QUERY_IDS.add(CommonUtil.getLong(value));

                                QUERY_CONTEXTS.put(queryId, testContext);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            LOGGER.debug("Inside SetQueryId : " + message.body());
                        }
                    });
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister();

                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    @Timeout(360 * 1000)
    void testGetPollingErrorReport(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
            {
                if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null && message.body().getJsonObject(EVENT_CONTEXT).getString(VISUALIZATION_NAME).equalsIgnoreCase("Unhealthy Monitors"))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + " - message: " + message.body());

                    var context = message.body().getJsonObject(EVENT_CONTEXT);

                    Assertions.assertNotNull(context.getValue(VISUALIZATION_NAME));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    assertEquals("Unhealthy Monitors", context.getString(VISUALIZATION_NAME));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_TYPE));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_CATEGORY));

                    var rows = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true).getJsonArray(RESULT);

                    Assertions.assertFalse(rows.isEmpty());

                    var result = rows.getJsonObject(0);

                    LOGGER.info(testInfo.getTestMethod().get().getName() + " - result: " + rows.encode());

                    Assertions.assertNotNull(result);

                    testContext.completeNow();
                }
            });

            LOGGER.info("Running testcase : " + testInfo.getTestMethod().get().getName());

            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_METRIC_ENRICHER,
                    new JsonObject("{\"cli.enabled\":\"no\",\"event.id\":8152074825579,\"event.timestamp\":1710313313,\"id\":8152074890900,\"metric.category\":\"Server\",\"metric.name\":\"LinuxDirectory\",\"metric.object\":8152074890896,\"metric.plugin\":\"linuxdirectory\",\"metric.state\":\"ENABLE\",\"metric.type\":\"Linux\",\"object.category\":\"Server\",\"object.creation.time.seconds\":1710312999,\"object.email.notification.recipients\":[\"<EMAIL>\"],\"object.groups\":[10000000000019,10000000000017],\"object.host\":\"motadata1098\",\"object.id\":1,\"object.ip\":\"***********\",\"object.monitor.polling.failed.notification.status\":\"yes\",\"object.monitor.polling.failed.notification.timer.seconds\":900,\"object.name\":\"motadata1098\",\"object.scheduler.status\":\"no\",\"object.sms.notification.recipients\":[\"9876543211\"],\"object.target\":\"************\",\"object.type\":\"Linux\",\"object.user.tags\":[],\"password\":\"motadata1\",\"plugin.id\":91,\"remote.address\":\"0:0:0:0:0:0:0:1\",\"result\":{},\"status\":\"fail\",\"user.name\":\"admin\",\"username\":\"motadata1\",\"error.code\":\"MD003\",\"message\":\"Metric polling failed. Possible reason : Invalid credentials\",\"event.type\":\"metric.poll\",\"copy.required\":false}")
                            .put("metric.object", CommonUtil.getLong(ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemByIP("***********")).getString(ID))));

            TestUtil.vertx().setTimer(5000L, timer ->
                    Bootstrap.vertx().eventBus().send(UI_ACTION_VISUALIZATION_RENDER, new JsonObject().put(ReportConstants.REPORT_ID, 1000000001031036L)
                            .put(ID, -1)
                            .put(SESSION_ID, TestUtil.getSessionId())
                            .put(USER_NAME, "admin")
                            .put("test.request.batch.size", 100))
            );

        }
        catch (Exception exception)
        {
            messageConsumer.unregister();

            testContext.failNow(exception);
        }

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    @Timeout(360 * 1000)
    void testRenderInventoryReportHavingTagOrGroup(VertxTestContext testContext, TestInfo testInfo)
    {

        var reportContext = new JsonObject("{\"id\": -1,\"visualization.name\": \"Inventory Report Test\",\"container.type\": \"report\",\"visualization.timeline\": {\"relative.timeline\": \"today\",\"visualization.time.range.inclusive\": \"no\"},\"visualization.category\": \"Active Alerts\",\"visualization.type\": \"Grid\",\"visualization.data.sources\": [{\"type\": \"availability\",\"status\": [],\"filters\": {\"data.filter\": {},\"result.filter\": {},\"drill.down.filter\": {}},\"data.points\": [{\"data.point\": \"monitor\",\"aggregator\": \"avg\",\"entity.type\": \"Group\",\"entities\": [10000000000017]}],\"join.type\": \"custom\"}],\"visualization.properties\": {\"grid\": {\"visualization.grid.properties.required\": \"no\",\"searchable\": \"yes\",\"column.selection\": \"no\",\"header\": \"yes\",\"style\": {\"header.font.size\": \"small\"},\"columns\": [{\"name\": \"monitor\",\"show\": \"yes\",\"sortable\": \"no\",\"disable\": \"no\",\"resizable\": \"yes\",\"selectable\": \"yes\",\"orderable\": \"yes\",\"position\": 1,\"style\": {}},{\"name\": \"object.type\",\"show\": \"no\",\"sortable\": \"no\",\"disable\": \"no\",\"resizable\": \"yes\",\"selectable\": \"yes\",\"orderable\": \"yes\",\"position\": 2,\"style\": {}},{\"name\": \"object.groups\",\"show\": \"yes\",\"sortable\": \"yes\",\"disable\": \"no\",\"resizable\": \"yes\",\"selectable\": \"yes\",\"orderable\": \"yes\",\"position\": 4,\"style\": {}},{\"name\": \"status\",\"show\": \"yes\",\"sortable\": \"yes\",\"disable\": \"no\",\"resizable\": \"yes\",\"selectable\": \"yes\",\"orderable\": \"yes\",\"position\": 5,\"style\": {}},{\"name\": \"timestamp\",\"show\": \"yes\",\"sortable\": \"yes\",\"disable\": \"no\",\"resizable\": \"yes\",\"selectable\": \"yes\",\"orderable\": \"yes\",\"position\": 6,\"style\": {}},{\"name\": \"duration\",\"show\": \"no\",\"sortable\": \"no\",\"disable\": \"no\",\"resizable\": \"yes\",\"selectable\": \"yes\",\"orderable\": \"yes\",\"position\": 8,\"style\": {}},{\"name\": \"vendor\",\"show\": \"yes\",\"sortable\": \"yes\",\"disable\": \"no\",\"resizable\": \"yes\",\"selectable\": \"yes\",\"orderable\": \"yes\",\"position\": 8,\"style\": {}},{\"name\": \"version\",\"show\": \"yes\",\"sortable\": \"yes\",\"disable\": \"no\",\"resizable\": \"yes\",\"selectable\": \"yes\",\"orderable\": \"yes\",\"position\": 10,\"style\": {}},{\"name\": \"type\",\"show\": \"yes\",\"sortable\": \"yes\",\"disable\": \"no\",\"resizable\": \"yes\",\"selectable\": \"yes\",\"orderable\": \"yes\",\"position\": 11,\"style\": {}},{\"name\": \"object.vendor\",\"show\": \"no\",\"sortable\": \"no\",\"disable\": \"no\",\"resizable\": \"yes\",\"selectable\": \"yes\",\"orderable\": \"yes\",\"position\": 14,\"style\": {}},{\"name\": \"object.ip\",\"show\": \"no\",\"sortable\": \"no\",\"disable\": \"no\",\"resizable\": \"yes\",\"selectable\": \"yes\",\"orderable\": \"yes\",\"position\": 15,\"style\": {}}]}},\"visualization.result.by\": [],\"granularity\": {\"value\": 5,\"unit\": \"m\"},\"visualization.tags\": [\"vendor\",\"version\",\"type\"],\"join.type\": \"custom\",\"join.result\": \"tag\",\"join.columns\": [\"monitor\"],\"ui.event.uuid\": \"5d5a318d-4712-46b5-a3d7-9a738ae3f102\",\"session-id\": \"bbc08feb-1c85-4b44-80d3-3ea9b5c1d422\",\"user.name\": \"admin\",\"max.records\": 1000 }");

        reportContext.put("visualization.tags", new JsonArray().add("vendor"));

        try
        {
            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
            {
                if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null && message.body().getJsonObject(EVENT_CONTEXT).getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase("Inventory Report Test"))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + " - message: " + message.body());

                    var context = message.body().getJsonObject(EVENT_CONTEXT);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_NAME));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_TYPE));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_CATEGORY));

                    var rows = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true).getJsonArray(RESULT);

                    Assertions.assertFalse(rows.isEmpty());

                    var result = rows.getJsonObject(0);

                    LOGGER.info(testInfo.getTestMethod().get().getName() + " - result: " + rows.encode());

                    Assertions.assertNotNull(result);

                    if (result.containsKey(AIOpsObject.OBJECT_GROUPS) && result.containsKey("vendor"))
                    {
                        testContext.completeNow();
                    }

                }
            });

            LOGGER.info("Running testcase : " + testInfo.getTestMethod().get().getName());

            Bootstrap.vertx().eventBus().send("test.visualization.render", reportContext.put(SESSION_ID, TestUtil.getSessionId()));
        }
        catch (Exception exception)
        {
            messageConsumer.unregister();

            testContext.failNow(exception);
        }

    }
}
