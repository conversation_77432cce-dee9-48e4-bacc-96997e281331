/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.TemplateConfigStore;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.VISUALIZATION_TEMPLATE_API_ENDPOINT;
import static com.mindarray.api.Template.TEMPLATE_NAME;
import static com.mindarray.api.Template.TEMPLATE_PARENT_ID;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.eventbus.EventBusConstants.ChangeNotificationType.ADD_EVENT_SOURCE;
import static com.mindarray.eventbus.EventBusConstants.ChangeNotificationType.DELETE_EVENT_SOURCE;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.*;

@Timeout(60 * 1000)
@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestTemplate
{

    private static final Logger LOGGER = new Logger(TestTemplate.class, MOTADATA_API, "Test Template");
    private static long templateId;
    private static long entityID;

    private static final String VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT = "/api/v1/visualization/templates/resolve";
    private static final String VISUALIZATION_TEMPLATE_GET_API_ENDPOINT = "/api/v1/visualization/templates/";


    @BeforeAll
    static void beforeAll(VertxTestContext testContext) throws InterruptedException
    {
        var item = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemsByCategory(NMSConstants.Category.NETWORK).getLong(0));

        entityID = item.getLong(ID);

        LOGGER.info("Going to add event sources for IP :: " + item.getString(AIOpsObject.OBJECT_IP) + " | item :: " + item.encode());

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, item.getString(AIOpsObject.OBJECT_IP)).put(EventBusConstants.EVENT, EVENT_LOG).put(GlobalConstants.PLUGIN_ID, 500005).put(LogEngineConstants.EVENT_CATEGORY, "Other").put(CHANGE_NOTIFICATION_TYPE, ADD_EVENT_SOURCE));

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, item.getString(AIOpsObject.OBJECT_IP)).put(EventBusConstants.EVENT, EVENT_FLOW).put(GlobalConstants.PLUGIN_ID, DatastoreConstants.PluginId.FLOW_EVENT.getName()).put(CHANGE_NOTIFICATION_TYPE, ADD_EVENT_SOURCE));

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, item.getString(AIOpsObject.OBJECT_IP)).put(EventBusConstants.EVENT, EVENT_TRAP).put(GlobalConstants.PLUGIN_ID, DatastoreConstants.PluginId.TRAP_EVENT.getName()).put(CHANGE_NOTIFICATION_TYPE, ADD_EVENT_SOURCE));

        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        testContext.completeNow();
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext) throws InterruptedException
    {
        var item = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemsByCategory(NMSConstants.Category.NETWORK).getLong(0));

        entityID = item.getLong(ID);

        LOGGER.info("Going to remove event sources for IP :: " + item.getString(AIOpsObject.OBJECT_IP) + " | item :: " + item.encode());

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, item.getString(AIOpsObject.OBJECT_IP)).put(EventBusConstants.EVENT, EVENT_LOG).put(CHANGE_NOTIFICATION_TYPE, DELETE_EVENT_SOURCE));

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, item.getString(AIOpsObject.OBJECT_IP)).put(EventBusConstants.EVENT, EVENT_FLOW).put(CHANGE_NOTIFICATION_TYPE, DELETE_EVENT_SOURCE));

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, item.getString(AIOpsObject.OBJECT_IP)).put(EventBusConstants.EVENT, EVENT_TRAP).put(CHANGE_NOTIFICATION_TYPE, DELETE_EVENT_SOURCE));

        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        testContext.completeNow();
    }


    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testTemplateServerCategory(VertxTestContext testContext, TestInfo testInfo)
    {
        var items = ObjectConfigStore.getStore().getItemsByCategory(NMSConstants.Category.SERVER);

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Server\"}");

        assertFalse(items.isEmpty());

        context.put("entity.id", items.getValue(0));

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testTemplateNetworkCategory(VertxTestContext testContext, TestInfo testInfo)
    {
        var items = ObjectConfigStore.getStore().getItemsByCategory(NMSConstants.Category.NETWORK);

        assertFalse(items.isEmpty());

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Network\"}");

        context.put("entity.id", items.getValue(0));

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testTemplateVirtualizationCategory(VertxTestContext testContext, TestInfo testInfo)
    {
        var items = ObjectConfigStore.getStore().getItemsByCategory(NMSConstants.Category.VIRTUALIZATION);

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Virtualization\"}");

        assertFalse(items.isEmpty());

        context.put("entity.id", items.getValue(0));

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testTemplateOtherCategory(VertxTestContext testContext, TestInfo testInfo)
    {
        var items = ObjectConfigStore.getStore().getItemsByCategory(NMSConstants.Category.OTHER);

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Other\"}");

        assertFalse(items.isEmpty());

        context.put("entity.id", items.getValue(0));

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testTemplateArubaWireless(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("*************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Network\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testTemplateRuckusWireless(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("**********");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Network\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testTemplateApplication(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Server\"}");

        context.put("entity.id", entityId).put("application", VisualizationConstants.Template.ACTIVE_DIRECTORY);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testTemplateTemplateUnKnownCategory(VertxTestContext testContext)
    {
        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"UnKnown\"}").put("entity.id", 0);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            assertEquals(200, SC_OK);

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testGetTemplate(VertxTestContext testContext)
    {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_API_ENDPOINT + "/" + 10000000000001L,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            var result = response.bodyAsJsonObject().getJsonObject(RESULT);

                            assertNotNull(result);

                            Assertions.assertFalse(result.isEmpty());

                            testContext.completeNow();

                        })));

    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testCreateCustomTemplate(VertxTestContext testContext)
    {

        var payload = new JsonObject().put(TEMPLATE_PARENT_ID,10000000000002L).put(TEMPLATE_NAME,"custom template for test");

        TestAPIUtil.post(VISUALIZATION_TEMPLATE_API_ENDPOINT , payload,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            var id = response.bodyAsJsonObject().getLong(ID);

                            var result = TemplateConfigStore.getStore().getItem(id);

                            assertNotNull(result);

                            Assertions.assertFalse(result.isEmpty());

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testRenderCloudType(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Cloud\"}");

        context.put("entity.id", ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.CLOUD.getName()).getLong(ID));

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            assertNotNull(response.bodyAsJsonObject().getJsonObject(RESULT));

                            testContext.completeNow();

                        })));

    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testRenderNetworkTemplateCustomMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Network\"}");

        context.put("entity.id", ObjectConfigStore.getStore().getItemByIP("***********"));

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            assertNotNull(response.bodyAsJsonObject().getJsonObject(RESULT));

                            testContext.completeNow();

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testTemplateHPEILO(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("*************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Network\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testTemplateDellIDRAC(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("*************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Network\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testTemplateIBMTapeLibrary(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("*************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Network\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testTemplateNutanix(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("*************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"HCI\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testTemplatePrism(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("*************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"HCI\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testTemplateCiscovManage(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("***********");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"SDN\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testTemplateCiscovEdge(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("**********");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"SDN\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testTemplateCiscovSmart(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("*********");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"SDN\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testTemplateCiscovBond(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("*********");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"SDN\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testTemplateCiscoMeraki(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("n149.meraki.com");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"SDN\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testTemplateCiscoMerakiSecurity(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"SDN\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testTemplateCiscoMerakiSwitch(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"SDN\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testTemplateCiscoMerakiRadio(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"SDN\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testRenderLogTemplate(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("Running " + testInfo.getTestMethod().get().getName());

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Virtualization\"}").put(AIOpsConstants.ENTITY_ID, entityID);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var result = response.bodyAsJsonObject().getJsonObject(RESULT);

                            LOGGER.info("Template API Response of :: " + testInfo.getTestMethod().get().getName() + " :: " + result.encode());

                            assertTrue(result.containsKey(Template.TEMPLATE_TABS));

                            var items = result.getJsonArray(Template.TEMPLATE_TABS);

                            for (var i = 0; i <= items.size(); i++)
                            {
                                var item = items.getJsonObject(i);

                                if (item.getString("text").equalsIgnoreCase(VisualizationConstants.Template.LOG_ANALYTICS.getName()))
                                {
                                    testContext.completeNow();

                                    break;
                                }
                            }

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testRenderTrapTemplate(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("Running -> " + testInfo.getTestMethod().get().getName());

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Network\"}").put(AIOpsConstants.ENTITY_ID, entityID);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var result = response.bodyAsJsonObject().getJsonObject(RESULT);

                            LOGGER.info("Template API Response of :: " + testInfo.getTestMethod().get().getName() + " :: " + result.encode());

                            assertTrue(result.containsKey(Template.TEMPLATE_TABS));

                            assertFalse(result.getJsonArray(Template.TEMPLATE_TABS).isEmpty());

                            var items = result.getJsonArray(Template.TEMPLATE_TABS);

                            for (var i = 0; i <= items.size(); i++)
                            {
                                var item = items.getJsonObject(i);

                                if (item.getString("text").equalsIgnoreCase(VisualizationConstants.Template.TRAP_ANALYTICS.getName()))
                                {
                                    testContext.completeNow();

                                    break;
                                }
                            }

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testTemplateNetAppONTAPCluster(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Storage\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testTemplateHPEStoreOnce(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("************");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Storage\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    void testTemplateCiscoACI(VertxTestContext testContext, TestInfo testInfo)
    {
        var entityId = ObjectConfigStore.getStore().getItemByIP("***********");

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"SDN\"}").put("entity.id", entityId);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(32)
    void testRenderFlowTemplate(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("Running -> " + testInfo.getTestMethod().get().getName());

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Network\"}").put(AIOpsConstants.ENTITY_ID, entityID);

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var result = response.bodyAsJsonObject().getJsonObject(RESULT);

                            LOGGER.info("Template API Response of :: " + testInfo.getTestMethod().get().getName() + " :: " + result.encode());

                            assertTrue(result.containsKey(Template.TEMPLATE_TABS));

                            for (var index = 0; index <= result.getJsonArray(Template.TEMPLATE_TABS).size(); index++)
                            {
                                var tab = result.getJsonArray(Template.TEMPLATE_TABS).getJsonObject(index);

                                if (tab.getString(Template.TEMPLATE_TEXT).equalsIgnoreCase(VisualizationConstants.Template.FLOW_ANALYTICS.getName()))
                                {
                                    assertEquals(10, tab.getJsonArray(Template.TEMPLATE_TABS).size());  // check for the all nested tabs of FLOW_ANALYTICS

                                    testContext.completeNow();

                                    break;
                                }
                            }
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(33)
    void testRenderApplicationTemplate(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("Running -> " + testInfo.getTestMethod().get().getName());

        var item = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemByIP("************"));

        assertFalse(item.isEmpty());

        var context = new JsonObject("{\"entity.type\":\"Monitor\",\"category\":\"Server\"}");

        context.put("entity.id", item.getLong(ID));

        TestAPIUtil.get(VISUALIZATION_TEMPLATE_RESOLVE_API_ENDPOINT + "?filter=" + context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var result = response.bodyAsJsonObject().getJsonObject(RESULT);

                            LOGGER.info("Template API Response of :: " + testInfo.getTestMethod().get().getName() + " :: " + result.encode());

                            assertTrue(result.containsKey(Template.TEMPLATE_TABS));

                            for (var index = 0; index <= result.getJsonArray(Template.TEMPLATE_TABS).size(); index++)
                            {
                                var tab = result.getJsonArray(Template.TEMPLATE_TABS).getJsonObject(index);

                                if (tab.getString(Template.TEMPLATE_TEXT).equalsIgnoreCase(NMSConstants.Type.ACTIVE_DIRECTORY.getName()))
                                {
                                    testContext.completeNow();

                                    break;
                                }
                            }
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(39)
    void testTemplateApacheTomcat(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000002L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Apache Tomcat", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(40)
    void testTemplateAutoScaling(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000003L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Auto Scaling", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(41)
    void testTemplateCloudFront(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000004L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Cloud Front", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(42)
    void testTemplateDocumentdb(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000005L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("DocumentDB", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(43)
    void testTemplateDynamodb(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000006L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("DynamoDB", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(44)
    void testTemplateEbs(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000007L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("EBS", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(45)
    void testTemplateEc2(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000008L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("EC2", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(46)
    void testTemplateElasticBeanstalk(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000009L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Elastic Beanstalk", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(47)
    void testTemplateLambda(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000010L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Lambda", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(48)
    void testTemplateRds(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000011L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("RDS", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(49)
    void testTemplateS3(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000012L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("S3", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(50)
    void testTemplateSns(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000013L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("SNS", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(51)
    void testTemplateSqs(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000014L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("SQS", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(52)
    void testTemplateApplicationGateway(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000015L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Application Gateway", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(53)
    void testTemplateAzureCdn(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000016L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Azure CDN", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(54)
    void testTemplateCosmosdb(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000017L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("CosmosDB", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(55)
    void testTemplateAzureFunction(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000018L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Azure Function", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(56)
    void testTemplateAzureLoadBalancer(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000019L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Azure Load Balancer", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(57)
    void testTemplateMssql(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000020L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("MSSQL", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(58)
    void testTemplateScaleset(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000021L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Scaleset", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(59)
    void testTemplateServiceBus(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000022L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Service Bus", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(60)
    void testTemplateVm(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000023L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("VM", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(61)
    void testTemplateWebappService(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000024L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("WebApp Service", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(62)
    void testTemplateBind9(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000025L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Bind9", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(63)
    void testTemplateHaproxy(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000026L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("HAProxy", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(64)
    void testTemplateIbmDb2(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000029L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("IBM DB2", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(65)
    void testTemplateIbmMq(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000034L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("IBM MQ", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(66)
    void testTemplateWebsphere(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000035L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("websphere", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(67)
    void testTemplateLightHttpd(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000036L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Light Httpd", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(68)
    void testTemplateLinuxDhcp(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000037L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Linux Dhcp", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(69)
    void testTemplateMicrosoftIis(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000038L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Microsoft IIS", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(70)
    void testTemplateMsmq(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000039L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("MSMQ", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(71)
    void testTemplateSqlServer(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000040L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("SQL Server", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(72)
    void testTemplateMysqlOverview(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000047L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("MySQL Overview", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(73)
    void testTemplateNginx(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000050L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Nginx", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(74)
    void testTemplateOffice365(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000051L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Office 365", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(75)
    void testTemplateExchange(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000052L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Exchange", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(76)
    void testTemplateOnedrive(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000053L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("OneDrive", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(77)
    void testTemplateSharepoint(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000054L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Sharepoint", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(78)
    void testTemplateTeams(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000055L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Teams", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(79)
    void testTemplateOracleTemplate(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000056L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Oracle template", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(80)
    void testTemplateOverview(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000065L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Overview", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(81)
    void testTemplateRabbitmq(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000069L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("RabbitMQ", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(82)
    void testTemplateSapHana(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000070L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("SAP HANA", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(83)
    void testTemplateSybase(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000074L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Sybase", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(84)
    void testTemplateVcenter(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000075L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("VCenter", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(85)
    void testTemplateEsxi(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000076L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("ESXI", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(86)
    void testTemplateHyperv(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000077L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("HyperV", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(87)
    void testTemplateHypervCluster(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000078L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("HyperV Cluster", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(88)
    void testTemplateCitrixXen(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000079L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Citrix Xen", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(89)
    void testTemplateCitricXenCluster(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000080L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Citric Xen Cluster", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(90)
    void testTemplateCitrixXenserverVm(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000081L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Citrix XenServer VM", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(91)
    void testTemplateEsxiVm(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000082L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("ESXI VM", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(92)
    void testTemplateHypervVm(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000083L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("HyperV VM", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(93)
    void testTemplateWildfly(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000084L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Wildfly", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(94)
    void testTemplateWindowsCluster(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000085L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Windows Cluster", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(95)
    void testTemplateWindowsDhcp(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000086L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Windows DHCP", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(96)
    void testTemplateWindowsDns(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000087L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Windows DNS", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(97)
    void testTemplateWindowsRdp(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000088L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Windows RDP", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(98)
    void testTemplateCiscoWireless(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000089L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Cisco Wireless", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(99)
    void testGetTemplateRuckusWireless(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000092L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Ruckus Wireless", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(100)
    void testGetTemplateArubaWireless(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000095L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Aruba Wireless", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(101)
    void testTemplateApache(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000098L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Apache", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(102)
    void testTemplateHpux(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000101L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("HP-UX", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(103)
    void testTemplateIbmAix(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000102L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("IBM AIX", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(104)
    void testTemplateSolaris(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000103L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Solaris", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(105)
    void testTemplateUrlTemplate(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000104L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("URL Template", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(106)
    void testTemplateDomainTemplate(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000105L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Domain Template", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(107)
    void testTemplatePortTemplate(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000106L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Port Template", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(108)
    void testTemplatePingTemplate(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000107L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Ping Template", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(109)
    void testTemplateFtpTemplate(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000108L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("FTP Template", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(110)
    void testTemplateDnsTemplate(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000109L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("DNS Template", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(111)
    void testTemplateRadiusTemplate(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000110L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Radius Template", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(112)
    void testTemplateNtpTemplate(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000111L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("NTP Template", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(113)
    void testTemplateEmailTemplate(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000112L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Email Template", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(114)
    void testTemplateSslCertificate(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000113L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("SSL Certificate", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(115)
    void testTemplateLinuxServer(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000115L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Linux Server", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(116)
    void testTemplateCiscoSwitchL2Device(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000116L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Cisco Switch L2 Device", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(117)
    void testTemplateJuniperSwitchL2Device(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000117L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Juniper Switch L2 Device", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(118)
    void testTemplateJuniperSwitchL3Device(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000118L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Juniper Switch L3 Device", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(119)
    void testTemplateCiscoSwitchL3Device(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000119L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Cisco Switch L3 Device", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(120)
    void testTemplateCiscoSystemsRouterDevice(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000120L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Cisco Systems Router Device", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(121)
    void testTemplateCiscoFirewall(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000121L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Cisco Firewall", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(122)
    void testTemplatePaloAltoFirewall(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000122L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Palo Alto Firewall", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(123)
    void testTemplateCheckpointFirewall(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000123L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("CheckPoint Firewall", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(124)
    void testTemplateFortinetFirewall(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000124L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Fortinet Firewall", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(125)
    void testTemplateSnmpDevice(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000155L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("SNMP Device", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(126)
    void testTemplateAwsNetworkElb(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000156L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("AWS Network ELB", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(127)
    void testTemplateAwsApplicationElb(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000157L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("AWS Application ELB", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(128)
    void testTemplateAwsClassicElb(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000158L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("AWS Classic ELB", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(129)
    void testTemplateAzureStorage(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000159L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Azure Storage", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(130)
    void testTemplateSymantecMessagingGateway(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000164L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Symantec Messaging Gateway", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(131)
    void testTemplateEdgeTransportRole(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000166L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Edge Transport Role", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(132)
    void testTemplateMailboxRole(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000167L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Mailbox Role", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(133)
    void testTemplateSapMaxdb(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000169L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("SAP MAXDB", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(134)
    void testTemplateMariadb(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000170L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("MariaDB", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(135)
    void testTemplateLog(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000173L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Log", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(136)
    void testTemplateApachemq(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000174L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("ApacheMQ", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(137)
    void testTemplateDotnet(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000175L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Dotnet", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(138)
    void testTemplateCiscoUcs(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000177L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Cisco UCS", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(140)
    void testTemplateAristaSwitch(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000179L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Arista Switch", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(141)
    void testTemplateArubaSwitch(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000181L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Aruba Switch", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(142)
    void testTemplateH3cSwitch(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000183L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("H3C Switch ", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(143)
    void testTemplateHpSwitch(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000185L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("HP Switch ", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(144)
    void testTemplateHuaweiSwitch(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000188L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Huawei Switch ", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(145)
    void testTemplateIntelSwitch(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000190L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Intel Switch ", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(146)
    void testTemplateNetgearSwitch(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000192L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("NetGear Switch ", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(147)
    void testTemplateAzurePostgresql(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000194L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Azure PostgreSQL", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(148)
    void testTemplateAzureMysql(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000195L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Azure MySQL", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(149)
    void testTemplateCiscoStacked(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000196L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Cisco Stacked", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(150)
    void testTemplateClientAccessRole(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000202L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Client Access Role", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(151)
    void testTemplateRadwareLoadBalancer(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000203L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Radware Load Balancer", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(152)
    void testTemplateCiscoWirelessAccessPoint(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000205L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Cisco Wireless Access Point", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(153)
    void testTemplateArubaWirelessAccessPoint(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000206L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Aruba Wireless Access Point", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(154)
    void testTemplateRuckusWirelessAccessPoint(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000207L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Ruckus Wireless Access Point", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(155)
    void testTemplateSonicwallFirewall(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000208L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Sonicwall Firewall", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(156)
    void testTemplateExtremeNetworksSwitchL2Device(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000211L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Extreme Networks Switch L2 Device", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(157)
    void testTemplateExtremeNetworksSwitchL3Device(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000214L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Extreme Networks Switch L3 Device", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(158)
    void testTemplateExtremeNetworksRouterDevice(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000218L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Extreme Networks Router Device", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(159)
    void testTemplateJuniperNetworksRouter(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000221L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Juniper Networks Router", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(160)
    void testTemplateCiscoStackedL3(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000224L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Cisco Stacked L3", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(161)
    void testTemplateJuniperNetworksRouterL3(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000230L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Juniper Networks Router L3", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(162)
    void testTemplateExtremeNetworksRouterL3(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000233L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Extreme Networks Router L3", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(163)
    void testTemplateCiscoSystemsRouterL3(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000236L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Cisco Systems Router L3", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(164)
    void testTemplateAristaSwitchL3(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000239L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Arista Switch L3", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(165)
    void testTemplateH3cSwitchL3(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000242L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("H3C Switch L3", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(166)
    void testTemplateArubaSwitchL3(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000245L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Aruba Switch L3", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(167)
    void testTemplateHpSwitchL3(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000248L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("HP Switch L3", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(168)
    void testTemplateHuaweiSwitchL3(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000252L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Huawei Switch L3", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(169)
    void testTemplateIntelSwitchL3(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000255L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Intel Switch L3", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(170)
    void testTemplateNetgearSwitchL3(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000258L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("NetGear Switch L3", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(171)
    void testTemplateNetscalerLoadBalancerOverview(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000261L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Netscaler Load Balancer Overview", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(172)
    void testTemplateF5LoadBalancerOverview(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000262L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("F5 Load Balancer Overview", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(173)
    void testTemplateDlinkSwitchL2Device(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000263L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("DLink Switch L2 Device", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(174)
    void testTemplateDlinkSwitchL3Device(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000265L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("DLink Switch L3 Device", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(175)
    void testTemplateIbmTapeLibrary(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000286L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("IBM Tape Library", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(176)
    void testTemplateIronportMessagingGateway(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000287L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("IronPort Messaging Gateway", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(177)
    void testTemplateHpeIlo(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000289L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("HPE iLO", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(178)
    void testTemplateDellIdrac(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000290L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Dell iDRAC", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(179)
    void testGetTemplatePrism(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000295L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Prism", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(180)
    void testGetTemplateNutanix(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000298L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Nutanix", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(181)
    void testTemplateNutanixVm(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000301L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Nutanix vm", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(182)
    void testTemplateCiscoSdwanOverview(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000302L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Cisco SD-WAN Overview", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(183)
    void testTemplateCiscoSdwanController(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000305L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Cisco SD-WAN Controller", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(184)
    void testTemplateCiscoSdwanValidator(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000306L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Cisco SD-WAN Validator", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(185)
    void testTemplateCiscoSdwanEdge(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000307L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Cisco SD-WAN Edge", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(186)
    void testTemplateIbmAix_2(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000311L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("IBM AIX", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(187)
    void testGetTemplateCiscoMerakiSecurity(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000315L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Cisco Meraki Security", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(188)
    void testGetTemplateCiscoMerakiSwitch(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000316L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Cisco Meraki Switch", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(189)
    void testGetTemplateCiscoMerakiRadio(VertxTestContext testContext, TestInfo testInfo) {
        TestAPIUtil.get(VISUALIZATION_TEMPLATE_GET_API_ENDPOINT + 10000000000319L,
                testContext.succeeding(response -> testContext.verify(() -> {
                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER,
                            testInfo.getTestMethod().get().getName());

                     var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                    assertEquals("Cisco Meraki Radio", body.getString(TEMPLATE_NAME));

                    testContext.completeNow();
                })));
    }




}
